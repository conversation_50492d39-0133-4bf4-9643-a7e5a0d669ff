/**
 * Custom styles for Summernote editor
 */

/* Adjust toolbar button spacing */
.note-toolbar .note-btn-group {
    margin: 0 1px;
}

/* Make toolbar buttons more compact */
.note-toolbar .note-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Ensure toolbar buttons are properly aligned */
.note-toolbar .note-btn i {
    vertical-align: middle;
}

/* Style the clear formatting button */
.note-btn[data-original-title="Remove Font Style"] {
    color: #dc3545;
}

/* Adjust dropdown menus */
.note-dropdown-menu {
    min-width: 120px;
}

/* Ensure proper spacing in font size dropdown */
.note-fontsize .note-dropdown-menu {
    min-width: 80px;
}

/* Ensure proper spacing in font family dropdown */
.note-fontname .note-dropdown-menu {
    min-width: 160px;
}

/* Adjust color palette */
.note-color .note-dropdown-menu {
    min-width: 180px;
}

/* Ensure proper spacing for paragraph alignment buttons */
.note-para .note-dropdown-menu {
    min-width: 120px;
}

/* Custom loading indicator for image uploads */
.summernote-loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 9999;
}

/* Word counter styling */
.word-counter {
    margin-top: 5px;
    font-size: 0.8rem;
    color: #6c757d;
}

.word-counter.text-warning {
    color: #ffc107 !important;
}

.word-counter.text-danger {
    color: #dc3545 !important;
}
