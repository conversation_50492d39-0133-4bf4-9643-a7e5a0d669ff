<!-- Latest Announcements -->
<div class="card border-0 shadow-sm mb-4 fade-in">
    <div class="card-header bg-warning text-white">
        <h5 class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Pengumuman Terbaru' : 'Latest Announcements'); ?></h5>
    </div>
    <div class="card-body">
        <?php $__empty_1 = true; $__currentLoopData = $latestAnnouncements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="related-item mb-3 pb-3 <?php echo e(!$loop->last ? 'border-bottom' : ''); ?>">
                <div class="small text-danger mb-1">
                    <i class="far fa-calendar-alt me-1"></i>
                    <?php echo e($announcement->start_date->format('d M Y')); ?> - <?php echo e($announcement->end_date->format('d M Y')); ?>

                </div>
                <h6 class="mb-0">
                    <a href="<?php echo e(route('announcements.show', $announcement->id)); ?>" class="text-decoration-none">
                        <?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($announcement->title, 70) : \Illuminate\Support\Str::limit($announcement->title_en, 70)); ?>

                    </a>
                </h6>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada pengumuman terbaru.' : 'No latest announcements.'); ?></p>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH /home/<USER>/laravel/resources/views/public/partials/latest-announcements.blade.php ENDPATH**/ ?>