<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Announcements List -->
    <section class="section-padding">
        <div class="container">
            <div class="row justify-content-center">
                <?php $__empty_1 = true; $__currentLoopData = $announcements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-lg-6 mb-4 fade-in">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <div class="announcement-date mb-2">
                                    <i class="far fa-calendar-alt me-1"></i>
                                    <?php echo e($announcement->start_date->format('d M Y')); ?> - <?php echo e($announcement->end_date->format('d M Y')); ?>

                                </div>
                                <h4 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en); ?></h4>
                                <p class="card-text">
                                    <?php
                                        $content = app()->getLocale() == 'id' ? $announcement->content : $announcement->content_en;
                                        // Proses HTML entity dengan benar
                                        $content = html_entity_decode($content);
                                        // Hapus tag HTML
                                        $content = strip_tags($content);
                                        // Hapus whitespace berlebih
                                        $content = preg_replace('/\s+/', ' ', $content);
                                        $content = trim($content);
                                        // Batasi panjang teks
                                        $content = \Illuminate\Support\Str::limit($content, 200);
                                    ?>
                                    <?php echo e($content); ?>

                                </p>
                                <a href="<?php echo e(route('announcements.show', $announcement->id)); ?>" class="btn btn-sm btn-outline-success"><?php echo e(app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More'); ?></a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <?php echo e(app()->getLocale() == 'id' ? 'Belum ada pengumuman yang tersedia.' : 'No announcements available yet.'); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($announcements->links()); ?>

            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/announcements/index.blade.php ENDPATH**/ ?>