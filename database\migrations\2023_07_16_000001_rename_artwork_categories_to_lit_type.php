<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename artwork_categories table to lit_type
        Schema::rename('artwork_categories', 'lit_type');

        // Remove category_id from literature table and rely on existing type column
        Schema::table('literature', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn('category_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add category_id back to literature table
        Schema::table('literature', function (Blueprint $table) {
            $table->foreignId('category_id')->nullable();
            $table->foreign('category_id')->references('id')->on('lit_type')->onDelete('set null');
        });

        // Rename lit_type table back to artwork_categories
        Schema::rename('lit_type', 'artwork_categories');
    }
};
