# Image Handling Refactoring

This document explains the refactoring of image handling in the application.

## Overview

The image handling code has been refactored to improve maintainability, reduce code duplication, and enhance error handling. The refactoring includes:

1. Enhanced `ImageService` class with better error handling and support for more image formats
2. New `ImageUploadable` trait for models that have image fields
3. Base `BaseModel` class that includes common scopes and the `ImageUploadable` trait
4. Form request classes for validation of image uploads

## Refactored Models

The following models have been refactored to use the new image handling system:

1. **News** - Handles `image` and `thumbnail` fields
2. **Announcement** - Handles `image` field

## Components

### ImageService

The `ImageService` class has been enhanced with:

- Better error handling with try-catch blocks and logging
- Support for WebP images
- Sanitization of filenames
- Separate methods for processing different image types
- New methods for creating thumbnails and getting image URLs

### ImageUploadable Trait

The `ImageUploadable` trait provides:

- Automatic image upload handling
- Automatic image deletion when a model is deleted
- Configurable image fields, paths, and dimensions
- Methods for handling image uploads and deletions

### BaseModel

The `BaseModel` class includes:

- Common scopes like `active()`, `featured()`, `ordered()`, and `latest()`
- The `ImageUploadable` trait

### Form Requests

Form request classes like `NewsRequest` provide:

- Validation rules for image uploads
- Separation of validation logic from controllers

## Usage

### Using the ImageUploadable Trait

To use the `ImageUploadable` trait in a model:

1. Extend the `BaseModel` class
2. Override the `getImageFields()`, `getImagePaths()`, and `getImageDimensions()` methods

```php
class YourModel extends BaseModel
{
    public function getImageFields(): array
    {
        return ['image', 'thumbnail'];
    }

    public function getImagePaths(): array
    {
        return [
            'image' => 'your-model/images',
            'thumbnail' => 'your-model/thumbnails',
        ];
    }

    public function getImageDimensions(): array
    {
        return [
            'image' => [
                'width' => config('image.max_width'),
                'height' => config('image.max_height'),
                'quality' => config('image.quality'),
            ],
            'thumbnail' => [
                'width' => 300,
                'height' => 200,
                'quality' => config('image.quality'),
            ],
        ];
    }
}
```

### Using the Form Request

To use the form request:

1. Create a new form request class that extends `ImageUploadRequest`
2. Override the `baseRules()` and `getImageFields()` methods

```php
class YourModelRequest extends ImageUploadRequest
{
    protected function baseRules(): array
    {
        return [
            'name' => 'required|string|max:255',
            // Other validation rules
        ];
    }

    protected function getImageFields(): array
    {
        return [
            'image' => false, // Not required
            'thumbnail' => true, // Required
        ];
    }
}
```

### In Controllers

In controllers, use the form request and the model's image handling methods:

```php
public function store(YourModelRequest $request)
{
    $data = $request->validated();

    if ($request->hasFile('image')) {
        $data['image'] = YourModel::make()->handleImageUpload($request->file('image'), 'image');
    }

    YourModel::create($data);

    return redirect()->route('your-models.index')
        ->with('success', 'Model created successfully.');
}

public function update(YourModelRequest $request, YourModel $model)
{
    $data = $request->validated();

    if ($request->hasFile('image')) {
        $model->deleteImage('image');
        $data['image'] = $model->handleImageUpload($request->file('image'), 'image');
    }

    $model->update($data);

    return redirect()->route('your-models.index')
        ->with('success', 'Model updated successfully.');
}
```

## Benefits

This refactoring provides several benefits:

1. **Reduced code duplication**: Image handling logic is centralized in the `ImageService` class and `ImageUploadable` trait
2. **Better error handling**: All image operations include proper error handling and logging
3. **Automatic cleanup**: Images are automatically deleted when models are deleted
4. **Consistent validation**: Image validation is consistent across the application
5. **Easier maintenance**: Changes to image handling only need to be made in one place

## Future Improvements

Potential future improvements include:

1. Adding support for image resizing on the fly
2. Implementing image optimization
3. Adding support for image cropping
4. Implementing a queue system for processing large images
5. Adding support for cloud storage providers
