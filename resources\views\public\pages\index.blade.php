@extends('layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Halaman' : 'Pages')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Halaman' : 'Pages' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Halaman' : 'Pages' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Pages List -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                @forelse($pages as $page)
                    <div class="col-md-6 col-lg-4">
                        <div class="card h-100 shadow-sm hover-card">
                            @if($page->image)
                                <img src="{{ asset('storage/' . $page->image) }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title) }}">
                            @else
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-file-alt fa-4x text-muted"></i>
                                </div>
                            @endif
                            <div class="card-body">
                                <h5 class="card-title">{{ app()->getLocale() == 'id' ? $page->title : ($page->title_en ?: $page->title) }}</h5>
                                <div class="card-text mb-3">
                                    @php
                                        $content = app()->getLocale() == 'id' ? $page->content : ($page->content_en ?: $page->content);
                                        $plainText = strip_tags($content);
                                        $excerpt = Str::limit($plainText, 120);
                                    @endphp
                                    {{ $excerpt }}
                                </div>
                                <a href="{{ route('pages.show', $page->slug) }}" class="btn btn-outline-success">
                                    {{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada halaman yang tersedia.' : 'No pages available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .hover-card {
        transition: transform 0.3s ease;
    }
    
    .hover-card:hover {
        transform: translateY(-5px);
    }
</style>
@endpush
