<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class GalleryController extends Controller
{
    /**
     * Display a listing of the gallery.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $galleries = \App\Models\Gallery::where('is_active', true)
            ->latest()  // Order by created_at DESC (newest first)
            ->paginate(12);

        // Get unique categories
        $categories = \App\Models\Gallery::where('is_active', true)
            ->select('category')
            ->distinct()
            ->whereNotNull('category')
            ->orderBy('category')  // Order by category name instead of order
            ->pluck('category');

        return view('public.gallery.index', compact('galleries', 'categories'));
    }

    /**
     * Display a listing of the gallery by category.
     *
     * @param  string  $category
     * @return \Illuminate\View\View
     */
    public function category($category)
    {
        $galleries = \App\Models\Gallery::where('is_active', true)
            ->where('category', $category)
            ->latest()  // Order by created_at DESC (newest first)
            ->paginate(12);

        // Get unique categories
        $categories = \App\Models\Gallery::where('is_active', true)
            ->select('category')
            ->distinct()
            ->whereNotNull('category')
            ->orderBy('category')  // Order by category name instead of order
            ->pluck('category');

        return view('public.gallery.index', compact('galleries', 'categories', 'category'));
    }
}
