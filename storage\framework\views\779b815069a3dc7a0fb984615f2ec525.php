<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo e(app()->getLocale() == 'id' ? '<PERSON><PERSON><PERSON>' : 'Registration Proof'); ?> - <?php echo e($registration->registration_number); ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .print-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }

        .print-header {
            padding: 20px;
            border-bottom: 2px solid #198754;
            background-color: #f8f9fa;
        }

        .print-logo {
            max-height: 80px;
            max-width: 100%;
        }

        .print-institution-name {
            font-size: 24px;
            font-weight: 700;
            color: #198754;
            margin-bottom: 5px;
            line-height: 1.2;
        }

        .print-institution-address {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .print-institution-contact {
            font-size: 14px;
            color: #6c757d;
        }

        .print-title {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .print-title h2 {
            font-weight: 700;
            color: #198754;
            margin-bottom: 5px;
        }

        .print-title p {
            color: #6c757d;
            margin-bottom: 0;
        }

        .print-content {
            padding: 30px;
        }

        .print-section {
            margin-bottom: 30px;
        }

        .print-section-title {
            font-weight: 600;
            color: #198754;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
        }

        .print-info-row {
            margin-bottom: 10px;
        }

        .print-info-label {
            font-weight: 600;
            color: #495057;
        }

        .print-info-value {
            color: #212529;
        }

        .print-footer {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }

        .print-note {
            font-style: italic;
            font-size: 14px;
            color: #6c757d;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #198754;
        }

        .print-qr {
            text-align: center;
            margin-top: 20px;
        }

        .print-qr img {
            max-width: 120px;
        }

        .print-qr-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .print-stamp {
            text-align: right;
            margin-top: 40px;
        }

        .print-stamp-line {
            width: 150px;
            border-bottom: 1px solid #000;
            margin-top: 60px;
            margin-left: auto;
        }

        .print-stamp-name {
            margin-top: 5px;
            font-weight: 600;
        }

        .print-stamp-title {
            font-size: 14px;
            color: #6c757d;
        }

        @media print {
            body {
                background-color: #fff;
                padding: 0;
            }

            .print-container {
                box-shadow: none;
                max-width: 100%;
                border-radius: 0;
            }

            .no-print {
                display: none !important;
            }

            @page {
                size: A4;
                margin: 2cm;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4 mb-4">
        <div class="text-end mb-3 no-print">
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Cetak Sekarang' : 'Print Now'); ?>

            </button>
            <a href="<?php echo e(route('registration.success', ['registration' => $registration->id])); ?>" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-arrow-left me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Kembali' : 'Back'); ?>

            </a>
        </div>

        <div class="print-container">
            <!-- Header with Logo and Institution Info -->
            <div class="print-header">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center text-md-start mb-3 mb-md-0">
                        <img src="<?php echo e(asset(\App\Helpers\SettingHelper::getLogo())); ?>" alt="Logo" class="print-logo">
                    </div>
                    <div class="col-md-10">
                        <div class="print-institution-name">
                            <?php echo e(app()->getLocale() == 'id' ?
                                \App\Helpers\SettingHelper::getInstitutionName() :
                                \App\Helpers\SettingHelper::get('institution_name_en', \App\Helpers\SettingHelper::getInstitutionName())); ?>

                        </div>
                        <div class="print-institution-address">
                            <?php echo e(app()->getLocale() == 'id' ?
                                \App\Helpers\SettingHelper::get('address', 'Jl. Contoh No. 123, Kota, Provinsi, Indonesia') :
                                \App\Helpers\SettingHelper::get('address_en', 'Example St. No. 123, City, Province, Indonesia')); ?>

                        </div>
                        <div class="print-institution-contact">
                            <i class="fas fa-phone-alt me-1"></i> <?php echo e(\App\Helpers\SettingHelper::get('phone', '+62 123 4567 890')); ?> |
                            <i class="fas fa-envelope me-1"></i> <?php echo e(\App\Helpers\SettingHelper::get('email', '<EMAIL>')); ?> |
                            <i class="fas fa-globe me-1"></i> <?php echo e(\App\Helpers\SettingHelper::get('domain', 'www.nurulhayah4.com')); ?>

                        </div>
                    </div>
                </div>
            </div>

            <!-- Title -->
            <div class="print-title">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'BUKTI PENDAFTARAN SANTRI' : 'STUDENT REGISTRATION PROOF'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Tahun Ajaran ' . date('Y') . '/' . (date('Y') + 1) : 'Academic Year ' . date('Y') . '/' . (date('Y') + 1)); ?></p>
            </div>

            <!-- Content -->
            <div class="print-content">
                <!-- Registration Information -->
                <div class="print-section">
                    <h4 class="print-section-title"><?php echo e(app()->getLocale() == 'id' ? 'Informasi Pendaftaran' : 'Registration Information'); ?></h4>

                    <div class="row print-info-row">
                        <div class="col-md-4 print-info-label"><?php echo e(app()->getLocale() == 'id' ? 'Nomor Pendaftaran' : 'Registration Number'); ?></div>
                        <div class="col-md-8 print-info-value"><?php echo e($registration->registration_number); ?></div>
                    </div>

                    <div class="row print-info-row">
                        <div class="col-md-4 print-info-label"><?php echo e(app()->getLocale() == 'id' ? 'Tanggal Pendaftaran' : 'Registration Date'); ?></div>
                        <div class="col-md-8 print-info-value"><?php echo e($registration->created_at->format('d F Y')); ?></div>
                    </div>

                    <div class="row print-info-row">
                        <div class="col-md-4 print-info-label"><?php echo e(app()->getLocale() == 'id' ? 'Jenjang Pendidikan' : 'Education Level'); ?></div>
                        <div class="col-md-8 print-info-value">
                            <?php if($registration->educationUnit): ?>
                                <?php echo e(app()->getLocale() == 'id' ? $registration->educationUnit->name : $registration->educationUnit->name_en); ?>

                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row print-info-row">
                        <div class="col-md-4 print-info-label"><?php echo e(app()->getLocale() == 'id' ? 'Nama Lengkap' : 'Full Name'); ?></div>
                        <div class="col-md-8 print-info-value"><?php echo e($registration->full_name); ?></div>
                    </div>
                </div>

                <!-- Note -->
                <div class="print-note">
                    <p class="mb-0">
                        <?php echo e(app()->getLocale() == 'id' ?
                            'Bukti pendaftaran ini harap dibawa saat melakukan pembayaran dan verifikasi dokumen. Untuk informasi lebih lanjut, silakan hubungi kami melalui kontak yang tertera di atas!' :
                            'Please bring this registration proof when making payment and document verification. For more information, please contact us through the contact information listed above!'); ?>

                    </p>
                </div>

                <!-- Signature -->
                <div class="row mt-5">
                    <div class="col-md-6">
                        <div class="print-qr">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=<?php echo e($registration->registration_number); ?>" alt="QR Code">
                            <div class="print-qr-text"><?php echo e(app()->getLocale() == 'id' ? 'Scan untuk verifikasi' : 'Scan to verify'); ?></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="print-stamp">
                            <div><?php echo e(app()->getLocale() == 'id' ? 'Petugas Pendaftaran' : 'Registration Officer'); ?></div>
                            <div class="print-stamp-line"></div>
                            <div class="print-stamp-name"><?php echo e(app()->getLocale() == 'id' ? 'Admin Pendaftaran' : 'Registration Admin'); ?></div>
                            <div class="print-stamp-title"><?php echo e(app()->getLocale() == 'id' ? 'Panitia Penerimaan Santri Baru' : 'New Student Admission Committee'); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="print-footer">
                <div><?php echo e(app()->getLocale() == 'id' ? 'Dicetak pada: ' : 'Printed on: '); ?> <?php echo e(now()->format('d F Y, H:i')); ?></div>
                <div><?php echo e(app()->getLocale() == 'id' ? 'Dokumen ini sah tanpa tanda tangan basah' : 'This document is valid without wet signature'); ?></div>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH /home/<USER>/laravel/resources/views/public/registration/print.blade.php ENDPATH**/ ?>