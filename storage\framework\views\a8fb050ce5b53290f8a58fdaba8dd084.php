<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Kontak' : 'Contact'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Hubungi Kami' : 'Contact Us'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Kontak' : 'Contact'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0" data-aos="fade-up">
                    <div class="contact-info">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body p-4">
                                <h3 class="card-title text-success mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Informasi Kontak' : 'Contact Information'); ?></h3>

                                <div class="d-flex mb-4">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-map-marker-alt text-success fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5><?php echo e(app()->getLocale() == 'id' ? 'Alamat' : 'Address'); ?></h5>
                                        <p class="mb-0"><?php echo e($address); ?></p>
                                    </div>
                                </div>

                                <div class="d-flex mb-4">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-phone-alt text-success fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5><?php echo e(app()->getLocale() == 'id' ? 'Telepon' : 'Phone'); ?></h5>
                                        <p class="mb-0"><?php echo e($phone); ?></p>
                                    </div>
                                </div>

                                <div class="d-flex mb-4">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-envelope text-success fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5><?php echo e(app()->getLocale() == 'id' ? 'Email' : 'Email'); ?></h5>
                                        <p class="mb-0"><?php echo e($email); ?></p>
                                    </div>
                                </div>

                                <div class="d-flex">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-clock text-success fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5><?php echo e(app()->getLocale() == 'id' ? 'Jam Operasional' : 'Operating Hours'); ?></h5>
                                        <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Senin - Jumat: 08.00 - 16.00' : 'Monday - Friday: 08.00 AM - 04.00 PM'); ?></p>
                                        <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Sabtu: 08.00 - 12.00' : 'Saturday: 08.00 AM - 12.00 PM'); ?></p>
                                        <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Ahad: Tutup' : 'Sunday: Closed'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8" data-aos="fade-up" data-aos-delay="100">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="card-title text-success mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Kirim Pesan' : 'Send Message'); ?></h3>

                            <?php if(session('success')): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <?php echo e(session('success')); ?>

                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <form action="<?php echo e(route('contact.store')); ?>" method="POST" class="contact-form needs-validation" novalidate>
                                <?php echo csrf_field(); ?>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label"><?php echo e(app()->getLocale() == 'id' ? 'Nama Lengkap' : 'Full Name'); ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                        <div class="invalid-feedback">
                                            <?php echo e(app()->getLocale() == 'id' ? 'Nama lengkap wajib diisi.' : 'Full name is required.'); ?>

                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label"><?php echo e(app()->getLocale() == 'id' ? 'Email' : 'Email'); ?> <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo e(old('email')); ?>" required>
                                        <div class="invalid-feedback">
                                            <?php echo e(app()->getLocale() == 'id' ? 'Email wajib diisi dengan format yang benar.' : 'Email is required with the correct format.'); ?>

                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label"><?php echo e(app()->getLocale() == 'id' ? 'Nomor Telepon' : 'Phone Number'); ?></label>
                                        <input type="text" class="form-control" id="phone" name="phone" value="<?php echo e(old('phone')); ?>">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="subject" class="form-label"><?php echo e(app()->getLocale() == 'id' ? 'Subjek' : 'Subject'); ?> <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="subject" name="subject" value="<?php echo e(old('subject')); ?>" required>
                                        <div class="invalid-feedback">
                                            <?php echo e(app()->getLocale() == 'id' ? 'Subjek wajib diisi.' : 'Subject is required.'); ?>

                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-3">
                                        <label for="message" class="form-label"><?php echo e(app()->getLocale() == 'id' ? 'Pesan' : 'Message'); ?> <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="message" name="message" rows="5" required><?php echo e(old('message')); ?></textarea>
                                        <div class="invalid-feedback">
                                            <?php echo e(app()->getLocale() == 'id' ? 'Pesan wajib diisi.' : 'Message is required.'); ?>

                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <button type="submit" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Kirim Pesan' : 'Send Message'); ?></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Lokasi Kami' : 'Our Location'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Temukan lokasi Pondok Pesantren Nurul Hayah 4 di peta' : 'Find the location of Nurul Hayah 4 Islamic Boarding School on the map'); ?></p>
            </div>

            <div class="row">
                <div class="col-12" data-aos="fade-up">
                    <div class="map-container">
                        <iframe width="100%" height="100%" frameborder="0" scrolling="no" marginheight="0" marginwidth="0" src="<?php echo e($mapUrl); ?>" style="border: 1px solid #ddd; border-radius: 5px;"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/contact/index.blade.php ENDPATH**/ ?>