<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivitySchedule;
use Illuminate\Http\Request;

class ActivityScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $activeTab = $request->query('tab', 'daily');

        // Validate tab parameter
        if (!in_array($activeTab, ['daily', 'weekly', 'monthly', 'yearly'])) {
            $activeTab = 'daily';
        }

        // Get schedules based on active tab
        $schedules = ActivitySchedule::where('activity_type', $activeTab)
            ->when($activeTab === 'weekly', function($query) {
                // Custom order for days of the week starting from Saturday
                return $query->orderByRaw("CASE
                    WHEN day_of_week = 'Saturday' THEN 1
                    WHEN day_of_week = 'Sunday' THEN 2
                    WHEN day_of_week = 'Monday' THEN 3
                    WHEN day_of_week = 'Tuesday' THEN 4
                    WHEN day_of_week = 'Wednesday' THEN 5
                    WHEN day_of_week = 'Thursday' THEN 6
                    WHEN day_of_week = 'Friday' THEN 7
                    ELSE 8 END");
            })
            ->when($activeTab === 'monthly', function($query) {
                return $query->orderBy('week_number', 'asc');
            })
            ->when($activeTab === 'yearly', function($query) {
                return $query->orderBy('month_number', 'asc');
            })
            ->orderBy('start_time', 'asc')
            ->get();

        return view('admin.schedules.index', compact(
            'schedules',
            'activeTab'
        ));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.schedules.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'activity_type' => 'required|string|in:daily,weekly,monthly,yearly',
            'day_of_week' => 'required_if:activity_type,weekly|nullable|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            'week_number' => 'required_if:activity_type,monthly|nullable|integer|min:1|max:4',
            'month_number' => 'required_if:activity_type,yearly|nullable|integer|min:1|max:12',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'location' => 'nullable|string|max:255',
            'location_en' => 'nullable|string|max:255',
            'instructor' => 'nullable|string|max:255',
            'instructor_en' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = ActivitySchedule::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Set default values based on activity type
        if ($data['activity_type'] !== 'weekly' && empty($data['day_of_week'])) {
            // Set appropriate default values for non-weekly activities
            if ($data['activity_type'] === 'daily') {
                $data['day_of_week'] = 'Daily';
            } else if ($data['activity_type'] === 'monthly') {
                $data['day_of_week'] = 'Monthly';
            } else if ($data['activity_type'] === 'yearly') {
                $data['day_of_week'] = 'Yearly';
            }
        }

        $schedule = ActivitySchedule::create($data);

        return redirect()->route('admin.schedules.index', ['tab' => $schedule->activity_type])
            ->with('success', 'Activity schedule created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $schedule = ActivitySchedule::findOrFail($id);
        return view('admin.schedules.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $schedule = ActivitySchedule::findOrFail($id);
        return view('admin.schedules.edit', compact('schedule'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, string $id)
    {
        $schedule = ActivitySchedule::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'activity_type' => 'required|string|in:daily,weekly,monthly,yearly',
            'day_of_week' => 'required_if:activity_type,weekly|nullable|string|in:Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',
            'week_number' => 'required_if:activity_type,monthly|nullable|integer|min:1|max:4',
            'month_number' => 'required_if:activity_type,yearly|nullable|integer|min:1|max:12',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'location' => 'nullable|string|max:255',
            'location_en' => 'nullable|string|max:255',
            'instructor' => 'nullable|string|max:255',
            'instructor_en' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        $data = $request->all();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Set default values based on activity type
        if ($data['activity_type'] !== 'weekly' && empty($data['day_of_week'])) {
            // Set appropriate default values for non-weekly activities
            if ($data['activity_type'] === 'daily') {
                $data['day_of_week'] = 'Daily';
            } else if ($data['activity_type'] === 'monthly') {
                $data['day_of_week'] = 'Monthly';
            } else if ($data['activity_type'] === 'yearly') {
                $data['day_of_week'] = 'Yearly';
            }
        }

        $schedule->update($data);

        return redirect()->route('admin.schedules.index', ['tab' => $schedule->activity_type])
            ->with('success', 'Activity schedule updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id, Request $request)
    {
        $schedule = ActivitySchedule::findOrFail($id);
        $activityType = $schedule->activity_type;
        $schedule->delete();

        // Get the current tab from the request, default to the deleted schedule's type
        $tab = $request->query('tab', $activityType);

        return redirect()->route('admin.schedules.index', ['tab' => $tab])
            ->with('success', 'Activity schedule deleted successfully.');
    }
}
