<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class Paint extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'paints';

    protected $fillable = [
        'title',
        'title_en',
        'description',
        'description_en',
        'image',
        'artist',
        'artist_en',
        'year',
        'medium',
        'medium_en',
        'dimensions',
        'is_active',
        'view_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'year' => 'integer',
        'view_count' => 'integer',
    ];



    /**
     * Scope a query to only include active paints.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Handle image upload
     *
     * @param  \Illuminate\Http\UploadedFile  $file
     * @param  string  $field
     * @return string
     */
    public function handleImageUpload($file, $field = 'image')
    {
        // Delete old image if exists
        $this->deleteImage($field);

        // Store the new image
        $path = $file->store('paints', 'public');
        return $path;
    }

    /**
     * Delete image
     *
     * @param  string  $field
     * @return void
     */
    public function deleteImage($field = 'image')
    {
        if ($this->{$field} && Storage::disk('public')->exists($this->{$field})) {
            Storage::disk('public')->delete($this->{$field});
        }
    }
}
