<?php

namespace App\Exports;

use App\Models\Registration;
use App\Models\EducationUnit;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithTitle;
// use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class RegistrationsExport implements FromCollection, WithHeadings, WithMapping, WithSty<PERSON>, ShouldAutoSize, WithTitle, WithEvents
{
    protected $registrations;
    protected $filters;

    public function __construct($registrations = null, $filters = [])
    {
        $this->registrations = $registrations;
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        if ($this->registrations) {
            return $this->registrations;
        }

        $query = Registration::query()->with('educationUnit');

        // Apply filters if provided
        if (isset($this->filters['status']) && $this->filters['status']) {
            $query->where('status', $this->filters['status']);
        }

        if (isset($this->filters['education_unit_id']) && $this->filters['education_unit_id']) {
            $query->where('education_unit_id', $this->filters['education_unit_id']);
        }

        if (isset($this->filters['search']) && $this->filters['search']) {
            $search = $this->filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('registration_number', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        return $query->latest()->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'No.',
            'Nomor Pendaftaran',
            'Tanggal Pendaftaran',
            'Status',
            'NIK',
            'Nama Lengkap',
            'Jenis Kelamin',
            'Tempat Lahir',
            'Tanggal Lahir',
            'Nama Wali',
            'Nama Ayah',
            'Pekerjaan Ayah',
            'Nama Ibu',
            'Pekerjaan Ibu',
            'Alamat',
            'No. Telepon',
            'Email',
            'NISN',
            'Pendidikan Terakhir',
            'Sekolah Asal',
            'Tahun Lulus',
            'Unit Pendidikan',
            'Alasan Mendaftar',
            'Hobi',
            'Cita-cita',
            'Catatan',
        ];
    }

    /**
     * @param mixed $registration
     * @return array
     */
    public function map($registration): array
    {
        // Note: We're not using this row number in the Excel export anymore
        // It's handled directly in the registerEvents method
        static $rowNumber = 0;
        $rowNumber++;

        // Translate status to Indonesian
        $status = '';
        switch ($registration->status) {
            case 'pending':
                $status = 'Menunggu';
                break;
            case 'approved':
                $status = 'Diterima';
                break;
            case 'rejected':
                $status = 'Ditolak';
                break;
            default:
                $status = ucfirst($registration->status);
        }

        // Translate gender to Indonesian
        $gender = '';
        switch (strtolower($registration->gender)) {
            case 'male':
                $gender = 'Laki-laki';
                break;
            case 'female':
                $gender = 'Perempuan';
                break;
            default:
                $gender = $registration->gender;
        }

        return [
            $rowNumber,
            $registration->registration_number,
            $registration->created_at ? $registration->created_at->format('d-m-Y H:i:s') : '',
            $status,
            $registration->nik,
            $registration->full_name,
            $gender,
            $registration->place_of_birth,
            $registration->date_of_birth ? $registration->date_of_birth->format('d-m-Y') : '',
            $registration->parent_name,
            $registration->father_name,
            $registration->father_occupation,
            $registration->mother_name,
            $registration->mother_occupation,
            $registration->address,
            $registration->phone,
            $registration->email,
            $registration->nisn,
            $registration->last_education,
            $registration->previous_school,
            $registration->graduation_year,
            $registration->educationUnit ? $registration->educationUnit->name : '',
            $registration->reason,
            $registration->hobby,
            $registration->ambition,
            $registration->notes,
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        // We'll handle all styling in the registerEvents method
        return [];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Pendaftaran';
    }

    // Removed startCell method as we're handling cell placement manually

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;

                // Get institution information from settings
                $institutionName = setting('institution_name', 'PONDOK PESANTREN NURUL HAYAH 4');
                $institutionAddress = setting('institution_address', 'Jl. Raya Pesantren No. 123, Kota Santri');
                $institutionPhone = setting('institution_phone', '(021) 12345678');
                $institutionEmail = setting('institution_email', '<EMAIL>');
                $academicYear = date('Y') . '/' . (date('Y') + 1);

                // Clear the default data that was placed at A1
                $sheet->getDelegate()->removeRow(1, 8); // Remove first 8 rows to make room for our header

                // Add institution information
                $sheet->setCellValue('A1', 'DATA PENDAFTARAN SANTRI BARU');
                $sheet->setCellValue('A2', $institutionName);
                $sheet->setCellValue('A3', 'Tahun Ajaran: ' . $academicYear);
                $sheet->setCellValue('A4', $institutionAddress);
                $sheet->setCellValue('A5', 'Telp: ' . $institutionPhone . ' | Email: ' . $institutionEmail);

                // Get the highest column letter
                $highestColumn = $sheet->getHighestColumn();

                // Merge cells for header
                $sheet->mergeCells('A1:' . $highestColumn . '1');
                $sheet->mergeCells('A2:' . $highestColumn . '2');
                $sheet->mergeCells('A3:' . $highestColumn . '3');
                $sheet->mergeCells('A4:' . $highestColumn . '4');
                $sheet->mergeCells('A5:' . $highestColumn . '5');

                // Style header
                $sheet->getStyle('A1:' . $highestColumn . '1')->getFont()->setBold(true)->setSize(16);
                $sheet->getStyle('A2:' . $highestColumn . '2')->getFont()->setBold(true)->setSize(14);
                $sheet->getStyle('A3:' . $highestColumn . '5')->getFont()->setSize(12);

                $sheet->getStyle('A1:' . $highestColumn . '5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

                // Add empty row as separator
                $sheet->setCellValue('A6', '');

                // Add column headers at row 7
                $headers = $this->headings();
                // Manually set headers to ensure correct column placement
                $sheet->setCellValue('A7', $headers[0]); // No.
                $sheet->setCellValue('B7', $headers[1]); // Nomor Pendaftaran
                $sheet->setCellValue('C7', $headers[2]); // Tanggal Pendaftaran
                $sheet->setCellValue('D7', $headers[3]); // Status
                $sheet->setCellValue('E7', $headers[4]); // NIK
                $sheet->setCellValue('F7', $headers[5]); // Nama Lengkap
                $sheet->setCellValue('G7', $headers[6]); // Jenis Kelamin
                $sheet->setCellValue('H7', $headers[7]); // Tempat Lahir
                $sheet->setCellValue('I7', $headers[8]); // Tanggal Lahir
                $sheet->setCellValue('J7', $headers[9]); // Nama Wali
                $sheet->setCellValue('K7', $headers[10]); // Nama Ayah
                $sheet->setCellValue('L7', $headers[11]); // Pekerjaan Ayah
                $sheet->setCellValue('M7', $headers[12]); // Nama Ibu
                $sheet->setCellValue('N7', $headers[13]); // Pekerjaan Ibu
                $sheet->setCellValue('O7', $headers[14]); // Alamat
                $sheet->setCellValue('P7', $headers[15]); // No. Telepon
                $sheet->setCellValue('Q7', $headers[16]); // Email
                $sheet->setCellValue('R7', $headers[17]); // NISN
                $sheet->setCellValue('S7', $headers[18]); // Pendidikan Terakhir
                $sheet->setCellValue('T7', $headers[19]); // Sekolah Asal
                $sheet->setCellValue('U7', $headers[20]); // Tahun Lulus
                $sheet->setCellValue('V7', $headers[21]); // Unit Pendidikan
                $sheet->setCellValue('W7', $headers[22]); // Alasan Mendaftar
                $sheet->setCellValue('X7', $headers[23]); // Hobi
                $sheet->setCellValue('Y7', $headers[24]); // Cita-cita
                $sheet->setCellValue('Z7', $headers[25]); // Catatan

                // Get the data from collection
                $collection = $this->collection();
                $rowNumber = 8; // Start data from row 8
                $counter = 1; // Initialize counter for row numbers starting at 1

                foreach ($collection as $item) {
                    $data = $this->map($item);

                    // Override the row number with our counter to ensure it starts from 1
                    $sheet->setCellValue('A' . $rowNumber, $counter); // No.
                    $sheet->setCellValue('B' . $rowNumber, $data[1]); // Nomor Pendaftaran
                    $sheet->setCellValue('C' . $rowNumber, $data[2]); // Tanggal Pendaftaran
                    $sheet->setCellValue('D' . $rowNumber, $data[3]); // Status
                    $sheet->setCellValue('E' . $rowNumber, $data[4]); // NIK
                    $sheet->setCellValue('F' . $rowNumber, $data[5]); // Nama Lengkap
                    $sheet->setCellValue('G' . $rowNumber, $data[6]); // Jenis Kelamin
                    $sheet->setCellValue('H' . $rowNumber, $data[7]); // Tempat Lahir
                    $sheet->setCellValue('I' . $rowNumber, $data[8]); // Tanggal Lahir
                    $sheet->setCellValue('J' . $rowNumber, $data[9]); // Nama Wali
                    $sheet->setCellValue('K' . $rowNumber, $data[10]); // Nama Ayah
                    $sheet->setCellValue('L' . $rowNumber, $data[11]); // Pekerjaan Ayah
                    $sheet->setCellValue('M' . $rowNumber, $data[12]); // Nama Ibu
                    $sheet->setCellValue('N' . $rowNumber, $data[13]); // Pekerjaan Ibu
                    $sheet->setCellValue('O' . $rowNumber, $data[14]); // Alamat
                    $sheet->setCellValue('P' . $rowNumber, $data[15]); // No. Telepon
                    $sheet->setCellValue('Q' . $rowNumber, $data[16]); // Email
                    $sheet->setCellValue('R' . $rowNumber, $data[17]); // NISN
                    $sheet->setCellValue('S' . $rowNumber, $data[18]); // Pendidikan Terakhir
                    $sheet->setCellValue('T' . $rowNumber, $data[19]); // Sekolah Asal
                    $sheet->setCellValue('U' . $rowNumber, $data[20]); // Tahun Lulus
                    $sheet->setCellValue('V' . $rowNumber, $data[21]); // Unit Pendidikan
                    $sheet->setCellValue('W' . $rowNumber, $data[22]); // Alasan Mendaftar
                    $sheet->setCellValue('X' . $rowNumber, $data[23]); // Hobi
                    $sheet->setCellValue('Y' . $rowNumber, $data[24]); // Cita-cita
                    $sheet->setCellValue('Z' . $rowNumber, $data[25]); // Catatan

                    $rowNumber++;
                    $counter++; // Increment our counter for the next row
                }

                // Style column headers
                $sheet->getStyle('A7:' . $highestColumn . '7')->getFont()->setBold(true);
                $sheet->getStyle('A7:' . $highestColumn . '7')->getFill()
                    ->setFillType(Fill::FILL_SOLID)
                    ->getStartColor()->setRGB('DDDDDD');

                // Auto filter for column headers
                $sheet->setAutoFilter('A7:' . $highestColumn . '7');

                // Freeze the header row
                $sheet->freezePane('A8');

                // Get the highest row
                $highestRow = $rowNumber - 1;

                // Style all data cells
                $sheet->getStyle('A7:' . $highestColumn . $highestRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

                // Set text alignment for all cells
                $sheet->getStyle('A7:' . $highestColumn . $highestRow)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

                // Center specific columns
                $sheet->getStyle('A7:A' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // No column
                $sheet->getStyle('B7:B' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Registration Number
                $sheet->getStyle('C7:C' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Registration Date
                $sheet->getStyle('D7:D' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Status
                $sheet->getStyle('G7:G' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Gender
                $sheet->getStyle('I7:I' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Date of Birth
                $sheet->getStyle('U7:U' . $highestRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); // Graduation Year

                // Add footer with export date
                $footerRow = $highestRow + 2;
                $sheet->setCellValue('A' . $footerRow, 'Diekspor pada: ' . date('d-m-Y H:i:s'));
                $sheet->mergeCells('A' . $footerRow . ':' . $highestColumn . $footerRow);
                $sheet->getStyle('A' . $footerRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            },
        ];
    }
}
