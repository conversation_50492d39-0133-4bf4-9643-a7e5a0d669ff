<?php $__env->startSection('title', 'View Video'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Video</h1>
        <div>
            <a href="<?php echo e(route('admin.videos.edit', $video)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.videos.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Video Details</h5>
                </div>
                <div class="card-body">
                    <div class="ratio ratio-16x9 mb-4">
                        <iframe src="<?php echo e($video->youtube_embed_url); ?>" title="<?php echo e($video->title); ?>" allowfullscreen></iframe>
                    </div>
                    
                    <div class="mb-3">
                        <h5><?php echo e($video->title); ?></h5>
                        <p class="text-muted"><?php echo e($video->description); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-muted">English Version</h6>
                        <h5><?php echo e($video->title_en); ?></h5>
                        <p class="text-muted"><?php echo e($video->description_en); ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>YouTube URL:</h6>
                        <a href="<?php echo e($video->youtube_url); ?>" target="_blank" class="d-block text-break">
                            <?php echo e($video->youtube_url); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>ID</span>
                            <span class="badge bg-secondary"><?php echo e($video->id); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Status</span>
                            <?php if($video->is_active): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Display Order</span>
                            <span><?php echo e($video->order); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Created At</span>
                            <span><?php echo e($video->created_at->format('d M Y H:i')); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Last Updated</span>
                            <span><?php echo e($video->updated_at->format('d M Y H:i')); ?></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="<?php echo e(route('admin.videos.edit', $video)); ?>" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Video
                    </a>
                    
                    <form action="<?php echo e(route('admin.videos.destroy', $video)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this video?')">
                            <i class="fas fa-trash me-1"></i> Delete Video
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/videos/show.blade.php ENDPATH**/ ?>