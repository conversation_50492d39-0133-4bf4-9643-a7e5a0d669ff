<?php $__env->startSection('title', 'View Registration'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Registration</h1>
        <a href="<?php echo e(route('admin.registrations.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Registration Details</h5>
                    <div>
                        <span class="badge <?php echo e($registration->status == 'pending' ? 'bg-warning' : ($registration->status == 'approved' ? 'bg-success' : 'bg-danger')); ?> fs-6">
                            <?php echo e(ucfirst($registration->status)); ?>

                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Registration Information Section -->
                    <h6 class="fw-bold text-primary mb-3 border-bottom pb-2">Registration Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Registration Number:</div>
                        <div class="col-md-8"><?php echo e($registration->registration_number); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Registration Date:</div>
                        <div class="col-md-8"><?php echo e($registration->created_at ? $registration->created_at->format('d M Y H:i:s') : 'Not available'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Education Level:</div>
                        <div class="col-md-8">
                            <?php if($registration->educationUnit): ?>
                                <?php echo e($registration->educationUnit->name); ?>

                            <?php else: ?>
                                Not specified
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Personal Information Section -->
                    <h6 class="fw-bold text-primary mb-3 mt-4 border-bottom pb-2">Personal Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">NIK:</div>
                        <div class="col-md-8"><?php echo e($registration->nik); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Full Name:</div>
                        <div class="col-md-8"><?php echo e($registration->full_name); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Gender:</div>
                        <div class="col-md-8"><?php echo e($registration->gender == 'male' ? 'Male' : 'Female'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Place of Birth:</div>
                        <div class="col-md-8"><?php echo e($registration->place_of_birth); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Date of Birth:</div>
                        <div class="col-md-8"><?php echo e($registration->date_of_birth ? $registration->date_of_birth->format('d M Y') : 'Not specified'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">NISN:</div>
                        <div class="col-md-8"><?php echo e($registration->nisn ?: 'Not specified'); ?></div>
                    </div>

                    <!-- Contact Information Section -->
                    <h6 class="fw-bold text-primary mb-3 mt-4 border-bottom pb-2">Contact Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Address:</div>
                        <div class="col-md-8"><?php echo e($registration->address); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Phone:</div>
                        <div class="col-md-8"><?php echo e($registration->phone); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Email:</div>
                        <div class="col-md-8"><?php echo e($registration->email ?: 'Not specified'); ?></div>
                    </div>

                    <!-- Parent Information Section -->
                    <h6 class="fw-bold text-primary mb-3 mt-4 border-bottom pb-2">Parent Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Father's Name:</div>
                        <div class="col-md-8"><?php echo e($registration->father_name); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Father's Occupation:</div>
                        <div class="col-md-8"><?php echo e($registration->father_occupation); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Mother's Name:</div>
                        <div class="col-md-8"><?php echo e($registration->mother_name); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Mother's Occupation:</div>
                        <div class="col-md-8"><?php echo e($registration->mother_occupation); ?></div>
                    </div>

                    <?php if($registration->parent_name): ?>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Guardian's Name:</div>
                        <div class="col-md-8"><?php echo e($registration->parent_name); ?></div>
                    </div>

                    <?php if($registration->parent_occupation): ?>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Guardian's Occupation:</div>
                        <div class="col-md-8"><?php echo e($registration->parent_occupation); ?></div>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>

                    <!-- Educational Background Section -->
                    <h6 class="fw-bold text-primary mb-3 mt-4 border-bottom pb-2">Educational Background</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Last Education:</div>
                        <div class="col-md-8"><?php echo e($registration->last_education); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Previous School:</div>
                        <div class="col-md-8"><?php echo e($registration->previous_school ?: 'Not specified'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Graduation Year:</div>
                        <div class="col-md-8"><?php echo e($registration->graduation_year); ?></div>
                    </div>

                    <!-- Interests and Goals Section -->
                    <h6 class="fw-bold text-primary mb-3 mt-4 border-bottom pb-2">Interests and Goals</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Reason for Applying:</div>
                        <div class="col-md-8"><?php echo e($registration->reason ?: 'Not specified'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Hobby:</div>
                        <div class="col-md-8"><?php echo e($registration->hobby ?: 'Not specified'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Ambition:</div>
                        <div class="col-md-8"><?php echo e($registration->ambition ?: 'Not specified'); ?></div>
                    </div>

                    <!-- Additional Information Section -->
                    <h6 class="fw-bold text-primary mb-3 mt-4 border-bottom pb-2">Additional Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">Notes:</div>
                        <div class="col-md-8"><?php echo e($registration->notes ?: 'No notes'); ?></div>
                    </div>


                </div>
            </div>
        </div>

        <div class="col-md-4">

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Status Management</h5>
                </div>
                <div class="card-body">
                    <?php if($registration->status == 'pending'): ?>
                        <div class="d-grid gap-2 mb-3">
                            <form action="<?php echo e(route('admin.registrations.approve', $registration)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-success w-100" onclick="return confirm('Are you sure you want to approve this registration?')">
                                    <i class="fas fa-check me-1"></i> Approve Registration
                                </button>
                            </form>
                        </div>

                        <div class="d-grid gap-2">
                            <form action="<?php echo e(route('admin.registrations.reject', $registration)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to reject this registration?')">
                                    <i class="fas fa-times me-1"></i> Reject Registration
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="alert <?php echo e($registration->status == 'approved' ? 'alert-success' : 'alert-danger'); ?> mb-0">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas <?php echo e($registration->status == 'approved' ? 'fa-check-circle' : 'fa-times-circle'); ?> fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo e($registration->status == 'approved' ? 'Approved' : 'Rejected'); ?></h6>
                                    <div class="small">
                                        <?php if($registration->status == 'approved'): ?>
                                            Approved at: <?php echo e($registration->approved_at ? $registration->approved_at->format('d M Y H:i:s') : 'Not available'); ?>

                                        <?php else: ?>
                                            Rejected at: <?php echo e($registration->rejected_at ? $registration->rejected_at->format('d M Y H:i:s') : 'Not available'); ?>

                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="mailto:<?php echo e($registration->email); ?>" class="btn btn-info w-100 mb-2">
                        <i class="fas fa-envelope me-1"></i> Send Email
                    </a>

                    <form action="<?php echo e(route('admin.registrations.destroy', $registration)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this registration? This action cannot be undone.')">
                            <i class="fas fa-trash me-1"></i> Delete Registration
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/registrations/show.blade.php ENDPATH**/ ?>