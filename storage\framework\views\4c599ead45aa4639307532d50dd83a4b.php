<?php $__env->startSection('title', app()->getLocale() == 'id' ? $unit->name : $unit->name_en); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? $unit->name : $unit->name_en); ?></h1>
                    <p class="lead mb-3"><?php echo e(app()->getLocale() == 'id' ? $unit->level : $unit->level_en); ?></p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('education-units')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Unit Pendidikan' : 'Education Units'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? $unit->name : $unit->name_en); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Education Unit Detail Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8 mb-5 mb-lg-0">
                    <div class="edu-unit-detail-card fade-in">
                        <!-- Featured Image -->
                        <div class="edu-unit-detail-image">
                            <?php if($unit->image): ?>
                                <img src="<?php echo e(asset('storage/' . $unit->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $unit->name : $unit->name_en); ?>" class="img-fluid rounded">
                            <?php else: ?>
                                <img src="<?php echo e(asset('images/education-placeholder.jpg')); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $unit->name : $unit->name_en); ?>" class="img-fluid rounded">
                            <?php endif; ?>
                            <div class="edu-unit-detail-level <?php echo e($unit->edu_type == 'non-formal' ? 'non-formal' : ''); ?>">
                                <?php echo e(app()->getLocale() == 'id' ? $unit->level : $unit->level_en); ?>

                            </div>
                        </div>

                        <!-- Unit Information -->
                        <div class="edu-unit-detail-content mt-4">
                            <h2 class="mb-4"><?php echo e(app()->getLocale() == 'id' ? $unit->name : $unit->name_en); ?></h2>

                            <!-- Tabs Navigation -->
                            <ul class="nav nav-tabs mb-4" id="unitDetailTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                                        <?php echo e(app()->getLocale() == 'id' ? 'Gambaran Umum' : 'Overview'); ?>

                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="facilities-tab" data-bs-toggle="tab" data-bs-target="#facilities" type="button" role="tab" aria-controls="facilities" aria-selected="false">
                                        <?php echo e(app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities'); ?>

                                    </button>
                                </li>
                                <?php if($unit->principal_name): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="principal-tab" data-bs-toggle="tab" data-bs-target="#principal" type="button" role="tab" aria-controls="principal" aria-selected="false">
                                        <?php echo e(app()->getLocale() == 'id' ? ($unit->edu_type == 'formal' ? 'Kepala Sekolah' : 'Kepala Madrasah') : ($unit->edu_type == 'formal' ? 'School Principal' : 'Head')); ?>

                                    </button>
                                </li>
                                <?php endif; ?>
                            </ul>

                            <!-- Tabs Content -->
                            <div class="tab-content" id="unitDetailTabsContent">
                                <!-- Overview Tab -->
                                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                                    <div class="edu-unit-detail-desc">
                                        <?php echo app()->getLocale() == 'id' ? $unit->description : $unit->description_en; ?>

                                    </div>
                                </div>

                                <!-- Facilities Tab -->
                                <div class="tab-pane fade" id="facilities" role="tabpanel" aria-labelledby="facilities-tab">
                                    <div class="edu-unit-detail-facilities">
                                        <h3 class="mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Fasilitas yang Tersedia' : 'Available Facilities'); ?></h3>
                                        <?php echo app()->getLocale() == 'id' ? $unit->facilities : $unit->facilities_en; ?>

                                    </div>
                                </div>

                                <!-- Principal Tab -->
                                <?php if($unit->principal_name): ?>
                                <div class="tab-pane fade" id="principal" role="tabpanel" aria-labelledby="principal-tab">
                                    <div class="edu-unit-detail-principal">
                                        <div class="row">
                                            <div class="col-md-4 mb-4 mb-md-0">
                                                <div class="principal-image-container">
                                                    <?php if($unit->principal_image): ?>
                                                        <img src="<?php echo e(asset('storage/' . $unit->principal_image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $unit->principal_name : $unit->principal_name_en); ?>" class="img-fluid rounded">
                                                    <?php else: ?>
                                                        <img src="<?php echo e(asset('images/principal-placeholder.jpg')); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $unit->principal_name : $unit->principal_name_en); ?>" class="img-fluid rounded">
                                                    <?php endif; ?>
                                                </div>
                                                <h4 class="principal-name mt-3"><?php echo e(app()->getLocale() == 'id' ? $unit->principal_name : $unit->principal_name_en); ?></h4>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="principal-details">
                                                    <?php if($unit->principal_education && trim(strip_tags(app()->getLocale() == 'id' ? $unit->principal_education : $unit->principal_education_en)) != ''): ?>
                                                    <div class="principal-section mb-4">
                                                        <h5 class="section-title"><?php echo e(app()->getLocale() == 'id' ? 'Riwayat Pendidikan' : 'Education History'); ?></h5>
                                                        <div class="section-content">
                                                            <?php echo app()->getLocale() == 'id' ? $unit->principal_education : $unit->principal_education_en; ?>

                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if($unit->principal_experience && trim(strip_tags(app()->getLocale() == 'id' ? $unit->principal_experience : $unit->principal_experience_en)) != ''): ?>
                                                    <div class="principal-section mb-4">
                                                        <h5 class="section-title"><?php echo e(app()->getLocale() == 'id' ? 'Pengalaman' : 'Experience'); ?></h5>
                                                        <div class="section-content">
                                                            <?php echo app()->getLocale() == 'id' ? $unit->principal_experience : $unit->principal_experience_en; ?>

                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if($unit->principal_achievements && trim(strip_tags(app()->getLocale() == 'id' ? $unit->principal_achievements : $unit->principal_achievements_en)) != ''): ?>
                                                    <div class="principal-section">
                                                        <h5 class="section-title"><?php echo e(app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements'); ?></h5>
                                                        <div class="section-content">
                                                            <?php echo app()->getLocale() == 'id' ? $unit->principal_achievements : $unit->principal_achievements_en; ?>

                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Call to Action -->
                        <div class="edu-unit-detail-cta mt-5 p-4 bg-white rounded shadow-sm">
                            <div class="row align-items-center">
                                <div class="col-md-8 mb-3 mb-md-0">
                                    <h4 class="mb-2"><?php echo e(app()->getLocale() == 'id' ? 'Tertarik untuk Bergabung?' : 'Interested in Joining?'); ?></h4>
                                    <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Daftarkan diri Anda atau anak Anda sekarang untuk mendapatkan pendidikan berkualitas.' : 'Register yourself or your child now to get quality education.'); ?></p>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <a href="<?php echo e(route('registration')); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                        <!-- Curriculum Card -->
                        <div class="curriculum-card fade-in mb-4">
                            <div class="curriculum-header">
                                <h3><?php echo e(app()->getLocale() == 'id' ? ($unit->edu_type == 'formal' ? 'Kurikulum' : 'Program') : ($unit->edu_type == 'formal' ? 'Curriculum' : 'Program')); ?></h3>
                                <p class="text-white mb-0 small"><?php echo e(app()->getLocale() == 'id' ? 'Menampilkan semua data kurikulum' : 'Showing all curriculum data'); ?></p>
                            </div>
                            <div class="curriculum-body">
                                <?php if($curricula->count() > 0): ?>
                                    <div class="accordion" id="curriculumAccordion">
                                        <?php $__currentLoopData = $curricula; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $curriculum): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="heading<?php echo e($curriculum->id); ?>">
                                                    <button class="accordion-button <?php echo e($index === 0 ? '' : 'collapsed'); ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($curriculum->id); ?>" aria-expanded="<?php echo e($index === 0 ? 'true' : 'false'); ?>" aria-controls="collapse<?php echo e($curriculum->id); ?>">
                                                        <?php echo e(app()->getLocale() == 'id' ? $curriculum->title : $curriculum->title_en); ?>

                                                    </button>
                                                </h2>
                                                <div id="collapse<?php echo e($curriculum->id); ?>" class="accordion-collapse collapse <?php echo e($index === 0 ? 'show' : ''); ?>" aria-labelledby="heading<?php echo e($curriculum->id); ?>" data-bs-parent="#curriculumAccordion">
                                                    <div class="accordion-body">
                                                        <div class="curriculum-item-info">
                                                            <!-- Formal Curriculum Fields -->
                                                            <?php if($curriculum->edu_type == 'formal' || $unit->edu_type == 'formal'): ?>
                                                                <?php if($curriculum->educational_goals): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Tujuan Pendidikan' : 'Educational Goals'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->educational_goals : $curriculum->educational_goals_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->national_curriculum): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kurikulum Nasional' : 'National Curriculum'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->national_curriculum : $curriculum->national_curriculum_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->general_subjects): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Mata Pelajaran Umum' : 'General Subjects'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->general_subjects : $curriculum->general_subjects_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->religious_subjects): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Mata Pelajaran Agama' : 'Religious Subjects'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->religious_subjects : $curriculum->religious_subjects_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->fields_of_study): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Bidang Studi' : 'Fields of Study'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->fields_of_study : $curriculum->fields_of_study_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->local_content): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Muatan Lokal' : 'Local Content'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->local_content : $curriculum->local_content_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->class_levels): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Tingkatan Kelas' : 'Class Levels'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->class_levels : $curriculum->class_levels_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->core_textbooks_studied): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kitab Inti yang Dipelajari' : 'Core Textbooks Studied'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->core_textbooks_studied : $curriculum->core_textbooks_studied_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->study_schedule): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Jadwal Belajar' : 'Study Schedule'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->study_schedule : $curriculum->study_schedule_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->teaching_methods): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Metode Pengajaran' : 'Teaching Methods'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->teaching_methods : $curriculum->teaching_methods_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->language_of_instruction): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Bahasa Pengantar' : 'Language of Instruction'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->language_of_instruction : $curriculum->language_of_instruction_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->supporting_activities): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kegiatan Pendukung' : 'Supporting Activities'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->supporting_activities : $curriculum->supporting_activities_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->extracurricular): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kegiatan Ekstrakurikuler' : 'Extracurricular Activities'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->extracurricular : $curriculum->extracurricular_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->special_program): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Program Khusus' : 'Special Program'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->special_program : $curriculum->special_program_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->learning_approach): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Pendekatan Pembelajaran' : 'Learning Approach'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->learning_approach : $curriculum->learning_approach_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->assessment_system): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Sistem Penilaian' : 'Assessment System'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->assessment_system : $curriculum->assessment_system_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->assessment_evaluation): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Evaluasi Penilaian' : 'Assessment Evaluation'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->assessment_evaluation : $curriculum->assessment_evaluation_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->graduation_certificate || $curriculum->graduation_certificates): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Sertifikat Kelulusan' : 'Graduation Certificates'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? ($curriculum->graduation_certificates ?: $curriculum->graduation_certificate) : ($curriculum->graduation_certificates_en ?: $curriculum->graduation_certificate_en); ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                            <!-- Non-Formal Curriculum Fields -->
                                                            <?php endif; ?>

                                                            <?php if($curriculum->edu_type == 'non-formal' || $unit->edu_type == 'non-formal'): ?>
                                                                <?php if($curriculum->educational_goals): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Tujuan Pendidikan' : 'Educational Goals'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->educational_goals : $curriculum->educational_goals_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->national_curriculum): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kurikulum Nasional' : 'National Curriculum'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->national_curriculum : $curriculum->national_curriculum_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->general_subjects): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Mata Pelajaran Umum' : 'General Subjects'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->general_subjects : $curriculum->general_subjects_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->religious_subjects): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Mata Pelajaran Agama' : 'Religious Subjects'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->religious_subjects : $curriculum->religious_subjects_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->fields_of_study): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Bidang Studi' : 'Fields of Study'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->fields_of_study : $curriculum->fields_of_study_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->local_content): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Muatan Lokal' : 'Local Content'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->local_content : $curriculum->local_content_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->class_levels): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Tingkatan Kelas' : 'Class Levels'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->class_levels : $curriculum->class_levels_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->core_textbooks_studied): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kitab Inti yang Dipelajari' : 'Core Textbooks Studied'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->core_textbooks_studied : $curriculum->core_textbooks_studied_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->study_schedule): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Jadwal Belajar' : 'Study Schedule'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->study_schedule : $curriculum->study_schedule_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->teaching_methods): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Metode Pengajaran' : 'Teaching Methods'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->teaching_methods : $curriculum->teaching_methods_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->language_of_instruction): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Bahasa Pengantar' : 'Language of Instruction'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->language_of_instruction : $curriculum->language_of_instruction_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->supporting_activities): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kegiatan Pendukung' : 'Supporting Activities'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->supporting_activities : $curriculum->supporting_activities_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->extracurricular): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Kegiatan Ekstrakurikuler' : 'Extracurricular Activities'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->extracurricular : $curriculum->extracurricular_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->special_program): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Program Khusus' : 'Special Program'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->special_program : $curriculum->special_program_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->learning_approach): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Pendekatan Pembelajaran' : 'Learning Approach'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->learning_approach : $curriculum->learning_approach_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->assessment_system): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Sistem Penilaian' : 'Assessment System'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->assessment_system : $curriculum->assessment_system_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->assessment_evaluation): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Evaluasi Penilaian' : 'Assessment Evaluation'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? $curriculum->assessment_evaluation : $curriculum->assessment_evaluation_en; ?></p>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if($curriculum->graduation_certificate || $curriculum->graduation_certificates): ?>
                                                                    <div class="curriculum-detail-item">
                                                                        <strong><?php echo e(app()->getLocale() == 'id' ? 'Sertifikat Kelulusan' : 'Graduation Certificate'); ?>:</strong>
                                                                        <p><?php echo app()->getLocale() == 'id' ? ($curriculum->graduation_certificate ?: $curriculum->graduation_certificates) : ($curriculum->graduation_certificate_en ?: $curriculum->graduation_certificates_en); ?></p>
                                                                    </div>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php else: ?>
                                    <div class="p-4 text-center">
                                        <p class="text-muted"><?php echo e(app()->getLocale() == 'id' ? 'Belum ada data kurikulum yang tersedia.' : 'No curriculum data available yet.'); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <!-- Contact Information -->
                        <div class="edu-unit-sidebar-card mb-4 fade-in">
                            <div class="card-header">
                                <h4><?php echo e(app()->getLocale() == 'id' ? 'Informasi Kontak' : 'Contact Information'); ?></h4>
                            </div>
                            <div class="card-body">
                                <ul class="edu-unit-contact-list">
                                    <?php if($unit->address): ?>
                                        <li>
                                            <i class="fas fa-map-marker-alt"></i>
                                            <div>
                                                <strong><?php echo e(app()->getLocale() == 'id' ? 'Alamat' : 'Address'); ?></strong>
                                                <p><?php echo e($unit->address); ?></p>
                                            </div>
                                        </li>
                                    <?php endif; ?>
                                    <?php if($unit->phone): ?>
                                        <li>
                                            <i class="fas fa-phone"></i>
                                            <div>
                                                <strong><?php echo e(app()->getLocale() == 'id' ? 'Telepon' : 'Phone'); ?></strong>
                                                <p><?php echo e($unit->phone); ?></p>
                                            </div>
                                        </li>
                                    <?php endif; ?>
                                    <?php if($unit->email): ?>
                                        <li>
                                            <i class="fas fa-envelope"></i>
                                            <div>
                                                <strong><?php echo e(app()->getLocale() == 'id' ? 'Email' : 'Email'); ?></strong>
                                                <p><?php echo e($unit->email); ?></p>
                                            </div>
                                        </li>
                                    <?php endif; ?>
                                    <?php if($unit->website): ?>
                                        <li>
                                            <i class="fas fa-globe"></i>
                                            <div>
                                                <strong><?php echo e(app()->getLocale() == 'id' ? 'Website' : 'Website'); ?></strong>
                                                <p><a href="<?php echo e($unit->website); ?>" target="_blank"><?php echo e($unit->website); ?></a></p>
                                            </div>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>



                        <!-- Related Education Units -->
                        <?php if($relatedUnits->count() > 0): ?>
                            <div class="edu-unit-sidebar-card fade-in">
                                <div class="card-header">
                                    <h4><?php echo e(app()->getLocale() == 'id' ? 'Unit Pendidikan Terkait' : 'Related Education Units'); ?></h4>
                                </div>
                                <div class="card-body p-0">
                                    <ul class="edu-unit-related-list">
                                        <?php $__currentLoopData = $relatedUnits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedUnit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <a href="<?php echo e(route('education-units.show', $relatedUnit->id)); ?>" class="edu-unit-related-item">
                                                    <div class="related-item-image">
                                                        <?php if($relatedUnit->image): ?>
                                                            <img src="<?php echo e(asset('storage/' . $relatedUnit->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $relatedUnit->name : $relatedUnit->name_en); ?>">
                                                        <?php else: ?>
                                                            <img src="<?php echo e(asset('images/education-placeholder.jpg')); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $relatedUnit->name : $relatedUnit->name_en); ?>">
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="related-item-info">
                                                        <h5><?php echo e(app()->getLocale() == 'id' ? $relatedUnit->name : $relatedUnit->name_en); ?></h5>
                                                        <p><?php echo e(app()->getLocale() == 'id' ? $relatedUnit->level : $relatedUnit->level_en); ?></p>
                                                    </div>
                                                </a>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Education Unit Detail Styles */
    .edu-unit-detail-card {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 0 0 30px 0;
    }

    .edu-unit-detail-image {
        position: relative;
        overflow: hidden;
    }

    .edu-unit-detail-image img {
        width: 100%;
        max-height: 400px;
        object-fit: cover;
    }

    .edu-unit-detail-level {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(25, 135, 84, 0.8);
        color: #fff;
        padding: 10px 20px;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
    }

    .edu-unit-detail-level.non-formal {
        background-color: rgba(201, 165, 90, 0.8);
    }

    .edu-unit-detail-content {
        padding: 0 30px;
    }

    .edu-unit-detail-content h2 {
        font-size: 28px;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
    }

    .edu-unit-detail-desc,
    .edu-unit-detail-facilities,
    .edu-unit-detail-curriculum {
        font-size: 16px;
        line-height: 1.8;
        color: #555;
    }

    .edu-unit-detail-desc img,
    .edu-unit-detail-facilities img,
    .edu-unit-detail-curriculum img {
        max-width: 100%;
        height: auto;
        margin: 20px 0;
        border-radius: 8px;
    }

    .edu-unit-detail-desc h3,
    .edu-unit-detail-facilities h3,
    .edu-unit-detail-curriculum h3 {
        font-size: 22px;
        font-weight: 600;
        color: #333;
        margin: 25px 0 15px;
    }

    .edu-unit-detail-desc ul,
    .edu-unit-detail-facilities ul,
    .edu-unit-detail-curriculum ul {
        padding-left: 20px;
        margin-bottom: 20px;
    }

    .edu-unit-detail-desc ul li,
    .edu-unit-detail-facilities ul li,
    .edu-unit-detail-curriculum ul li {
        margin-bottom: 10px;
    }

    /* Sidebar Styles */
    /* Note: .sticky-sidebar is now defined in custom.css */

    .edu-unit-sidebar-card {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .edu-unit-sidebar-card .card-header {
        background-color: #198754;
        color: #fff;
        padding: 15px 20px;
        border-bottom: none;
    }

    .edu-unit-sidebar-card .card-header h4 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
    }

    .edu-unit-sidebar-card .card-body {
        padding: 20px;
    }

    .edu-unit-contact-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .edu-unit-contact-list li {
        display: flex;
        margin-bottom: 15px;
    }

    .edu-unit-contact-list li:last-child {
        margin-bottom: 0;
    }

    .edu-unit-contact-list li i {
        color: #198754;
        font-size: 18px;
        margin-right: 15px;
        margin-top: 3px;
        width: 20px;
        text-align: center;
    }

    .edu-unit-contact-list li div {
        flex: 1;
    }

    .edu-unit-contact-list li strong {
        display: block;
        font-size: 14px;
        margin-bottom: 3px;
        color: #555;
    }

    .edu-unit-contact-list li p {
        margin: 0;
        color: #333;
        font-size: 15px;
    }

    .edu-unit-contact-list li a {
        color: #198754;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .edu-unit-contact-list li a:hover {
        color: #0d6efd;
        text-decoration: underline;
    }

    /* Related Education Units */
    .edu-unit-related-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .edu-unit-related-list li {
        border-bottom: 1px solid #eee;
    }

    .edu-unit-related-list li:last-child {
        border-bottom: none;
    }

    .edu-unit-related-item {
        display: flex;
        padding: 15px 20px;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
    }

    .edu-unit-related-item:hover {
        background-color: #f8f9fa;
    }

    .related-item-image {
        width: 70px;
        height: 70px;
        border-radius: 5px;
        overflow: hidden;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .related-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .related-item-info {
        flex: 1;
    }

    .related-item-info h5 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 5px;
        color: #333;
        transition: all 0.3s ease;
    }

    .edu-unit-related-item:hover .related-item-info h5 {
        color: #198754;
    }

    .related-item-info p {
        font-size: 14px;
        color: #666;
        margin: 0;
    }

    /* Curriculum Styles */
    .curriculum-card {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .curriculum-header {
        background-color: #198754;
        color: #fff;
        padding: 15px 20px;
        border-bottom: none;
    }

    .curriculum-header h3 {
        margin: 0 0 5px 0;
        font-size: 18px;
        font-weight: 600;
    }

    .curriculum-header p {
        opacity: 0.8;
        font-size: 12px;
    }

    .curriculum-body {
        flex: 1;
        overflow: auto;
        max-height: 600px;
    }

    .accordion-button {
        padding: 15px 20px;
        font-weight: 600;
        font-size: 15px;
        color: #333;
        background-color: #f8f9fa;
    }

    .accordion-button:not(.collapsed) {
        color: #198754;
        background-color: #f0f9f4;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: rgba(25, 135, 84, 0.1);
    }

    .accordion-button::after {
        background-size: 16px;
        width: 16px;
        height: 16px;
    }

    .accordion-body {
        padding: 15px 20px;
        background-color: #fff;
    }

    .curriculum-item-info {
        font-size: 14px;
    }

    .curriculum-detail-item {
        margin-bottom: 15px;
    }

    .curriculum-detail-item:last-child {
        margin-bottom: 0;
    }

    .curriculum-detail-item strong {
        display: block;
        color: #198754;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .curriculum-detail-item p {
        margin: 0;
        color: #555;
        line-height: 1.6;
        white-space: pre-line;
    }

    .curriculum-detail-item ul {
        padding-left: 20px;
        margin-top: 5px;
        margin-bottom: 0;
    }

    .curriculum-detail-item ul li {
        margin-bottom: 5px;
    }

    /* Principal Styles */
    .principal-image-container {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .principal-image-container img {
        width: 100%;
        aspect-ratio: 4/5;
        object-fit: cover;
    }

    .principal-name {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        text-align: center;
        margin-top: 15px;
    }

    .principal-section {
        margin-bottom: 25px;
    }

    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #198754;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
    }

    .section-content {
        font-size: 15px;
        line-height: 1.7;
        color: #555;
        white-space: pre-line;
    }

    .section-content ul {
        padding-left: 20px;
        margin-top: 10px;
    }

    .section-content ul li {
        margin-bottom: 8px;
    }

    /* Tab Styles */
    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
    }

    .nav-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        border-radius: 0;
        padding: 10px 20px;
        font-weight: 600;
        color: #555;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: #198754;
    }

    .nav-tabs .nav-link.active {
        color: #198754;
        background-color: transparent;
        border-bottom-color: #198754;
    }

    /* Responsive Styles */
    @media (max-width: 991.98px) {
        /* Note: .sticky-sidebar is now handled in custom.css */

        .edu-unit-detail-content {
            padding: 0 20px;
        }

        .edu-unit-detail-content h2 {
            font-size: 24px;
        }
    }

    @media (max-width: 767.98px) {
        .edu-unit-detail-image img {
            max-height: 300px;
        }

        .edu-unit-detail-level {
            padding: 8px 15px;
            font-size: 14px;
        }

        .edu-unit-detail-content {
            padding: 0 15px;
        }

        .edu-unit-detail-content h2 {
            font-size: 22px;
        }

        .edu-unit-detail-desc,
        .edu-unit-detail-facilities,
        .edu-unit-detail-curriculum {
            font-size: 15px;
            line-height: 1.7;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations
        const fadeElements = document.querySelectorAll('.fade-in');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        fadeElements.forEach(element => {
            observer.observe(element);
        });

        // Initialize tab animations
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');

        tabLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Get the target tab content
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                // Add a slight delay to allow the Bootstrap tab transition to complete
                setTimeout(() => {
                    // Trigger animations for elements inside the active tab
                    const animElements = targetPane.querySelectorAll('h3, p, ul, img');
                    animElements.forEach((element, index) => {
                        // Add staggered animation delay
                        element.style.opacity = '0';
                        element.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            element.style.transition = 'all 0.5s ease';
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }, 150);
            });
        });

        // Sticky sidebar is now handled by the sticky-sidebar.js file
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/education_units/show.blade.php ENDPATH**/ ?>