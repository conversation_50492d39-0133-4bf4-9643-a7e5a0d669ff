<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('literature', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('title_en');
            $table->text('content');
            $table->text('content_en');
            $table->string('author')->nullable();
            $table->string('author_en')->nullable();
            $table->integer('year')->nullable();
            $table->string('type')->nullable();
            $table->string('type_en')->nullable();
            $table->string('image')->nullable();
            $table->string('file')->nullable();
            $table->foreignId('category_id')->nullable()->constrained('artwork_categories')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('literature');
    }
};
