@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en)

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('achievements') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Achievement Detail -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Achievement Card -->
                    <div class="card shadow-sm mb-4 fade-in">
                        <div class="achievement-image-wrapper">
                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($achievement->image, 'achievement') }}" class="card-img-top achievement-detail-image" alt="{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}">
                        </div>
                        <div class="card-body">
                            <div class="achievement-meta mb-4">
                                <div class="row">
                                    <div class="col-md-6 mb-3 mb-md-0">
                                        <div class="d-flex align-items-center">
                                            <div class="achievement-icon">
                                                <i class="far fa-calendar-alt"></i>
                                            </div>
                                            <div class="ms-2">
                                                <h6 class="mb-0">{{ app()->getLocale() == 'id' ? 'Tanggal' : 'Date' }}</h6>
                                                <p class="mb-0">{{ $achievement->achievement_date ? $achievement->achievement_date->format('d F Y') : '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center">
                                            <div class="achievement-icon">
                                                <i class="fas fa-award"></i>
                                            </div>
                                            <div class="ms-2">
                                                <h6 class="mb-0">{{ app()->getLocale() == 'id' ? 'Diberikan Oleh' : 'Awarded By' }}</h6>
                                                <p class="mb-0">{{ app()->getLocale() == 'id' ? $achievement->award_by : $achievement->award_by_en }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="achievement-content">
                                <h4 class="card-title mb-4">{{ app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en }}</h4>
                                <div class="card-text achievement-description">
                                    {!! app()->getLocale() == 'id' ? $achievement->description : $achievement->description_en !!}
                                </div>
                            </div>

                            <!-- Social Share -->
                            <div class="social-share mt-4 pt-4 border-top">
                                <h5 class="mb-3">{{ app()->getLocale() == 'id' ? 'Bagikan' : 'Share' }}</h5>
                                <div class="d-flex">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" target="_blank" class="btn btn-outline-primary me-2">
                                        <i class="fab fa-facebook-f"></i>
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->url()) }}&text={{ urlencode(app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en) }}" target="_blank" class="btn btn-outline-info me-2">
                                        <i class="fab fa-twitter"></i>
                                    </a>
                                    <a href="https://wa.me/?text={{ urlencode((app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en) . ' ' . request()->url()) }}" target="_blank" class="btn btn-outline-success me-2">
                                        <i class="fab fa-whatsapp"></i>
                                    </a>
                                    <a href="mailto:?subject={{ urlencode(app()->getLocale() == 'id' ? $achievement->title : $achievement->title_en) }}&body={{ urlencode(request()->url()) }}" class="btn btn-outline-secondary">
                                        <i class="far fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Related Achievements -->
                    @if($relatedAchievements->isNotEmpty())
                    <div class="related-achievements fade-in">
                        <h4 class="section-title">{{ app()->getLocale() == 'id' ? 'Prestasi Terkait' : 'Related Achievements' }}</h4>
                        <div class="section-divider"></div>

                        <div class="row">
                            @foreach($relatedAchievements as $relatedAchievement)
                            <div class="col-md-4 mb-4">
                                <div class="card h-100 shadow-sm achievement-card fade-in">
                                    <div class="achievement-image-container">
                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($relatedAchievement->image, 'achievement') }}" class="card-img-top achievement-image" alt="{{ app()->getLocale() == 'id' ? $relatedAchievement->title : $relatedAchievement->title_en }}">
                                    </div>
                                    <div class="card-body">
                                        <div class="achievement-date mb-2">
                                            <i class="far fa-calendar-alt me-1"></i> {{ $relatedAchievement->achievement_date ? $relatedAchievement->achievement_date->format('d M Y') : '-' }}
                                        </div>
                                        <h5 class="card-title">{{ app()->getLocale() == 'id' ? $relatedAchievement->title : $relatedAchievement->title_en }}</h5>
                                        <a href="{{ route('achievements.show', $relatedAchievement->id) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Detail' : 'View Details' }}</a>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>

                <div class="col-lg-4">
                    <div class="achievements-sidebar">
                        <!-- Latest News -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">{{ app()->getLocale() == 'id' ? 'Berita Terbaru' : 'Latest News' }}</h5>
                            </div>
                            <div class="card-body">
                                @if($latestNews->isEmpty())
                                    <p class="text-muted">{{ app()->getLocale() == 'id' ? 'Belum ada berita yang tersedia.' : 'No news available yet.' }}</p>
                                @else
                                    <ul class="list-group list-group-flush">
                                        @foreach($latestNews as $news)
                                            <li class="list-group-item px-0">
                                                <div class="d-flex">
                                                    <div class="flex-shrink-0 me-3">
                                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($news->thumbnail, 'news') }}" alt="{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}" class="rounded" width="60">
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1"><a href="{{ route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}</a></h6>
                                                        <small class="text-muted"><i class="far fa-calendar-alt me-1"></i> {{ $news->published_at->format('d M Y') }}</small>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                    <div class="text-center mt-3">
                                        <a href="{{ route('news') }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Semua Berita' : 'View All News' }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Latest Announcements -->
                        <div class="card shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">{{ app()->getLocale() == 'id' ? 'Pengumuman Terbaru' : 'Latest Announcements' }}</h5>
                            </div>
                            <div class="card-body">
                                @if($latestAnnouncements->isEmpty())
                                    <p class="text-muted">{{ app()->getLocale() == 'id' ? 'Belum ada pengumuman yang tersedia.' : 'No announcements available yet.' }}</p>
                                @else
                                    <ul class="list-group list-group-flush">
                                        @foreach($latestAnnouncements as $announcement)
                                            <li class="list-group-item px-0">
                                                <h6 class="mb-1"><a href="{{ route('announcements.show', $announcement->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en }}</a></h6>
                                                <small class="text-muted"><i class="far fa-calendar-alt me-1"></i> {{ $announcement->start_date->format('d M Y') }} - {{ $announcement->end_date->format('d M Y') }}</small>
                                            </li>
                                        @endforeach
                                    </ul>
                                    <div class="text-center mt-3">
                                        <a href="{{ route('announcements') }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Lihat Semua Pengumuman' : 'View All Announcements' }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- CTA Card -->
                        <div class="card border-0 bg-success text-white shadow-sm mt-4">
                            <div class="card-body p-4 text-center">
                                <h5 class="mb-3">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</h5>
                                <p>{{ app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family.' }}</p>
                                <a href="{{ route('registration') }}" class="btn btn-light">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    /* Achievement Detail Styling */
    .achievement-image-wrapper {
        height: 400px;
        overflow: hidden;
    }

    .achievement-detail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .achievement-meta {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
    }

    .achievement-icon {
        width: 40px;
        height: 40px;
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .achievement-description {
        line-height: 1.8;
    }

    .achievement-description p {
        margin-bottom: 1.5rem;
    }

    /* Support for formatted content */
    .achievement-description img {
        max-width: 100%;
        height: auto;
        margin: 1rem 0;
    }

    .achievement-description ul,
    .achievement-description ol {
        margin-bottom: 1.5rem;
        padding-left: 2rem;
    }

    .achievement-description table {
        width: 100%;
        margin-bottom: 1.5rem;
        border-collapse: collapse;
    }

    .achievement-description table td,
    .achievement-description table th {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .achievement-description blockquote {
        padding: 1rem;
        margin: 1.5rem 0;
        border-left: 4px solid #198754;
        background-color: rgba(25, 135, 84, 0.05);
    }

    /* Related Achievement Cards */
    .achievement-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        border-radius: 8px;
    }

    .achievement-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .achievement-image-container {
        height: 150px;
        overflow: hidden;
    }

    .achievement-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .achievement-card:hover .achievement-image {
        transform: scale(1.05);
    }

    /* Section Styling */
    .section-title {
        position: relative;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .section-divider {
        width: 50px;
        height: 3px;
        background-color: #198754;
        margin-bottom: 2rem;
    }

    /* Social Share */
    .social-share .btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .social-share .btn:hover {
        transform: translateY(-3px);
    }

    /* Sidebar Styling */
    .achievements-sidebar {
        position: sticky;
        top: 2rem;
        z-index: 10;
    }

    .achievements-sidebar .card {
        margin-bottom: 1.5rem;
    }

    .achievements-sidebar .card-header {
        padding: 0.75rem 1.25rem;
    }

    .achievements-sidebar .list-group-item {
        border-left: none;
        border-right: none;
        padding: 0.75rem 0;
    }

    .achievements-sidebar .list-group-item:first-child {
        border-top: none;
    }

    .achievements-sidebar .list-group-item:last-child {
        border-bottom: none;
    }

    /* Section Padding */
    .section-padding {
        padding: 80px 0;
    }

    /* Responsive adjustments */
    @media (max-width: 991.98px) {
        .achievements-sidebar {
            position: relative;
            top: 0;
        }

        .achievement-image-wrapper {
            height: 300px;
        }

        .section-padding {
            padding: 60px 0;
        }
    }

    @media (max-width: 767.98px) {
        .achievement-image-wrapper {
            height: 250px;
        }

        .achievement-meta {
            padding: 1rem;
        }

        .section-padding {
            padding: 40px 0;
        }
    }
</style>
@endpush
