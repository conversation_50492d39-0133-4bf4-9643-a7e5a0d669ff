<?php

namespace App\Http\Controllers\Api;

use App\Models\Facility;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class FacilityController extends BaseApiController
{
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        parent::__construct($webhookService);
        $this->model = new Facility();
        $this->modelName = 'facility';
        
        $this->storeRules = [
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'required|string',
            'description_en' => 'required|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ];
        
        $this->updateRules = [
            'name' => 'sometimes|required|string|max:255',
            'name_en' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'description_en' => 'sometimes|required|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Display a listing of the facilities.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $facilities = Facility::active()->get();
        
        return response()->json([
            'success' => true,
            'data' => $facilities,
        ]);
    }

    /**
     * Display the specified facility.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $facility = Facility::where('is_active', true)
            ->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'data' => $facility,
        ]);
    }

    /**
     * Store a newly created facility in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->storeRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Facility::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = $data['is_active'] ?? true;

        // Handle image upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('facilities', $fileName, 'public');
            $data['image'] = $filePath;
        }

        $facility = Facility::create($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('facility.created', $facility->toArray());

        return response()->json([
            'success' => true,
            'message' => 'Facility created successfully',
            'data' => $facility,
        ], 201);
    }

    /**
     * Update the specified facility in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $facility = Facility::findOrFail($id);

        $validator = Validator::make($request->all(), $this->updateRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($facility->image) {
                Storage::disk('public')->delete($facility->image);
            }

            // Upload new image
            $file = $request->file('image');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('facilities', $fileName, 'public');
            $data['image'] = $filePath;
        }

        $facility->update($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('facility.updated', $facility->toArray());

        return response()->json([
            'success' => true,
            'message' => 'Facility updated successfully',
            'data' => $facility,
        ]);
    }

    /**
     * Remove the specified facility from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $facility = Facility::findOrFail($id);
        
        // Delete image if exists
        if ($facility->image) {
            Storage::disk('public')->delete($facility->image);
        }
        
        $facility->delete();

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('facility.deleted', ['id' => $id]);

        return response()->json([
            'success' => true,
            'message' => 'Facility deleted successfully',
        ]);
    }
}
