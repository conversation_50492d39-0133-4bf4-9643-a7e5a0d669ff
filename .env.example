APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Default Mail Configuration
MAIL_MAILER=default
MAIL_SCHEME=smtps
MAIL_HOST=mail.nurulhayah.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Informasi NURUL HAYAH 4"

# Password Reset Mail Configuration
MAIL_PASSWORD_RESET_USERNAME=<EMAIL>
MAIL_PASSWORD_RESET_PASSWORD=
MAIL_PASSWORD_RESET_FROM_ADDRESS="<EMAIL>"
MAIL_PASSWORD_RESET_FROM_NAME="ADMINISTRATOR"

# Other mail configurations will use the default settings unless overridden
# MAIL_NOTIFICATIONS_USERNAME=
# MAIL_CONFIRMATIONS_USERNAME=
# MAIL_TRANSACTIONAL_USERNAME=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# MyMemory Translation API (Free)
MYMEMORY_TRANSLATE_EMAIL=<EMAIL>
