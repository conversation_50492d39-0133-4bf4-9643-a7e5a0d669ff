<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Services\ImageService;

class FacilityController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the facilities.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $facilities = \App\Models\Facility::all();
        return view('admin.facilities.index', compact('facilities'));
    }

    /**
     * Show the form for creating a new facility.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.facilities.create');
    }

    /**
     * Store a newly created facility in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'required|string',
            'description_en' => 'required|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->all();

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'facilities',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        \App\Models\Facility::create($data);

        return redirect()->route('admin.facilities.index')
            ->with('success', 'Facility created successfully.');
    }

    /**
     * Display the specified facility.
     *
     * @param  \App\Models\Facility  $facility
     * @return \Illuminate\View\View
     */
    public function show(\App\Models\Facility $facility)
    {
        return view('admin.facilities.show', compact('facility'));
    }

    /**
     * Show the form for editing the specified facility.
     *
     * @param  \App\Models\Facility  $facility
     * @return \Illuminate\View\View
     */
    public function edit(\App\Models\Facility $facility)
    {
        return view('admin.facilities.edit', compact('facility'));
    }

    /**
     * Update the specified facility in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Facility  $facility
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, \App\Models\Facility $facility)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'required|string',
            'description_en' => 'required|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->all();

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($facility->image) {
                $this->imageService->deleteImage($facility->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'facilities',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $facility->update($data);

        return redirect()->route('admin.facilities.index')
            ->with('success', 'Facility updated successfully.');
    }

    /**
     * Remove the specified facility from storage.
     *
     * @param  \App\Models\Facility  $facility
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(\App\Models\Facility $facility)
    {
        // Delete associated image
        if ($facility->image) {
            $this->imageService->deleteImage($facility->image);
        }

        $facility->delete();

        return redirect()->route('admin.facilities.index')
            ->with('success', 'Facility deleted successfully.');
    }
}
