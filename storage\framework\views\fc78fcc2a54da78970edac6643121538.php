<?php $__env->startSection('title', 'View News Category'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View News Category</h1>
        <div>
            <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Category Details</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 200px;">ID</th>
                            <td><?php echo e($category->id); ?></td>
                        </tr>
                        <tr>
                            <th>Name (Indonesian)</th>
                            <td><?php echo e($category->name); ?></td>
                        </tr>
                        <tr>
                            <th>Name (English)</th>
                            <td><?php echo e($category->name_en); ?></td>
                        </tr>
                        <tr>
                            <th>Slug (Indonesian)</th>
                            <td><?php echo e($category->slug); ?></td>
                        </tr>
                        <tr>
                            <th>Slug (English)</th>
                            <td><?php echo e($category->slug_en); ?></td>
                        </tr>
                        <tr>
                            <th>Description (Indonesian)</th>
                            <td><?php echo e($category->description); ?></td>
                        </tr>
                        <tr>
                            <th>Description (English)</th>
                            <td><?php echo e($category->description_en); ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php if($category->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td><?php echo e($category->created_at->format('d M Y H:i:s')); ?></td>
                        </tr>
                        <tr>
                            <th>Updated At</th>
                            <td><?php echo e($category->updated_at->format('d M Y H:i:s')); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">News in this Category</h5>
                </div>
                <div class="card-body">
                    <?php if($category->news->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Published Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $category->news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($news->id); ?></td>
                                            <td><?php echo e($news->title); ?></td>
                                            <td><?php echo e($news->published_at->format('d M Y')); ?></td>
                                            <td>
                                                <a href="<?php echo e(route('admin.news.show', $news)); ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No news articles in this category yet.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="<?php echo e(route('admin.categories.edit', $category)); ?>" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Category
                    </a>
                    
                    <form action="<?php echo e(route('admin.categories.destroy', $category)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this category?')">
                            <i class="fas fa-trash me-1"></i> Delete Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/news_categories/show.blade.php ENDPATH**/ ?>