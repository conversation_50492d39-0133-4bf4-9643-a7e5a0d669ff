<?php

namespace App\Console\Commands;

use App\Models\Setting;
use Illuminate\Console\Command;

class TelegramSetupConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:setup-config {bot_token?} {chat_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up Telegram configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up Telegram configuration...');

        // Get bot token
        $botToken = $this->argument('bot_token');
        if (!$botToken) {
            $botToken = $this->ask('Enter your Telegram bot token (from BotFather)');
        }

        // Get chat ID
        $chatId = $this->argument('chat_id');
        if (!$chatId) {
            $chatId = $this->ask('Enter your Telegram chat ID (where notifications will be sent)');
        }

        // Save settings
        Setting::updateOrCreate(['key' => 'telegram_bot_token'], ['value' => $botToken, 'group' => 'telegram']);
        Setting::updateOrCreate(['key' => 'telegram_chat_id'], ['value' => $chatId, 'group' => 'telegram']);
        Setting::updateOrCreate(['key' => 'telegram_notify_contacts'], ['value' => '1', 'group' => 'telegram']);
        Setting::updateOrCreate(['key' => 'telegram_notify_registrations'], ['value' => '1', 'group' => 'telegram']);
        Setting::updateOrCreate(['key' => 'telegram_notify_announcements'], ['value' => '1', 'group' => 'telegram']);
        Setting::updateOrCreate(['key' => 'telegram_notifications_enabled'], ['value' => '1', 'group' => 'telegram']);

        $this->info('Telegram configuration saved successfully!');

        // Set webhook
        if ($this->confirm('Do you want to set up the webhook now?', true)) {
            $this->call('telegram:set-webhook');
        }

        // Test configuration
        if ($this->confirm('Do you want to send a test message?', true)) {
            $this->info('Sending test message...');

            $telegramService = new \App\Services\TelegramBotService();
            $result = $telegramService->sendAdminMessage('🔔 <b>Test Message</b>

This is a test message from the Telegram configuration setup command.

If you receive this message, your Telegram bot is configured correctly!');

            if ($result && isset($result['ok']) && $result['ok']) {
                $this->info('Test message sent successfully!');
            } else {
                $this->error('Failed to send test message.');
                $this->error('Response: ' . json_encode($result));
            }
        }

        return 0;
    }
}
