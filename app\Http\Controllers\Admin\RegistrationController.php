<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Registration;
use App\Models\RegistrationSchedule;
use App\Models\RegistrationRequirement;
use App\Models\RegistrationFee;
use App\Models\RegistrationInfo;
use App\Models\EducationUnit;
use App\Exports\RegistrationsExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class RegistrationController extends Controller
{
    /**
     * Display a listing of the registrations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $activeTab = $request->query('tab', 'management');

        // Validate tab parameter
        if (!in_array($activeTab, ['management', 'registration'])) {
            $activeTab = 'management';
        }

        if ($activeTab === 'management') {
            // Get data for management tab
            $schedules = RegistrationSchedule::orderBy('start_date', 'desc')->get();
            $requirements = RegistrationRequirement::orderBy('order', 'asc')->get();
            $fees = RegistrationFee::orderBy('order', 'asc')->get();
            $info = RegistrationInfo::first();

            return view('admin.registrations.management', compact(
                'activeTab',
                'schedules',
                'requirements',
                'fees',
                'info'
            ));
        } else {
            // Get data for registration tab
            $query = Registration::query();

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('education_unit_id')) {
                $query->where('education_unit_id', $request->education_unit_id);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('full_name', 'like', "%{$search}%")
                      ->orWhere('registration_number', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('nik', 'like', "%{$search}%");
                });
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $registrations = $query->latest()->paginate(10)->withQueryString();

            // Get education units for filter dropdown
            $educationUnits = EducationUnit::orderBy('name')->get();

            return view('admin.registrations.index', compact(
                'activeTab',
                'registrations',
                'educationUnits'
            ));
        }
    }

    /**
     * Display the specified registration.
     *
     * @param  \App\Models\Registration  $registration
     * @return \Illuminate\View\View
     */
    public function show(\App\Models\Registration $registration)
    {
        return view('admin.registrations.show', compact('registration'));
    }

    /**
     * Approve the specified registration.
     *
     * @param  \App\Models\Registration  $registration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve(\App\Models\Registration $registration)
    {
        $registration->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => auth()->id(),
        ]);

        // Send notification email to the registrant
        // TODO: Implement email notification

        return redirect()->route('admin.registrations.show', $registration)
            ->with('success', 'Registration approved successfully.');
    }

    /**
     * Reject the specified registration.
     *
     * @param  \App\Models\Registration  $registration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(\App\Models\Registration $registration)
    {
        $registration->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'rejected_by' => auth()->id(),
        ]);

        // Send notification email to the registrant
        // TODO: Implement email notification

        return redirect()->route('admin.registrations.show', $registration)
            ->with('success', 'Registration rejected successfully.');
    }

    /**
     * Remove the specified registration from storage.
     *
     * @param  \App\Models\Registration  $registration
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Registration $registration)
    {
        // Delete associated document if exists
        if ($registration->document) {
            Storage::disk('public')->delete($registration->document);
        }

        $registration->delete();

        return redirect()->route('admin.registrations.index')
            ->with('success', 'Registration deleted successfully.');
    }

    /**
     * Store a new registration schedule.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeSchedule(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        RegistrationSchedule::create([
            'title' => $request->title,
            'title_en' => $request->title_en,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'description' => $request->description,
            'description_en' => $request->description_en,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration schedule created successfully.');
    }

    /**
     * Update the specified registration schedule.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\RegistrationSchedule  $schedule
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSchedule(Request $request, RegistrationSchedule $schedule)
    {

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        $schedule->update([
            'title' => $request->title,
            'title_en' => $request->title_en,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'description' => $request->description,
            'description_en' => $request->description_en,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration schedule updated successfully.');
    }

    /**
     * Remove the specified registration schedule.
     *
     * @param  \App\Models\RegistrationSchedule  $schedule
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroySchedule(RegistrationSchedule $schedule)
    {
        $schedule->delete();

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration schedule deleted successfully.');
    }

    /**
     * Store a new registration requirement.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeRequirement(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        // Set default order if not provided
        if (!$request->filled('order')) {
            $maxOrder = RegistrationRequirement::max('order');
            $order = $maxOrder ? $maxOrder + 1 : 1;
        } else {
            $order = $request->order;
        }

        RegistrationRequirement::create([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'description' => $request->description,
            'description_en' => $request->description_en,
            'order' => $order,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration requirement created successfully.');
    }

    /**
     * Update the specified registration requirement.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\RegistrationRequirement  $requirement
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateRequirement(Request $request, RegistrationRequirement $requirement)
    {

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        $requirement->update([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'description' => $request->description,
            'description_en' => $request->description_en,
            'order' => $request->order,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration requirement updated successfully.');
    }

    /**
     * Remove the specified registration requirement.
     *
     * @param  \App\Models\RegistrationRequirement  $requirement
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyRequirement(RegistrationRequirement $requirement)
    {
        $requirement->delete();

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration requirement deleted successfully.');
    }

    /**
     * Store a new registration fee.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeFee(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        // Set default order if not provided
        if (!$request->filled('order')) {
            $maxOrder = RegistrationFee::max('order');
            $order = $maxOrder ? $maxOrder + 1 : 1;
        } else {
            $order = $request->order;
        }

        RegistrationFee::create([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'amount' => $request->amount,
            'description' => $request->description,
            'description_en' => $request->description_en,
            'order' => $order,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration fee created successfully.');
    }

    /**
     * Update the specified registration fee.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\RegistrationFee  $fee
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateFee(Request $request, RegistrationFee $fee)
    {

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'order' => 'nullable|integer',
        ]);

        $fee->update([
            'name' => $request->name,
            'name_en' => $request->name_en,
            'amount' => $request->amount,
            'description' => $request->description,
            'description_en' => $request->description_en,
            'order' => $request->order,
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration fee updated successfully.');
    }

    /**
     * Remove the specified registration fee.
     *
     * @param  \App\Models\RegistrationFee  $fee
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyFee(RegistrationFee $fee)
    {
        $fee->delete();

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration fee deleted successfully.');
    }

    /**
     * Store or update registration info.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeInfo(Request $request)
    {
        $request->validate([
            'content' => 'required|string',
            'content_en' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $info = RegistrationInfo::first();
        $imagePath = $info ? $info->image : null;

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($imagePath) {
                Storage::disk('public')->delete($imagePath);
            }

            $imagePath = $request->file('image')->store('registration', 'public');
        }

        if ($info) {
            // Update existing info
            $info->update([
                'content' => $request->content,
                'content_en' => $request->content_en,
                'image' => $imagePath,
                'is_active' => $request->has('is_active'),
            ]);
        } else {
            // Create new info
            RegistrationInfo::create([
                'content' => $request->content,
                'content_en' => $request->content_en,
                'image' => $imagePath,
                'is_active' => $request->has('is_active'),
            ]);
        }

        return redirect()->route('admin.registrations.index', ['tab' => 'management'])
            ->with('success', 'Registration information updated successfully.');
    }

    /**
     * Export registrations to Excel.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        // Get filters from request
        $filters = [
            'status' => $request->query('status'),
            'education_unit_id' => $request->query('education_unit_id'),
            'search' => $request->query('search'),
        ];

        // Generate filename with current date
        $filename = 'data_pendaftaran_' . date('Y-m-d_His') . '.xlsx';

        // Return the Excel file as download
        return Excel::download(new RegistrationsExport(null, $filters), $filename);
    }
}
