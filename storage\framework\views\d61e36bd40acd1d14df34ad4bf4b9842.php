<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Berita' : 'News'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Berita & Artikel' : 'News & Articles'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Berita' : 'News'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- News List -->
    <section class="section-padding">
        <div class="container">
            <?php if(isset($categories) && $categories->count() > 0): ?>
                <!-- Category Tabs -->
                <ul class="nav nav-tabs mb-4" id="newsCategoryTabs" role="tablist">
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link <?php echo e($index === 0 ? 'active' : ''); ?>"
                                id="category-<?php echo e($category->id); ?>-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#category-<?php echo e($category->id); ?>"
                                type="button"
                                role="tab"
                                aria-controls="category-<?php echo e($category->id); ?>"
                                aria-selected="<?php echo e($index === 0 ? 'true' : 'false'); ?>">
                                <?php echo e(app()->getLocale() == 'id' ? $category->name : $category->name_en); ?>

                            </button>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="newsCategoryTabsContent">
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="tab-pane fade <?php echo e($index === 0 ? 'show active' : ''); ?>"
                            id="category-<?php echo e($category->id); ?>"
                            role="tabpanel"
                            aria-labelledby="category-<?php echo e($category->id); ?>-tab">

                            <div class="row">
                                <?php $__empty_1 = true; $__currentLoopData = $newsByCategory[$category->id]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <div class="col-lg-4 col-md-6 mb-4 fade-in">
                                        <div class="news-card card h-100">
                                            <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($item->thumbnail, 'news')); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $item->title : $item->title_en); ?>">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div class="news-date">
                                                        <i class="far fa-calendar-alt me-1"></i> <?php echo e($item->published_at->format('d M Y')); ?>

                                                    </div>
                                                    <div class="news-category">
                                                        <span class="badge bg-success">
                                                            <?php echo e(app()->getLocale() == 'id' ? $category->name : $category->name_en); ?>

                                                        </span>
                                                    </div>
                                                </div>
                                                <h5 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $item->title : $item->title_en); ?></h5>
                                                <p class="card-text">
                                                    <?php
                                                        $content = app()->getLocale() == 'id' ? $item->content : $item->content_en;
                                                        $plainText = strip_tags($content);
                                                        // Hapus karakter HTML entities seperti &nbsp;
                                                        $plainText = html_entity_decode($plainText);
                                                        // Hapus whitespace berlebih
                                                        $plainText = preg_replace('/\s+/', ' ', $plainText);
                                                        $plainText = trim($plainText);
                                                        $excerpt = \Illuminate\Support\Str::limit($plainText, 150);
                                                    ?>
                                                    <?php echo e($excerpt); ?>

                                                </p>
                                                <a href="<?php echo e(route('news.show', app()->getLocale() == 'id' ? $item->slug : $item->slug_en)); ?>" class="btn btn-sm btn-outline-success"><?php echo e(app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More'); ?></a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <div class="col-12 text-center">
                                        <div class="alert alert-info">
                                            <?php echo e(app()->getLocale() == 'id' ? 'Belum ada berita dalam kategori ini.' : 'No news available in this category yet.'); ?>

                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-center mt-4">
                                <?php echo e($newsByCategory[$category->id]->links()); ?>

                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <!-- Fallback if no categories exist -->
                <div class="row">
                    <?php $__empty_1 = true; $__currentLoopData = $news; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="col-lg-4 col-md-6 mb-4 fade-in">
                            <div class="news-card card h-100">
                                <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($item->thumbnail, 'news')); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $item->title : $item->title_en); ?>">
                                <div class="card-body">
                                    <div class="news-date mb-2">
                                        <i class="far fa-calendar-alt me-1"></i> <?php echo e($item->published_at->format('d M Y')); ?>

                                    </div>
                                    <h5 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $item->title : $item->title_en); ?></h5>
                                    <p class="card-text">
                                        <?php
                                            $content = app()->getLocale() == 'id' ? $item->content : $item->content_en;
                                            $plainText = strip_tags($content);
                                            // Hapus karakter HTML entities seperti &nbsp;
                                            $plainText = html_entity_decode($plainText);
                                            // Hapus whitespace berlebih
                                            $plainText = preg_replace('/\s+/', ' ', $plainText);
                                            $plainText = trim($plainText);
                                            $excerpt = \Illuminate\Support\Str::limit($plainText, 150);
                                        ?>
                                        <?php echo e($excerpt); ?>

                                    </p>
                                    <a href="<?php echo e(route('news.show', app()->getLocale() == 'id' ? $item->slug : $item->slug_en)); ?>" class="btn btn-sm btn-outline-success"><?php echo e(app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More'); ?></a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="col-12 text-center">
                            <div class="alert alert-info">
                                <?php echo e(app()->getLocale() == 'id' ? 'Belum ada berita yang tersedia.' : 'No news available yet.'); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if(isset($news)): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($news->links()); ?>

                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/news/index.blade.php ENDPATH**/ ?>