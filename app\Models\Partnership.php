<?php

namespace App\Models;

use App\Services\CountryService;
use Illuminate\Database\Eloquent\Model;

class Partnership extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'description',
        'description_en',
        'website',
        'logo',
        'partnership_since',
        'country_code',
        'is_active',
    ];

    protected $casts = [
        'partnership_since' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active partnerships.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->orderBy('name', 'asc');
    }

    /**
     * Get the country flag emoji.
     *
     * @return string|null
     */
    public function getCountryFlagAttribute()
    {
        if (!$this->country_code) {
            return null;
        }

        $countryService = app(CountryService::class);
        return $countryService->getFlag($this->country_code);
    }

    /**
     * Get the country name.
     *
     * @return string|null
     */
    public function getCountryNameAttribute()
    {
        if (!$this->country_code) {
            return null;
        }

        $countryService = app(CountryService::class);
        $country = $countryService->getCountry($this->country_code);
        return $country ? $country['name'] : null;
    }

    /**
     * Get the country flag HTML.
     *
     * @return string|null
     */
    public function getFlagHtmlAttribute()
    {
        if (!$this->country_code) {
            return null;
        }

        $countryService = app(CountryService::class);
        return $countryService->getFlagHtml($this->country_code);
    }
}
