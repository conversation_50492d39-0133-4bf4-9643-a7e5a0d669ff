<?php $__env->startSection('title', 'Activity Schedule'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .nav-tabs .nav-link {
        color: #6c757d;
    }
    .nav-tabs .nav-link.active {
        font-weight: 600;
        color: #0d6efd;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Activity Schedule</h1>
        <a href="<?php echo e(route('admin.schedules.create')); ?>" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link <?php echo e($activeTab == 'daily' ? 'active' : ''); ?>" href="<?php echo e(route('admin.schedules.index', ['tab' => 'daily'])); ?>">
                        Daily
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e($activeTab == 'weekly' ? 'active' : ''); ?>" href="<?php echo e(route('admin.schedules.index', ['tab' => 'weekly'])); ?>">
                        Weekly
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e($activeTab == 'monthly' ? 'active' : ''); ?>" href="<?php echo e(route('admin.schedules.index', ['tab' => 'monthly'])); ?>">
                        Monthly
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e($activeTab == 'yearly' ? 'active' : ''); ?>" href="<?php echo e(route('admin.schedules.index', ['tab' => 'yearly'])); ?>">
                        Annual
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <?php if($activeTab == 'daily'): ?>
                                    Daily
                                <?php elseif($activeTab == 'weekly'): ?>
                                    Day
                                <?php elseif($activeTab == 'monthly'): ?>
                                    Week
                                <?php elseif($activeTab == 'yearly'): ?>
                                    Month
                                <?php endif; ?>
                            </th>
                            <th>Time</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php if($schedule->activity_type == 'daily'): ?>
                                        <span>Daily</span>
                                    <?php elseif($schedule->activity_type == 'weekly'): ?>
                                        <span><?php echo e($schedule->day_of_week); ?></span>
                                    <?php elseif($schedule->activity_type == 'monthly'): ?>
                                        <span>Week <?php echo e($schedule->week_number); ?></span>
                                    <?php elseif($schedule->activity_type == 'yearly'): ?>
                                        <span><?php echo e($schedule->month_name); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($schedule->start_time->format('H:i')); ?> - <?php echo e($schedule->end_time->format('H:i')); ?></td>
                                <td><?php echo e($schedule->title); ?></td>
                                <td>
                                    <?php if($schedule->category): ?>
                                        <span class="badge bg-info"><?php echo e(ucfirst($schedule->category)); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($schedule->is_active): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('admin.schedules.show', $schedule)); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.schedules.edit', $schedule)); ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.schedules.destroy', ['schedule' => $schedule, 'tab' => $activeTab])); ?>" method="POST" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this schedule?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="text-center">
                                    <?php if($activeTab == 'daily'): ?>
                                        No daily schedules found.
                                    <?php elseif($activeTab == 'weekly'): ?>
                                        No weekly schedules found.
                                    <?php elseif($activeTab == 'monthly'): ?>
                                        No monthly schedules found.
                                    <?php elseif($activeTab == 'yearly'): ?>
                                        No annual schedules found.
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/schedules/index.blade.php ENDPATH**/ ?>