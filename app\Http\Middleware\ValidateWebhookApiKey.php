<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Webhook;
use Illuminate\Support\Facades\Log;

class ValidateWebhookApiKey
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $apiKey = $request->header('X-API-Key') ?? $request->query('api_key');

        if (empty($apiKey)) {
            Log::warning('Webhook API request without API key', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'API key is required',
            ], 401);
        }

        // Check if the API key exists in the database
        $webhook = Webhook::where('api_key', $apiKey)->first();

        if (!$webhook) {
            Log::warning('Webhook API request with invalid API key', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'api_key' => $apiKey,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Invalid API key',
            ], 401);
        }

        // Store the webhook in the request for later use
        $request->attributes->add(['webhook' => $webhook]);

        return $next($request);
    }
}
