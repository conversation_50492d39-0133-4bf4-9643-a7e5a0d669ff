document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a detail page with a sidebar
    const sidebar = document.querySelector('.sticky-sidebar');
    const mainContent = document.querySelector('.main-content');

    // Function to check if we're on mobile - menggunakan breakpoint yang sama dengan proyek lama
    function isMobile() {
        return window.innerWidth < 768;
    }

    // Only apply JavaScript enhancements if we have both elements and not on mobile
    if (sidebar && mainContent && !isMobile()) {
        // Implementasi sticky sidebar seperti di proyek lama
        let lastScrollTop = 0;
        let ticking = false;
        let sidebarHeight = sidebar.offsetHeight;
        let mainContentHeight = mainContent.offsetHeight;
        let sidebarTop = sidebar.getBoundingClientRect().top + window.pageYOffset;

        // Function to update sidebar position
        function updateSidebar() {
            // Recalculate heights in case of dynamic content
            sidebarHeight = sidebar.offsetHeight;
            mainContentHeight = mainContent.offsetHeight;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;

            // Implementasi dari proyek lama
            // ...
        }

        // Event listener for scroll
        window.addEventListener('scroll', function() {
            lastScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (!ticking) {
                window.requestAnimationFrame(function() {
                    updateSidebar();
                    ticking = false;
                });

                ticking = true;
            }
        });

        // Event listener for resize
        window.addEventListener('resize', function() {
            // If switched to mobile view, reset sidebar styles
            if (isMobile()) {
                sidebar.style.position = 'static';
                sidebar.style.top = '0';
                sidebar.style.width = '100%';
                sidebar.style.transform = 'none';
                return;
            }

            // Recalculate dimensions
            sidebarHeight = sidebar.offsetHeight;
            mainContentHeight = mainContent.offsetHeight;
            sidebarTop = sidebar.getBoundingClientRect().top + window.pageYOffset;
            sidebar.style.width = sidebar.parentElement.offsetWidth + 'px';

            updateSidebar();
        });

        // Initial call
        updateSidebar();
    }
});

