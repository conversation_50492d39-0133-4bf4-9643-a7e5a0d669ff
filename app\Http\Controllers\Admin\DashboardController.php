<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Auth middleware is now applied in routes
    }

    /**
     * Display the dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get counts for dashboard
        $newsCount = \App\Models\News::count();
        $galleryCount = \App\Models\Gallery::count();
        $pendingRegistrations = \App\Models\Registration::where('status', 'pending')->count();
        $unreadMessages = \App\Models\Contact::where('is_read', false)->count();

        // Get latest registrations
        $latestRegistrations = \App\Models\Registration::latest()->take(5)->get();

        // Get latest messages
        $latestMessages = \App\Models\Contact::latest()->take(5)->get();

        return view('admin.dashboard', compact(
            'newsCount',
            'galleryCount',
            'pendingRegistrations',
            'unreadMessages',
            'latestRegistrations',
            'latestMessages'
        ));
    }
}
