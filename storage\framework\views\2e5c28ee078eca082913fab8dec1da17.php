<?php $__env->startSection('title', 'Paints'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Paints</h1>
        <a href="<?php echo e(route('admin.artwork.paints.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Paint
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if($paints->isEmpty()): ?>
                <div class="alert alert-info">
                    No paints found. <a href="<?php echo e(route('admin.artwork.paints.create')); ?>">Create your first paint</a>.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Artist</th>
                                <th>Year</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $paints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $paint): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php if($paint->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $paint->image)); ?>" alt="<?php echo e($paint->title); ?>" class="img-thumbnail" style="max-height: 50px;">
                                        <?php else: ?>
                                            <span class="text-muted">No image</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($paint->title); ?></td>
                                    <td><?php echo e($paint->artist); ?></td>
                                    <td><?php echo e($paint->year ?: '-'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.artwork.paints.edit', $paint->id)); ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <form action="<?php echo e(route('admin.artwork.paints.destroy', $paint->id)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this paint?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    <?php echo e($paints->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/artwork/paints/index.blade.php ENDPATH**/ ?>