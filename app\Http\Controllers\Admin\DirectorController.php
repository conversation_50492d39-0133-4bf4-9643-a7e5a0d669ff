<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Director;
use App\Services\ImageService;
use Illuminate\Http\Request;

class DirectorController extends Controller
{
    /**
     * The image service instance.
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $directors = Director::orderBy('order', 'asc')->get();
        return view('admin.directors.index', compact('directors'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function create()
    {
        // Check if a director already exists
        if (Director::count() > 0) {
            return redirect()->route('admin.directors-insight.index', ['tab' => 'director'])
                ->with('error', 'Only one director can be added.');
        }

        return view('admin.directors.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Check if a director already exists
        if (Director::count() > 0) {
            return redirect()->route('admin.directors-insight.index', ['tab' => 'director'])
                ->with('error', 'Only one director can be added.');
        }
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'position' => 'required|string|max:255',
            'position_en' => 'nullable|string|max:255',
            'bio' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'bio_en' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio_en field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio (English) must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
        ]);

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Director::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'directors',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        Director::create($data);

        return redirect()->route('admin.directors-insight.index', ['tab' => 'director'])
            ->with('success', 'Director created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $director = Director::findOrFail($id);
        return view('admin.directors.show', compact('director'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $director = Director::findOrFail($id);
        return view('admin.directors.edit', compact('director'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, string $id)
    {
        $director = Director::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'position' => 'required|string|max:255',
            'position_en' => 'nullable|string|max:255',
            'bio' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'bio_en' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio_en field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio (English) must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
        ]);

        $data = $request->all();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($director->image) {
                $this->imageService->deleteImage($director->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'directors',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $director->update($data);

        return redirect()->route('admin.directors-insight.index', ['tab' => 'director'])
            ->with('success', 'Director updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id)
    {
        $director = Director::findOrFail($id);

        // Delete image if exists
        if ($director->image) {
            $this->imageService->deleteImage($director->image);
        }

        $director->delete();

        return redirect()->route('admin.directors-insight.index', ['tab' => 'director'])
            ->with('success', 'Director deleted successfully.');
    }
}
