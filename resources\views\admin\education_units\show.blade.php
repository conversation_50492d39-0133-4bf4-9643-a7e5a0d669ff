@extends('admin.layouts.app')

@section('title', 'View Education Unit')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Education Unit</h1>
        <div>
            <a href="{{ route('admin.education-units.edit', $educationUnit) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.education-units.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body text-center">
                    @if($educationUnit->image)
                        <img src="{{ asset('storage/' . $educationUnit->image) }}" alt="{{ $educationUnit->name }}" class="img-fluid rounded">
                    @else
                        <div class="alert alert-info">No image available</div>
                    @endif
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Status Information</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Education Type:</th>
                            <td>
                                @if($educationUnit->edu_type == 'formal')
                                    <span class="badge bg-primary">Formal</span>
                                @else
                                    <span class="badge bg-info">Non-Formal</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                @if($educationUnit->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Display Order:</th>
                            <td>{{ $educationUnit->order }}</td>
                        </tr>
                        <tr>
                            <th>Created:</th>
                            <td>{{ $educationUnit->created_at->format('d M Y H:i') }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated:</th>
                            <td>{{ $educationUnit->updated_at->format('d M Y H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Contact Information</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Address:</th>
                            <td>{{ $educationUnit->address ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td>{{ $educationUnit->phone ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td>{{ $educationUnit->email ?? 'N/A' }}</td>
                        </tr>
                        <tr>
                            <th>Website:</th>
                            <td>
                                @if($educationUnit->website)
                                    <a href="{{ $educationUnit->website }}" target="_blank">{{ $educationUnit->website }}</a>
                                @else
                                    N/A
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <table class="table">
                                <tr>
                                    <th>Name:</th>
                                    <td>{{ $educationUnit->name }}</td>
                                </tr>
                                <tr>
                                    <th>Level:</th>
                                    <td>{{ $educationUnit->level ?? 'N/A' }}</td>
                                </tr>
                            </table>
                            <h6 class="mt-3">Description:</h6>
                            <div class="p-3 bg-light rounded">
                                {!! $educationUnit->description ?? 'No description available.' !!}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <table class="table">
                                <tr>
                                    <th>Name:</th>
                                    <td>{{ $educationUnit->name_en }}</td>
                                </tr>
                                <tr>
                                    <th>Level:</th>
                                    <td>{{ $educationUnit->level_en ?? 'N/A' }}</td>
                                </tr>
                            </table>
                            <h6 class="mt-3">Description:</h6>
                            <div class="p-3 bg-light rounded">
                                {!! $educationUnit->description_en ?? 'No description available.' !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Facilities</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <div class="p-3 bg-light rounded">
                                {!! $educationUnit->facilities ?? 'No facilities information available.' !!}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <div class="p-3 bg-light rounded">
                                {!! $educationUnit->facilities_en ?? 'No facilities information available.' !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
@endsection
