<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webhooks', function (Blueprint $table) {
            if (!Schema::hasColumn('webhooks', 'api_key')) {
                $table->string('api_key', 64)->nullable()->after('secret');
            }
        });

        // Generate API keys for existing webhooks
        if (class_exists('App\Models\Webhook')) {
            $webhooks = \App\Models\Webhook::all();
            foreach ($webhooks as $webhook) {
                $webhook->api_key = Str::random(32);
                $webhook->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webhooks', function (Blueprint $table) {
            if (Schema::hasColumn('webhooks', 'api_key')) {
                $table->dropColumn('api_key');
            }
        });
    }
};
