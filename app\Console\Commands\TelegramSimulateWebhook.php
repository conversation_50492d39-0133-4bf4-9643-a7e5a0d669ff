<?php

namespace App\Console\Commands;

use App\Models\Contact;
use App\Models\Setting;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;

class TelegramSimulateWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:simulate-webhook {contact_id?} {reply_text?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Simulate a Telegram webhook request with a reply to a contact message';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the contact ID
        $contactId = $this->argument('contact_id');
        if (!$contactId) {
            // Get the latest contact
            $contact = Contact::latest()->first();
            
            if (!$contact) {
                $this->error('No contacts found in the database.');
                return 1;
            }
            
            $contactId = $contact->id;
            $this->info("Using latest contact with ID: {$contactId}");
        } else {
            // Find the contact
            $contact = Contact::find($contactId);
            
            if (!$contact) {
                $this->error("Contact with ID {$contactId} not found.");
                return 1;
            }
        }

        // Get the reply text
        $replyText = $this->argument('reply_text');
        if (!$replyText) {
            $replyText = $this->ask('Enter your reply message');
        }

        // Get admin chat ID
        $chatId = Setting::getValue('telegram_chat_id');
        if (empty($chatId)) {
            $this->error('Telegram chat ID is not configured.');
            return 1;
        }

        // Prepare the original message (similar to what would be sent by the notification)
        $originalMessage = "🔔 <b>PESAN KONTAK BARU</b>\n\n";
        $originalMessage .= "<b>Informasi Kontak:</b>\n";
        $originalMessage .= "ID: <b>{$contact->id}</b>\n";
        $originalMessage .= "Nama: <b>{$contact->name}</b>\n";
        $originalMessage .= "Email: {$contact->email}\n";
        if ($contact->phone) {
            $originalMessage .= "Telepon: {$contact->phone}\n";
        }
        $originalMessage .= "Tanggal: " . $contact->created_at->format('d M Y H:i') . "\n\n";
        $originalMessage .= "<b>Subjek:</b> {$contact->subject}\n\n";
        $originalMessage .= "<b>Pesan:</b>\n";
        $originalMessage .= "{$contact->message}\n\n";
        $originalMessage .= "<a href=\"" . route('admin.contacts.show', $contact->id) . "\">Lihat Detail di Admin Panel</a>\n\n";
        $originalMessage .= "<b>Cara Membalas:</b>\n";
        $originalMessage .= "1. Balas langsung ke pesan ini, atau\n";
        $originalMessage .= "2. Gunakan perintah: <code>/reply {$contact->id} [pesan balasan]</code>\n\n";
        $originalMessage .= "Dari: <b>Nurul Hayah 4</b>";

        // Create a simulated webhook update
        $update = [
            'update_id' => rand(100000, 999999),
            'message' => [
                'message_id' => rand(1000, 9999),
                'from' => [
                    'id' => $chatId,
                    'is_bot' => false,
                    'first_name' => 'Admin',
                    'username' => 'admin',
                ],
                'chat' => [
                    'id' => $chatId,
                    'first_name' => 'Admin',
                    'username' => 'admin',
                    'type' => 'private',
                ],
                'date' => time(),
                'text' => $replyText,
                'reply_to_message' => [
                    'message_id' => rand(100, 999),
                    'from' => [
                        'id' => rand(10000, 99999),
                        'is_bot' => true,
                        'first_name' => 'NurulHayahBot',
                        'username' => 'nurul_hayah_bot',
                    ],
                    'chat' => [
                        'id' => $chatId,
                        'first_name' => 'Admin',
                        'username' => 'admin',
                        'type' => 'private',
                    ],
                    'date' => time() - 3600, // 1 hour ago
                    'text' => $originalMessage,
                    'entities' => [
                        ['offset' => 0, 'length' => 1, 'type' => 'bold'],
                        // ... other entities would be here in a real message
                    ],
                ],
            ],
        ];

        // Log the simulated webhook update
        $this->info('Simulating webhook update with reply to contact message...');
        Log::info('Simulating Telegram webhook update', [
            'update_id' => $update['update_id'],
            'contact_id' => $contactId,
            'reply_text' => $replyText,
        ]);

        // Create a request with the update data
        $request = Request::create(
            '/webhook/telegram',
            'POST',
            [],
            [],
            [],
            ['CONTENT_TYPE' => 'application/json'],
            json_encode($update)
        );

        // Dispatch the request to the webhook route
        $this->info('Dispatching request to webhook route...');
        try {
            $response = Route::dispatch($request);
            
            // Check the response
            $statusCode = $response->getStatusCode();
            $content = json_decode($response->getContent(), true);
            
            $this->info("Response status code: {$statusCode}");
            $this->info('Response content: ' . json_encode($content));
            
            if ($statusCode === 200 && isset($content['status']) && $content['status'] === 'success') {
                $this->info('Webhook request processed successfully!');
                
                // Check if the contact was updated
                $contact->refresh();
                if ($contact->hasReply() && $contact->reply_message === $replyText) {
                    $this->info('Contact record was updated with the reply message.');
                    $this->info('Reply timestamp: ' . $contact->replied_at);
                } else {
                    $this->warn('Contact record was not updated with the reply message.');
                    $this->warn('Current reply message: ' . ($contact->reply_message ?? 'None'));
                }
                
                return 0;
            } else {
                $this->error('Webhook request failed.');
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('Exception when dispatching webhook request: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }
    }
}
