<?php $__env->startSection('title', app()->getLocale() == 'id' ? $video->title : $video->title_en); ?>

<?php $__env->startSection('meta_description', app()->getLocale() == 'id' ? $video->description : $video->description_en); ?>

<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
    <link rel="stylesheet" href="<?php echo e(asset('css/plyr-custom.css')); ?>" />
    <style>
        .video-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .video-details {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        .video-title {
            font-weight: 700;
            margin-bottom: 15px;
            color: #333;
        }

        .video-description {
            color: #666;
            line-height: 1.7;
            margin-bottom: 20px;
        }

        .page-header {
            background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('<?php echo e(asset('images/header-bg.jpg')); ?>') center/cover no-repeat;
            padding: 60px 0;
            margin-bottom: 50px;
        }

        .page-title {
            color: white;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .breadcrumb-item, .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
        }

        .breadcrumb-item.active {
            color: white;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.6);
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">
                        <?php echo e(app()->getLocale() == 'id' ? $video->title : $video->title_en); ?>

                    </h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('videos')); ?>"><?php echo e(app()->getLocale() == 'id' ? 'Video' : 'Videos'); ?></a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? $video->title : $video->title_en); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Detail Section -->
    <section class="video-detail-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8" data-aos="fade-up">
                    <a href="<?php echo e(route('videos')); ?>" class="back-to-videos">
                        <i class="fas fa-arrow-left"></i> <?php echo e(app()->getLocale() == 'id' ? 'Kembali ke Video' : 'Back to Videos'); ?>

                    </a>

                    <div class="video-container">
                        <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="<?php echo e($video->youtube_id); ?>"></div>
                    </div>

                    <div class="video-details">
                        <h2 class="video-title"><?php echo e(app()->getLocale() == 'id' ? $video->title : $video->title_en); ?></h2>
                        <div class="video-description summernote-content">
                            <?php echo app()->getLocale() == 'id' ? $video->description : $video->description_en; ?>

                        </div>
                    </div>
                </div>

                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
                    <?php if($relatedVideos->count() > 0): ?>
                        <h3 class="related-videos-title"><?php echo e(app()->getLocale() == 'id' ? 'Video Terkait' : 'Related Videos'); ?></h3>
                        <div class="row g-4">
                            <?php $__currentLoopData = $relatedVideos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedVideo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-6 col-lg-12">
                                    <a href="<?php echo e(route('videos.show', $relatedVideo->id)); ?>" class="text-decoration-none">
                                        <div class="related-video-card">
                                            <div class="related-video-thumbnail">
                                                <?php if($relatedVideo->thumbnail): ?>
                                                    <img src="<?php echo e(asset('storage/' . $relatedVideo->thumbnail)); ?>" alt="<?php echo e($relatedVideo->title); ?>" class="img-fluid">
                                                <?php else: ?>
                                                    <div class="ratio ratio-16x9">
                                                        <iframe src="<?php echo e($relatedVideo->youtube_embed_url); ?>" title="<?php echo e($relatedVideo->title); ?>" allowfullscreen></iframe>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="play-icon">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            </div>
                                            <div class="related-video-info">
                                                <h5 class="related-video-title"><?php echo e(app()->getLocale() == 'id' ? $relatedVideo->title : $relatedVideo->title_en); ?></h5>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
    <script src="<?php echo e(asset('js/plyr-custom.js')); ?>"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/videos/show.blade.php ENDPATH**/ ?>