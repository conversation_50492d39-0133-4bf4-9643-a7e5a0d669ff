<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    /**
     * Get image URL with fallback to default image if not exists
     *
     * @param string|null $path The image path relative to storage
     * @param string $type The type of default image to use (achievement, news, etc.)
     * @return string The image URL
     */
    public static function getImageUrl($path, $type = 'default')
    {
        // If path is null or empty, return default image
        if (empty($path)) {
            return self::getDefaultImage($type);
        }

        // Check if file exists in storage
        if (Storage::disk('public')->exists($path)) {
            return asset('storage/' . $path);
        }

        // If file doesn't exist, return default image
        return self::getDefaultImage($type);
    }

    /**
     * Get default image based on type
     *
     * @param string $type The type of default image
     * @return string The default image URL
     */
    public static function getDefaultImage($type = 'default')
    {
        switch ($type) {
            case 'achievement':
                return asset('images/no-achievement-image.svg');
            case 'news':
                return asset('images/no-news-image.svg');
            case 'announcement':
                return asset('images/no-image.svg');
            case 'profile':
                return asset('images/no-image.svg');
            case 'facility':
                return asset('images/no-image.svg');
            default:
                return asset('images/no-image.svg');
        }
    }
}
