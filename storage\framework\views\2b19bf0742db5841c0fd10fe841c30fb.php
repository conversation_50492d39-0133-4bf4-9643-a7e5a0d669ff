<?php $__env->startSection('title', 'Achievement Details'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Achievement Details</h1>
        <div>
            <a href="<?php echo e(route('admin.achievements.edit', $achievement)); ?>" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.achievements.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Achievement Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Title (Indonesian)</h6>
                            <p><?php echo e($achievement->title); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Title (English)</h6>
                            <p><?php echo e($achievement->title_en); ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Award By (Indonesian)</h6>
                            <p><?php echo e($achievement->award_by ?: '-'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Award By (English)</h6>
                            <p><?php echo e($achievement->award_by_en ?: '-'); ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Achievement Date</h6>
                            <p><?php echo e($achievement->achievement_date ? $achievement->achievement_date->format('d F Y') : '-'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Display Order</h6>
                            <p><?php echo e($achievement->order); ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Featured</h6>
                            <p>
                                <?php if($achievement->is_featured): ?>
                                    <span class="badge bg-success">Yes</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">No</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Status</h6>
                            <p>
                                <?php if($achievement->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Description (Indonesian)</h6>
                            <div class="formatted-content">
                                <?php echo $achievement->description ?: '-'; ?>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Description (English)</h6>
                            <div class="formatted-content">
                                <?php echo $achievement->description_en ?: '-'; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Achievement Image</h5>
                </div>
                <div class="card-body text-center">
                    <?php if($achievement->image): ?>
                        <img src="<?php echo e(asset('storage/' . $achievement->image)); ?>" alt="<?php echo e($achievement->title); ?>" class="img-fluid rounded">
                    <?php else: ?>
                        <div class="bg-light p-5 rounded">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="mt-3 text-muted">No image available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Metadata</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">ID</h6>
                        <p><?php echo e($achievement->id); ?></p>
                    </div>
                    <div class="mb-3">
                        <h6 class="fw-bold">Created At</h6>
                        <p><?php echo e($achievement->created_at->format('d F Y, H:i')); ?></p>
                    </div>
                    <div>
                        <h6 class="fw-bold">Last Updated</h6>
                        <p><?php echo e($achievement->updated_at->format('d F Y, H:i')); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Formatted content styling */
    .formatted-content {
        line-height: 1.6;
    }

    .formatted-content img {
        max-width: 100%;
        height: auto;
        margin: 0.5rem 0;
    }

    .formatted-content ul,
    .formatted-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    .formatted-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .formatted-content table td,
    .formatted-content table th {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .formatted-content blockquote {
        padding: 0.5rem 1rem;
        margin: 1rem 0;
        border-left: 4px solid #6c757d;
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/achievements/show.blade.php ENDPATH**/ ?>