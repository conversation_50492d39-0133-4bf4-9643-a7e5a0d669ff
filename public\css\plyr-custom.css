/* Custom Plyr.js Styling */
.plyr--video {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.plyr--full-ui input[type=range] {
    color: #198754;
}

.plyr__control--overlaid {
    background: rgba(25, 135, 84, 0.8);
}

.plyr--video .plyr__control:hover {
    background: #198754;
}

.plyr--video .plyr__control.plyr__tab-focus {
    background: #198754;
}

.plyr__menu__container .plyr__control[role=menuitemradio][aria-checked=true]::before {
    background: #198754;
}

/* Video Cards Styling */
.video-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: 10px 10px 0 0;
    height: 200px;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.play-icon i {
    color: #198754;
    font-size: 24px;
    margin-left: 4px;
}

.video-card:hover .play-icon {
    background-color: #198754;
}

.video-card:hover .play-icon i {
    color: white;
}

.video-info {
    padding: 1.25rem;
    background-color: white;
}

.video-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 72px; /* Increased height to accommodate 3 lines */
}

.video-description {
    color: #6c757d;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 60px;
    font-family: 'Segoe UI', Arial, sans-serif;
}

/* Apply Summernote content styles to video descriptions */
.video-description.summernote-content {
    text-align: justify;
}

.video-description.summernote-content p {
    margin-bottom: 0.5em;
}

.video-description.summernote-content strong,
.video-description.summernote-content b {
    font-weight: bold;
}

.video-description.summernote-content em,
.video-description.summernote-content i {
    font-style: italic;
}

.video-description.summernote-content ul,
.video-description.summernote-content ol {
    padding-left: 1.5em;
    margin-bottom: 0.5em;
}

/* Related Videos Styling */
.related-videos-title {
    font-weight: 600;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 15px;
}

.related-videos-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #198754;
}

.related-video-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.related-video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.related-video-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: 10px 10px 0 0;
    height: 150px;
}

.related-video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.related-video-card:hover .related-video-thumbnail img {
    transform: scale(1.05);
}

.related-video-info {
    padding: 15px;
    background-color: white;
}

.related-video-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 72px;
    font-size: 0.95rem;
}

/* Back to videos link */
.back-to-videos {
    display: inline-flex;
    align-items: center;
    margin-bottom: 20px;
    color: #198754;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-videos:hover {
    color: #0d6e4d;
    text-decoration: none;
}

.back-to-videos i {
    margin-right: 8px;
}
