@extends('admin.layouts.app')

@section('title', 'Activity Schedule')

@push('styles')
<style>
    .nav-tabs .nav-link {
        color: #6c757d;
    }
    .nav-tabs .nav-link.active {
        font-weight: 600;
        color: #0d6efd;
    }
</style>
@endpush

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Activity Schedule</h1>
        <a href="{{ route('admin.schedules.create') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'daily' ? 'active' : '' }}" href="{{ route('admin.schedules.index', ['tab' => 'daily']) }}">
                        Daily
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'weekly' ? 'active' : '' }}" href="{{ route('admin.schedules.index', ['tab' => 'weekly']) }}">
                        Weekly
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'monthly' ? 'active' : '' }}" href="{{ route('admin.schedules.index', ['tab' => 'monthly']) }}">
                        Monthly
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'yearly' ? 'active' : '' }}" href="{{ route('admin.schedules.index', ['tab' => 'yearly']) }}">
                        Annual
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                @if($activeTab == 'daily')
                                    Daily
                                @elseif($activeTab == 'weekly')
                                    Day
                                @elseif($activeTab == 'monthly')
                                    Week
                                @elseif($activeTab == 'yearly')
                                    Month
                                @endif
                            </th>
                            <th>Time</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($schedules as $schedule)
                            <tr>
                                <td>
                                    @if($schedule->activity_type == 'daily')
                                        <span>Daily</span>
                                    @elseif($schedule->activity_type == 'weekly')
                                        <span>{{ $schedule->day_of_week }}</span>
                                    @elseif($schedule->activity_type == 'monthly')
                                        <span>Week {{ $schedule->week_number }}</span>
                                    @elseif($schedule->activity_type == 'yearly')
                                        <span>{{ $schedule->month_name }}</span>
                                    @endif
                                </td>
                                <td>{{ $schedule->start_time->format('H:i') }} - {{ $schedule->end_time->format('H:i') }}</td>
                                <td>{{ $schedule->title }}</td>
                                <td>
                                    @if($schedule->category)
                                        <span class="badge bg-info">{{ ucfirst($schedule->category) }}</span>
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    @if($schedule->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('admin.schedules.show', $schedule) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.schedules.edit', $schedule) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.schedules.destroy', ['schedule' => $schedule, 'tab' => $activeTab]) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this schedule?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">
                                    @if($activeTab == 'daily')
                                        No daily schedules found.
                                    @elseif($activeTab == 'weekly')
                                        No weekly schedules found.
                                    @elseif($activeTab == 'monthly')
                                        No monthly schedules found.
                                    @elseif($activeTab == 'yearly')
                                        No annual schedules found.
                                    @endif
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
