<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Lang;

class CustomResetPasswordNotification extends ResetPassword
{
    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if (static::$createUrlCallback) {
            $url = call_user_func(static::$createUrlCallback, $notifiable, $this->token);
        } else {
            $url = url(route('password.reset', [
                'token' => $this->token,
                'email' => $notifiable->getEmailForPasswordReset(),
            ], false));
        }

        // Use the password_reset mailer configuration
        \Illuminate\Support\Facades\Config::set('mail.default', 'password_reset');

        $mailMessage = new MailMessage;

        // Get from address and name from configuration
        $fromConfig = config('mail.from_addresses.password_reset', config('mail.from'));
        $mailMessage->from($fromConfig['address'], $fromConfig['name']);

        // Get the locale from the user or use the current app locale
        $locale = $notifiable->locale ?? app()->getLocale();

        // Set the locale for this email
        app()->setLocale($locale);

        return $mailMessage
            ->subject(__('emails.reset_password_subject'))
            ->greeting(__('emails.islamic_greeting'))
            ->line(__('emails.reset_password_intro'))
            ->action(__('emails.reset_password_action'), $url)
            ->line(__('emails.reset_password_expiry', ['count' => config('auth.passwords.'.config('auth.defaults.passwords').'.expire')]))
            ->line(__('emails.reset_password_ignore'))
            ->line('**' . __('emails.auto_email_notice') . '**')
            ->salutation(__('emails.islamic_closing'));
    }
}
