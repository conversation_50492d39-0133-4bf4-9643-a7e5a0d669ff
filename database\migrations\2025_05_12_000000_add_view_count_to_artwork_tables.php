<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add view_count column to paints table
        Schema::table('paints', function (Blueprint $table) {
            if (!Schema::hasColumn('paints', 'view_count')) {
                $table->unsignedInteger('view_count')->default(0)->after('is_active');
            }
        });

        // Add view_count column to literature table
        Schema::table('literature', function (Blueprint $table) {
            if (!Schema::hasColumn('literature', 'view_count')) {
                $table->unsignedInteger('view_count')->default(0)->after('is_active');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove view_count column from paints table
        Schema::table('paints', function (Blueprint $table) {
            if (Schema::hasColumn('paints', 'view_count')) {
                $table->dropColumn('view_count');
            }
        });

        // Remove view_count column from literature table
        Schema::table('literature', function (Blueprint $table) {
            if (Schema::hasColumn('literature', 'view_count')) {
                $table->dropColumn('view_count');
            }
        });
    }
};
