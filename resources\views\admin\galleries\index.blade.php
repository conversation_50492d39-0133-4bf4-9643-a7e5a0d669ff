@extends('admin.layouts.app')

@section('title', 'Gallery Management')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Gallery Management</h1>
        <a href="{{ route('admin.galleries.create') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                @forelse($galleries as $gallery)
                    <div class="col-md-4 col-sm-6 mb-4">
                        <div class="card h-100">
                            <div class="position-relative">
                                @if($gallery->image)
                                    <img src="{{ asset('storage/' . $gallery->image) }}" class="card-img-top" alt="{{ $gallery->title }}" style="height: 200px; object-fit: cover;">
                                @else
                                    <div class="bg-light text-center py-5">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                @endif
                                <div class="position-absolute top-0 end-0 p-2">
                                    <span class="badge bg-primary">{{ $gallery->category }}</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">{{ $gallery->title }}</h5>
                                @if($gallery->description)
                                    <p class="card-text text-muted small">{{ \Illuminate\Support\Str::limit($gallery->description, 100) }}</p>
                                @endif
                            </div>
                            <div class="card-footer bg-white border-top-0">
                                <div class="btn-group w-100">
                                    <a href="{{ route('admin.galleries.show', $gallery) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.galleries.destroy', $gallery) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this item?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            No gallery items found.
                        </div>
                    </div>
                @endforelse
            </div>
            
            <div class="d-flex justify-content-center mt-4">
                {{ $galleries->links() }}
            </div>
        </div>
    </div>
@endsection
