/**
 * Styles for formatted content from Summernote
 */

/* Global content container class for all Summernote content */
.summernote-content,
/* Existing specific content containers */
.news-content,
.announcement-content,
.agenda-content,
.edu-unit-detail-desc,
.edu-unit-detail-facilities,
.principal-section .section-content,
.content-wrapper {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
    font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
}

/* Headings */
.summernote-content h1,
.news-content h1,
.announcement-content h1,
.agenda-content h1,
.edu-unit-detail-desc h1,
.edu-unit-detail-facilities h1,
.principal-section .section-content h1,
.content-wrapper h1 {
    font-size: 28px;
    margin-top: 1.5em;
    margin-bottom: 0.8em;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
}

.summernote-content h2,
.news-content h2,
.announcement-content h2,
.agenda-content h2,
.edu-unit-detail-desc h2,
.edu-unit-detail-facilities h2,
.principal-section .section-content h2,
.content-wrapper h2 {
    font-size: 24px;
    margin-top: 1.3em;
    margin-bottom: 0.7em;
    font-weight: 600;
    font-family: 'Poppin<PERSON>', sans-serif;
}

.summernote-content h3,
.news-content h3,
.announcement-content h3,
.agenda-content h3,
.edu-unit-detail-desc h3,
.edu-unit-detail-facilities h3,
.principal-section .section-content h3,
.content-wrapper h3 {
    font-size: 20px;
    margin-top: 1.2em;
    margin-bottom: 0.6em;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}

.summernote-content h4,
.news-content h4,
.announcement-content h4,
.agenda-content h4,
.edu-unit-detail-desc h4,
.edu-unit-detail-facilities h4,
.principal-section .section-content h4,
.content-wrapper h4 {
    font-size: 18px;
    margin-top: 1.1em;
    margin-bottom: 0.5em;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}

/* First heading should not have top margin */
.summernote-content h1:first-child,
.summernote-content h2:first-child,
.summernote-content h3:first-child,
.summernote-content h4:first-child,
.news-content h1:first-child,
.news-content h2:first-child,
.news-content h3:first-child,
.news-content h4:first-child,
.announcement-content h1:first-child,
.announcement-content h2:first-child,
.announcement-content h3:first-child,
.announcement-content h4:first-child,
.agenda-content h1:first-child,
.agenda-content h2:first-child,
.agenda-content h3:first-child,
.agenda-content h4:first-child,
.edu-unit-detail-desc h1:first-child,
.edu-unit-detail-desc h2:first-child,
.edu-unit-detail-desc h3:first-child,
.edu-unit-detail-desc h4:first-child,
.edu-unit-detail-facilities h1:first-child,
.edu-unit-detail-facilities h2:first-child,
.edu-unit-detail-facilities h3:first-child,
.edu-unit-detail-facilities h4:first-child,
.principal-section .section-content h1:first-child,
.principal-section .section-content h2:first-child,
.principal-section .section-content h3:first-child,
.principal-section .section-content h4:first-child,
.content-wrapper h1:first-child,
.content-wrapper h2:first-child,
.content-wrapper h3:first-child,
.content-wrapper h4:first-child {
    margin-top: 0;
}

/* Paragraphs */
.summernote-content p,
.news-content p,
.announcement-content p,
.agenda-content p,
.edu-unit-detail-desc p,
.edu-unit-detail-facilities p,
.principal-section .section-content p,
.content-wrapper p {
    margin-bottom: 1em;
    font-family: 'Poppins', Arial, sans-serif;
    font-size: 16px;
    text-align: justify;
}

/* Paragraph alignment */
.summernote-content p[style*="text-align"],
.news-content p[style*="text-align"],
.announcement-content p[style*="text-align"],
.agenda-content p[style*="text-align"],
.edu-unit-detail-desc p[style*="text-align"],
.edu-unit-detail-facilities p[style*="text-align"],
.principal-section .section-content p[style*="text-align"],
.content-wrapper p[style*="text-align"] {
    display: block;
    width: 100%;
}

/* Links */
.news-content a,
.announcement-content a,
.agenda-content a,
.edu-unit-detail-desc a,
.edu-unit-detail-facilities a,
.principal-section .section-content a {
    color: #198754;
    text-decoration: none;
    transition: color 0.2s;
}

.news-content a:hover,
.announcement-content a:hover,
.agenda-content a:hover,
.edu-unit-detail-desc a:hover,
.edu-unit-detail-facilities a:hover,
.principal-section .section-content a:hover {
    color: #ffffff;
    text-decoration: none;
}

/* Lists */
.news-content ul,
.news-content ol,
.announcement-content ul,
.announcement-content ol,
.agenda-content ul,
.agenda-content ol,
.edu-unit-detail-desc ul,
.edu-unit-detail-desc ol,
.edu-unit-detail-facilities ul,
.edu-unit-detail-facilities ol,
.principal-section .section-content ul,
.principal-section .section-content ol {
    margin-bottom: 1em;
    padding-left: 2em;
}

.news-content li,
.announcement-content li,
.agenda-content li,
.edu-unit-detail-desc li,
.edu-unit-detail-facilities li,
.principal-section .section-content li {
    margin-bottom: 0.5em;
}

/* Blockquotes */
.news-content blockquote,
.announcement-content blockquote,
.agenda-content blockquote,
.edu-unit-detail-desc blockquote,
.edu-unit-detail-facilities blockquote,
.principal-section .section-content blockquote {
    border-left: 4px solid #198754;
    padding: 0.5em 1em;
    margin: 1.5em 0;
    background-color: #f8f9fa;
    font-style: italic;
}

/* Tables */
.news-content table,
.announcement-content table,
.agenda-content table,
.edu-unit-detail-desc table,
.edu-unit-detail-facilities table,
.principal-section .section-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5em 0;
}

.news-content th,
.news-content td,
.announcement-content th,
.announcement-content td,
.agenda-content th,
.agenda-content td,
.edu-unit-detail-desc th,
.edu-unit-detail-desc td,
.edu-unit-detail-facilities th,
.edu-unit-detail-facilities td,
.principal-section .section-content th,
.principal-section .section-content td {
    border: 1px solid #dee2e6;
    padding: 0.75em;
}

.news-content th,
.announcement-content th,
.agenda-content th,
.edu-unit-detail-desc th,
.edu-unit-detail-facilities th,
.principal-section .section-content th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.news-content tr:nth-child(even),
.announcement-content tr:nth-child(even),
.agenda-content tr:nth-child(even),
.edu-unit-detail-desc tr:nth-child(even),
.edu-unit-detail-facilities tr:nth-child(even),
.principal-section .section-content tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Images */
.news-content img,
.announcement-content img,
.agenda-content img,
.edu-unit-detail-desc img,
.edu-unit-detail-facilities img,
.principal-section .section-content img {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
    border-radius: 4px;
}

/* Figure with caption */
.news-content figure,
.announcement-content figure,
.agenda-content figure,
.edu-unit-detail-desc figure,
.edu-unit-detail-facilities figure,
.principal-section .section-content figure {
    margin: 1.5em 0;
    text-align: center;
}

.news-content figcaption,
.announcement-content figcaption,
.agenda-content figcaption,
.edu-unit-detail-desc figcaption,
.edu-unit-detail-facilities figcaption,
.principal-section .section-content figcaption {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 0.5em;
}

/* Code blocks */
.news-content pre,
.announcement-content pre,
.agenda-content pre,
.edu-unit-detail-desc pre,
.edu-unit-detail-facilities pre,
.principal-section .section-content pre {
    background-color: #f8f9fa;
    padding: 1em;
    border-radius: 4px;
    overflow-x: auto;
    margin: 1.5em 0;
    font-family: monospace;
}

/* Inline code */
.news-content code,
.announcement-content code,
.agenda-content code,
.edu-unit-detail-desc code,
.edu-unit-detail-facilities code,
.principal-section .section-content code {
    background-color: #f8f9fa;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
}

/* Horizontal rule */
.news-content hr,
.announcement-content hr,
.agenda-content hr,
.edu-unit-detail-desc hr,
.edu-unit-detail-facilities hr,
.principal-section .section-content hr {
    border: 0;
    border-top: 1px solid #dee2e6;
    margin: 2em 0;
}

/* Text formatting */
.news-content strong,
.announcement-content strong,
.agenda-content strong,
.edu-unit-detail-desc strong,
.edu-unit-detail-facilities strong,
.principal-section .section-content strong {
    font-weight: 700;
}

.news-content em,
.announcement-content em,
.agenda-content em,
.edu-unit-detail-desc em,
.edu-unit-detail-facilities em,
.principal-section .section-content em {
    font-style: italic;
}

.news-content u,
.announcement-content u,
.agenda-content u,
.edu-unit-detail-desc u,
.edu-unit-detail-facilities u,
.principal-section .section-content u {
    text-decoration: underline;
}

.news-content s,
.announcement-content s,
.agenda-content s,
.edu-unit-detail-desc s,
.edu-unit-detail-facilities s,
.principal-section .section-content s {
    text-decoration: line-through;
}

/* Text alignment */
.news-content .text-left,
.announcement-content .text-left,
.agenda-content .text-left,
.edu-unit-detail-desc .text-left,
.edu-unit-detail-facilities .text-left,
.principal-section .section-content .text-left,
.news-content [style*="text-align: left"],
.announcement-content [style*="text-align: left"],
.agenda-content [style*="text-align: left"],
.edu-unit-detail-desc [style*="text-align: left"],
.edu-unit-detail-facilities [style*="text-align: left"],
.principal-section .section-content [style*="text-align: left"] {
    text-align: left;
}

.news-content .text-center,
.announcement-content .text-center,
.agenda-content .text-center,
.edu-unit-detail-desc .text-center,
.edu-unit-detail-facilities .text-center,
.principal-section .section-content .text-center,
.news-content [style*="text-align: center"],
.announcement-content [style*="text-align: center"],
.agenda-content [style*="text-align: center"],
.edu-unit-detail-desc [style*="text-align: center"],
.edu-unit-detail-facilities [style*="text-align: center"],
.principal-section .section-content [style*="text-align: center"] {
    text-align: center;
}

.news-content .text-right,
.announcement-content .text-right,
.agenda-content .text-right,
.edu-unit-detail-desc .text-right,
.edu-unit-detail-facilities .text-right,
.principal-section .section-content .text-right,
.news-content [style*="text-align: right"],
.announcement-content [style*="text-align: right"],
.agenda-content [style*="text-align: right"],
.edu-unit-detail-desc [style*="text-align: right"],
.edu-unit-detail-facilities [style*="text-align: right"],
.principal-section .section-content [style*="text-align: right"] {
    text-align: right;
}

.news-content .text-justify,
.announcement-content .text-justify,
.agenda-content .text-justify,
.edu-unit-detail-desc .text-justify,
.edu-unit-detail-facilities .text-justify,
.principal-section .section-content .text-justify,
.news-content [style*="text-align: justify"],
.announcement-content [style*="text-align: justify"],
.agenda-content [style*="text-align: justify"],
.edu-unit-detail-desc [style*="text-align: justify"],
.edu-unit-detail-facilities [style*="text-align: justify"],
.principal-section .section-content [style*="text-align: justify"] {
    text-align: justify;
}

/* Responsive tables */
@media (max-width: 768px) {
    .summernote-content table,
    .news-content table,
    .announcement-content table,
    .agenda-content table,
    .edu-unit-detail-desc table,
    .edu-unit-detail-facilities table,
    .principal-section .section-content table,
    .content-wrapper table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}

/* Override inline styles for consistency */
.summernote-content *,
.news-content *,
.announcement-content *,
.agenda-content *,
.edu-unit-detail-desc *,
.edu-unit-detail-facilities *,
.principal-section .section-content *,
.content-wrapper * {
    font-family: 'Poppins', Arial, sans-serif !important;
}

/* Default paragraph alignment is justify, but can be overridden */
.summernote-content p:not([style*="text-align"]),
.news-content p:not([style*="text-align"]),
.announcement-content p:not([style*="text-align"]),
.agenda-content p:not([style*="text-align"]),
.edu-unit-detail-desc p:not([style*="text-align"]),
.edu-unit-detail-facilities p:not([style*="text-align"]),
.principal-section .section-content p:not([style*="text-align"]),
.content-wrapper p:not([style*="text-align"]) {
    text-align: justify;
}

.summernote-content span,
.news-content span,
.announcement-content span,
.agenda-content span,
.edu-unit-detail-desc span,
.edu-unit-detail-facilities span,
.principal-section .section-content span,
.content-wrapper span {
    font-size: inherit !important;
    line-height: inherit !important;
}

/* Normalize font sizes from Summernote */
.summernote-content [style*="font-size"],
.news-content [style*="font-size"],
.announcement-content [style*="font-size"],
.agenda-content [style*="font-size"],
.edu-unit-detail-desc [style*="font-size"],
.edu-unit-detail-facilities [style*="font-size"],
.principal-section .section-content [style*="font-size"],
.content-wrapper [style*="font-size"] {
    font-size: 16px !important;
}

/* Preserve heading sizes */
.summernote-content h1, .news-content h1, .announcement-content h1, .agenda-content h1,
.edu-unit-detail-desc h1, .edu-unit-detail-facilities h1, .principal-section .section-content h1, .content-wrapper h1 {
    font-size: 28px !important;
}

.summernote-content h2, .news-content h2, .announcement-content h2, .agenda-content h2,
.edu-unit-detail-desc h2, .edu-unit-detail-facilities h2, .principal-section .section-content h2, .content-wrapper h2 {
    font-size: 24px !important;
}

.summernote-content h3, .news-content h3, .announcement-content h3, .agenda-content h3,
.edu-unit-detail-desc h3, .edu-unit-detail-facilities h3, .principal-section .section-content h3, .content-wrapper h3 {
    font-size: 20px !important;
}

.summernote-content h4, .news-content h4, .announcement-content h4, .agenda-content h4,
.edu-unit-detail-desc h4, .edu-unit-detail-facilities h4, .principal-section .section-content h4, .content-wrapper h4 {
    font-size: 18px !important;
}
