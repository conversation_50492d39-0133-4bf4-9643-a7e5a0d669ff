<?php

// Get database configuration from Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get database configuration
$connection = config('database.default');
$config = config("database.connections.{$connection}");

// Database credentials
$host = $config['host'];
$database = $config['database'];
$username = $config['username'];
$password = $config['password'];

// Path to SQL file
$sqlFile = __DIR__ . '/nurul_hayah_4.sql';

// Check if the file exists
if (!file_exists($sqlFile)) {
    die("SQL file not found: {$sqlFile}\n");
}

// Create PDO connection
try {
    $pdo = new PDO("mysql:host={$host}", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Drop and recreate the database
    echo "Dropping database if exists...\n";
    $pdo->exec("DROP DATABASE IF EXISTS `{$database}`");
    
    echo "Creating database...\n";
    $pdo->exec("CREATE DATABASE `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    echo "Selecting database...\n";
    $pdo->exec("USE `{$database}`");
    
    // Read and execute SQL file
    echo "Importing SQL file...\n";
    $sql = file_get_contents($sqlFile);
    
    // Split SQL file into individual statements
    $statements = explode(';', $sql);
    
    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                echo "Error executing statement: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "Database restored successfully!\n";
    
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage() . "\n");
}
