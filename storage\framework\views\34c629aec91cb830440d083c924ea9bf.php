<?php $__env->startSection('title', 'View News'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View News</h1>
        <div>
            <a href="<?php echo e(route('admin.news.edit', $news)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.news.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">News Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID:</div>
                        <div class="col-md-9"><?php echo e($news->id); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Published Date:</div>
                        <div class="col-md-9"><?php echo e($news->published_at->format('d M Y')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Category:</div>
                        <div class="col-md-9">
                            <?php if($news->category): ?>
                                <a href="<?php echo e(route('admin.categories.show', $news->category)); ?>" class="badge bg-info text-decoration-none">
                                    <?php echo e($news->category->name); ?>

                                </a>
                            <?php else: ?>
                                <span class="text-muted">Uncategorized</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Author:</div>
                        <div class="col-md-9"><?php echo e($news->user->name); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At:</div>
                        <div class="col-md-9"><?php echo e($news->created_at->format('d M Y H:i:s')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Updated At:</div>
                        <div class="col-md-9"><?php echo e($news->updated_at->format('d M Y H:i:s')); ?></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Indonesian Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Title:</div>
                                <div class="col-md-9"><?php echo e($news->title); ?></div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Slug:</div>
                                <div class="col-md-9"><?php echo e($news->slug); ?></div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Content:</div>
                                <div class="col-md-9"><?php echo $news->content; ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">English Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Title:</div>
                                <div class="col-md-9"><?php echo e($news->title_en); ?></div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Slug:</div>
                                <div class="col-md-9"><?php echo e($news->slug_en); ?></div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Content:</div>
                                <div class="col-md-9"><?php echo $news->content_en; ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Images</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">Thumbnail:</h6>
                        <?php if($news->thumbnail): ?>
                            <img src="<?php echo e(asset('storage/' . $news->thumbnail)); ?>" alt="<?php echo e($news->title); ?>" class="img-fluid rounded">
                        <?php else: ?>
                            <p class="text-muted">No thumbnail available</p>
                        <?php endif; ?>
                    </div>

                    <div>
                        <h6 class="fw-bold">Main Image:</h6>
                        <?php if($news->image): ?>
                            <img src="<?php echo e(asset('storage/' . $news->image)); ?>" alt="<?php echo e($news->title); ?>" class="img-fluid rounded">
                        <?php else: ?>
                            <p class="text-muted">No main image available</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="<?php echo e(route('news.show', $news->slug)); ?>" class="btn btn-info w-100 mb-2" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i> View on Website
                    </a>

                    <a href="<?php echo e(route('admin.news.edit', $news)); ?>" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit News
                    </a>

                    <form action="<?php echo e(route('admin.news.destroy', $news)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this news?')">
                            <i class="fas fa-trash me-1"></i> Delete News
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/news/show.blade.php ENDPATH**/ ?>