<?php $__env->startSection('title', 'Achievements'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Achievements</h1>
        <a href="<?php echo e(route('admin.achievements.create')); ?>" class="btn btn-success">
            <i class="fas fa-plus me-1"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if($achievements->isEmpty()): ?>
                <div class="text-center py-5">
                    <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                    <p class="mb-0">No achievements found. Click the "Add New" button to create one.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="80">Image</th>
                                <th>Title</th>
                                <th>Award By</th>
                                <th>Date</th>
                                <th width="100">Featured</th>
                                <th width="120">Order</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php if($achievement->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $achievement->image)); ?>" alt="<?php echo e($achievement->title); ?>" class="img-thumbnail" width="60">
                                        <?php else: ?>
                                            <div class="bg-light text-center p-2 rounded">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo e($achievement->title); ?></div>
                                        <small class="text-muted"><?php echo e($achievement->title_en); ?></small>
                                    </td>
                                    <td><?php echo e($achievement->award_by); ?></td>
                                    <td><?php echo e($achievement->achievement_date ? $achievement->achievement_date->format('d M Y') : '-'); ?></td>
                                    <td>
                                        <?php if($achievement->is_featured): ?>
                                            <span class="badge bg-success">Featured</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($achievement->order); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.achievements.edit', $achievement)); ?>" class="btn btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.achievements.show', $achievement)); ?>" class="btn btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo e($achievement->id); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?php echo e($achievement->id); ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo e($achievement->id); ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel<?php echo e($achievement->id); ?>">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Are you sure you want to delete the achievement "<?php echo e($achievement->title); ?>"?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="<?php echo e(route('admin.achievements.destroy', $achievement)); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/achievements/index.blade.php ENDPATH**/ ?>