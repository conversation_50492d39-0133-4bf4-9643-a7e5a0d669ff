@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Pendaftaran' : 'Registration')

@push('styles')
<!-- International Telephone Input CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/css/intlTelInput.css">

<style>
    html {
        scroll-behavior: smooth;
    }

    /* Sticky image container */
    .sticky-registration-image {
        position: sticky;
        top: 100px; /* This will be overridden by JavaScript */
        margin-top: 0;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    /* Make sure the image is fully visible */
    .sticky-registration-image .card {
        overflow: hidden;
        transition: all 0.3s ease;
        transform: translateZ(0);
        will-change: transform;
        border-radius: 10px; /* Ensure rounded corners are visible */
        margin-top: 10px; /* Increased top margin from 5px to 10px */
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); /* Enhanced shadow for better visibility */
    }

    /* Enhance button appearance */
    .sticky-registration-image .btn-success {
        padding: 10px 24px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .sticky-registration-image .btn-success:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Disable sticky on mobile */
    @media (max-width: 991.98px) {
        .sticky-registration-image {
            position: static;
            margin-top: 20px;
            margin-bottom: 20px;
        }
    }

    /* Registration form fade effect */
    .registration-form-container {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        transition: opacity 0.8s ease-out, max-height 0.8s ease-out;
        pointer-events: none;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
        position: relative;
        z-index: 10;
    }

    .registration-form-container.show {
        opacity: 1;
        max-height: 5000px; /* Arbitrary large value */
        overflow: visible;
        pointer-events: auto;
        margin-bottom: 1.5rem;
    }

    .registration-info-container {
        opacity: 1;
        max-height: 5000px; /* Arbitrary large value */
        overflow: visible;
        transition: opacity 0.8s ease-out, max-height 0.8s ease-out, visibility 0s linear 0.8s;
        transform: translateY(0);
        transition-property: opacity, max-height, transform, visibility;
        visibility: visible;
        position: relative;
        z-index: 5;
    }

    .registration-info-container.hide {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        pointer-events: none;
        transform: translateY(-20px);
        visibility: hidden;
        transition: opacity 0.8s ease-out, max-height 0.8s ease-out, visibility 0s linear 0.8s;
        position: absolute;
        z-index: 1;
    }

    /* Ensure section title is visible when form is shown */
    #registration-form .section-title.show {
        margin-bottom: 2rem;
    }

    /* Make sure the registration form section is full width */
    #registration-form {
        width: 100%;
        position: relative;
        z-index: 20;
    }

    /* International Telephone Input custom styles */
    .iti {
        width: 100%;
    }

    .iti__flag {
        background-image: url("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/img/flags.png");
    }

    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .iti__flag {
            background-image: url("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/img/<EMAIL>");
        }
    }

    /* Terms and Conditions Dialog Box Styles */
    #terms-dialog {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1050;
        overflow: auto;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .terms-dialog-content {
        position: relative;
        background-color: #fff;
        margin: 10% auto;
        padding: 20px;
        width: 80%;
        max-width: 700px;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from { transform: translateY(-50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    .terms-dialog-close {
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 24px;
        font-weight: bold;
        color: #aaa;
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .terms-dialog-close:hover {
        color: #333;
    }

    .terms-dialog-body {
        max-height: 400px;
        overflow-y: auto;
        padding: 15px 5px;
        margin-bottom: 20px;
        border-top: 1px solid #e9ecef;
        border-bottom: 1px solid #e9ecef;
    }

    .terms-dialog-buttons {
        text-align: right;
    }

    .terms-dialog-buttons button {
        margin-left: 10px;
    }

    /* Make dialog responsive */
    @media (max-width: 767.98px) {
        .terms-dialog-content {
            width: 95%;
            margin: 5% auto;
            padding: 15px;
        }

        .terms-dialog-body {
            max-height: 300px;
        }
    }

    /* Additional Information Text Formatting */
    .registration-additional-info .summernote-content {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #198754;
        margin-top: 1rem;
    }

    .registration-additional-info .summernote-content p:last-child {
        margin-bottom: 0;
    }

    .registration-additional-info .summernote-content h1,
    .registration-additional-info .summernote-content h2,
    .registration-additional-info .summernote-content h3,
    .registration-additional-info .summernote-content h4,
    .registration-additional-info .summernote-content h5,
    .registration-additional-info .summernote-content h6 {
        color: #198754;
        margin-top: 1.5rem;
        margin-bottom: 1rem;
    }

    .registration-additional-info .summernote-content h1:first-child,
    .registration-additional-info .summernote-content h2:first-child,
    .registration-additional-info .summernote-content h3:first-child,
    .registration-additional-info .summernote-content h4:first-child,
    .registration-additional-info .summernote-content h5:first-child,
    .registration-additional-info .summernote-content h6:first-child {
        margin-top: 0;
    }

    .registration-additional-info .summernote-content ul,
    .registration-additional-info .summernote-content ol {
        padding-left: 1.5rem;
    }

    .registration-additional-info .summernote-content li {
        margin-bottom: 0.5rem;
    }

    .registration-additional-info .summernote-content blockquote {
        border-left: 4px solid #198754;
        background-color: rgba(25, 135, 84, 0.1);
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        font-style: italic;
    }

    .registration-additional-info .summernote-content table {
        background-color: #fff;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* General text formatting for all registration content */
    .registration-info-container .summernote-content {
        font-size: 16px;
        line-height: 1.7;
    }

    .registration-info-container .summernote-content p {
        margin-bottom: 1em;
        text-align: justify;
    }

    .registration-info-container .summernote-content strong {
        font-weight: 600;
        color: #198754;
    }

    .registration-info-container .summernote-content em {
        font-style: italic;
        color: #6c757d;
    }

    .registration-info-container .summernote-content ul,
    .registration-info-container .summernote-content ol {
        margin-bottom: 1rem;
    }

    .registration-info-container .summernote-content li {
        margin-bottom: 0.5rem;
        line-height: 1.6;
    }

    .registration-info-container .summernote-content small {
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* Table cell content formatting */
    .table .summernote-content {
        font-size: 14px;
        line-height: 1.5;
    }

    .table .summernote-content p {
        margin-bottom: 0.5rem;
    }

    .table .summernote-content p:last-child {
        margin-bottom: 0;
    }
</style>
@endpush

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Pendaftaran Santri Baru' : 'New Student Registration' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Pendaftaran' : 'Registration' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Information -->
    <section class="section-padding bg-light registration-info-container">
        <div class="container">
            @if(!$hasSchedule)
            <!-- Display when no registration schedule exists -->
            <div class="row justify-content-center">
                <div class="col-lg-8" data-aos="fade-up">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-5 text-center">
                            <div class="mb-4">
                                <i class="fas fa-calendar-times text-warning fa-4x mb-4"></i>
                                <h2 class="card-title text-warning mb-4">{{ app()->getLocale() == 'id' ? 'Pendaftaran Belum Tersedia' : 'Registration Not Available' }}</h2>
                                <p class="lead">{{ app()->getLocale() == 'id' ? 'Mohon maaf, pendaftaran santri baru belum tersedia saat ini.' : 'Sorry, new student registration is not available at this time.' }}</p>
                                <p>{{ app()->getLocale() == 'id' ? 'Silakan hubungi kami untuk informasi lebih lanjut mengenai jadwal pendaftaran yang akan datang.' : 'Please contact us for more information about upcoming registration schedules.' }}</p>
                                <div class="mt-4">
                                    <a href="{{ route('contact') }}" class="btn btn-success btn-lg">
                                        <i class="fas fa-envelope me-2"></i>{{ app()->getLocale() == 'id' ? 'Hubungi Kami' : 'Contact Us' }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @else
            <div class="row">
                <!-- Left Column - Registration Information -->
                <div class="col-lg-8" data-aos="fade-up">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="card-title text-success mb-4 text-center">{{ app()->getLocale() == 'id' ? 'Informasi Pendaftaran' : 'Registration Information' }}</h2>

                            <div class="mb-4">
                                <h5><i class="fas fa-calendar-alt text-success me-2"></i> {{ app()->getLocale() == 'id' ? 'Jadwal Pendaftaran' : 'Registration Schedule' }}</h5>
                                @if(isset($currentSchedule))
                                    <div class="alert {{ $isOpen ? 'alert-success' : 'alert-warning' }}">
                                        <strong>{{ app()->getLocale() == 'id' ? 'Status:' : 'Status:' }}</strong>
                                        @if($isOpen)
                                            {{ app()->getLocale() == 'id' ? 'Pendaftaran Dibuka' : 'Registration Open' }}
                                        @else
                                            {{ app()->getLocale() == 'id' ? 'Pendaftaran Ditutup' : 'Registration Closed' }}
                                        @endif
                                    </div>
                                    <p>{{ app()->getLocale() == 'id' ? $currentSchedule->title : $currentSchedule->title_en }}</p>
                                    <p><strong>{{ app()->getLocale() == 'id' ? 'Periode:' : 'Period:' }}</strong> {{ $currentSchedule->start_date ? $currentSchedule->start_date->format('d M Y') : 'N/A' }} - {{ $currentSchedule->end_date ? $currentSchedule->end_date->format('d M Y') : 'N/A' }}</p>
                                    @if($currentSchedule->description)
                                        <div class="summernote-content">
                                            {!! app()->getLocale() == 'id' ? $currentSchedule->description : $currentSchedule->description_en !!}
                                        </div>
                                    @endif
                                @else
                                    <p>{{ app()->getLocale() == 'id' ? 'Pendaftaran dibuka sepanjang tahun, dengan prioritas untuk pendaftaran awal tahun ajaran.' : 'Registration is open throughout the year, with priority for early academic year registration.' }}</p>
                                @endif
                            </div>

                            <div class="mb-4">
                                <h5><i class="fas fa-file-alt text-success me-2"></i> {{ app()->getLocale() == 'id' ? 'Persyaratan Dokumen' : 'Document Requirements' }}</h5>
                                <div class="summernote-content">
                                    <ul>
                                        @if($requirements->count() > 0)
                                            @foreach($requirements as $requirement)
                                                <li>{{ app()->getLocale() == 'id' ? $requirement->name : $requirement->name_en }}
                                                    @if($requirement->description)
                                                        <small class="text-muted d-block">{!! app()->getLocale() == 'id' ? $requirement->description : $requirement->description_en !!}</small>
                                                    @endif
                                                </li>
                                            @endforeach
                                        @else
                                            <li>{{ app()->getLocale() == 'id' ? 'Fotokopi Akta Kelahiran' : 'Copy of Birth Certificate' }}</li>
                                            <li>{{ app()->getLocale() == 'id' ? 'Fotokopi Kartu Keluarga' : 'Copy of Family Card' }}</li>
                                            <li>{{ app()->getLocale() == 'id' ? 'Fotokopi Rapor Terakhir' : 'Copy of Latest Report Card' }}</li>
                                            <li>{{ app()->getLocale() == 'id' ? 'Pas Foto Berwarna 3x4 (4 lembar)' : 'Colored Passport Photo 3x4 (4 pieces)' }}</li>
                                            <li>{{ app()->getLocale() == 'id' ? 'Surat Keterangan Sehat dari Dokter' : 'Health Certificate from Doctor' }}</li>
                                        @endif
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5><i class="fas fa-money-bill-wave text-success me-2"></i> {{ app()->getLocale() == 'id' ? 'Biaya Pendaftaran' : 'Registration Fee' }}</h5>
                                @if($fees->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>{{ app()->getLocale() == 'id' ? 'Jenis Biaya' : 'Fee Type' }}</th>
                                                    <th>{{ app()->getLocale() == 'id' ? 'Jumlah' : 'Amount' }}</th>
                                                    <th>{{ app()->getLocale() == 'id' ? 'Keterangan' : 'Description' }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($fees as $fee)
                                                    <tr>
                                                        <td>{{ app()->getLocale() == 'id' ? $fee->name : $fee->name_en }}</td>
                                                        <td>Rp {{ number_format($fee->amount, 0, ',', '.') }}</td>
                                                        <td class="summernote-content">{!! app()->getLocale() == 'id' ? $fee->description : $fee->description_en !!}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p>{{ app()->getLocale() == 'id' ? 'Biaya pendaftaran sebesar Rp 500.000 (lima ratus ribu rupiah) yang dapat dibayarkan melalui transfer bank atau langsung di kantor kami.' : 'Registration fee of IDR 500,000 (five hundred thousand rupiah) which can be paid via bank transfer or directly at our office.' }}</p>
                                @endif
                            </div>

                            <div class="mb-4 registration-additional-info">
                                <h5><i class="fas fa-info-circle text-success me-2"></i> {{ app()->getLocale() == 'id' ? 'Informasi Tambahan' : 'Additional Information' }}</h5>
                                <div class="summernote-content">
                                    @if(isset($info) && $info->content)
                                        {!! app()->getLocale() == 'id' ? $info->content : $info->content_en !!}
                                    @else
                                        <p>{{ app()->getLocale() == 'id' ? 'Untuk informasi lebih lanjut, silakan hubungi kami melalui telepon atau email yang tertera di halaman Kontak.' : 'For more information, please contact us via phone or email listed on the Contact page.' }}</p>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-4">
                                <h5><i class="fas fa-search text-success me-2"></i> {{ app()->getLocale() == 'id' ? 'Cek Status Pendaftaran' : 'Check Registration Status' }}</h5>
                                <p>{{ app()->getLocale() == 'id' ? 'Jika Anda sudah mendaftar dan ingin memeriksa status pendaftaran, silakan masukkan nomor pendaftaran Anda di bawah ini:' : 'If you have registered and want to check your registration status, please enter your registration number below:' }}</p>

                                <form action="{{ route('registration.check-status') }}" method="POST" class="mt-3">
                                    @csrf
                                    <div class="input-group">
                                        <input type="text" name="registration_number" class="form-control" placeholder="{{ app()->getLocale() == 'id' ? 'Nomor Pendaftaran' : 'Registration Number' }}" required>
                                        <button type="submit" class="btn btn-success">{{ app()->getLocale() == 'id' ? 'Cek Status' : 'Check Status' }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Registration Image with Sticky Effect -->
                @if(isset($info) && $info->image)
                <div class="col-lg-4">
                    <div class="sticky-registration-image">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <img src="{{ asset('storage/' . $info->image) }}" alt="Registration Information" class="img-fluid rounded mb-3">
                                <div class="text-center mt-3">
                                    <a href="#registration-form" id="register-now-btn" class="btn btn-success btn-lg">
                                        {{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            @endif
        </div>
    </section>

    @if($hasSchedule)
    <!-- Registration Form -->
    <section id="registration-form" class="section-padding pt-5">
        <div class="container">
            <div class="section-title registration-form-container">
                <h2>{{ app()->getLocale() == 'id' ? 'Formulir Pendaftaran' : 'Registration Form' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Silakan isi formulir di bawah ini untuk mendaftar sebagai santri baru' : 'Please fill out the form below to register as a new student' }}</p>
            </div>

            @if(!$isOpen)
                <div class="alert alert-warning text-center mb-4 registration-form-container">
                    <h4 class="alert-heading">{{ app()->getLocale() == 'id' ? 'Pendaftaran Ditutup!' : 'Registration Closed!' }}</h4>
                    <p>{{ app()->getLocale() == 'id' ? 'Mohon maaf, pendaftaran saat ini sedang ditutup. Silakan periksa jadwal pendaftaran di atas.' : 'Sorry, registration is currently closed. Please check the registration schedule above.' }}</p>
                </div>
            @endif

            <div class="row">
                <div class="col-lg-10 mx-auto registration-form-container" data-aos="fade-up">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <form action="{{ route('registration.store') }}" method="POST" enctype="multipart/form-data" class="registration-form needs-validation" id="registration-form" novalidate {{ !$isOpen ? 'disabled' : '' }}>
                                @csrf
                                <!-- Extra CSRF token as backup -->
                                <input type="hidden" name="_token_backup" value="{{ csrf_token() }}" class="csrf-token-backup">

                                <!-- Personal Information -->
                                <div class="mb-4">
                                    <h5 class="border-bottom pb-2 text-success">{{ app()->getLocale() == 'id' ? 'Informasi Pribadi' : 'Personal Information' }}</h5>

                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="nik" class="form-label">{{ app()->getLocale() == 'id' ? 'NIK (Nomor Induk Kependudukan)' : 'National ID Number' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control numeric-only" id="nik" name="nik" value="{{ old('nik') }}" required maxlength="16" minlength="16" pattern="[0-9]{16}" inputmode="numeric">
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'NIK wajib diisi (16 digit angka).' : 'National ID Number is required (16 digits numbers only).' }}
                                            </div>
                                            <small class="form-text text-muted">{{ app()->getLocale() == 'id' ? 'Masukkan 16 digit NIK sesuai KTP/KK (hanya angka)' : 'Enter 16 digits National ID Number (numbers only)' }}</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="full_name" class="form-label">{{ app()->getLocale() == 'id' ? 'Nama Lengkap' : 'Full Name' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase name-field" id="full_name" name="full_name" value="{{ old('full_name') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Nama lengkap wajib diisi.' : 'Full name is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">{{ app()->getLocale() == 'id' ? 'Jenis Kelamin' : 'Gender' }} <span class="text-danger">*</span></label>
                                            <div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender" id="gender_male" value="male" {{ old('gender') == 'male' ? 'checked' : '' }} required>
                                                    <label class="form-check-label" for="gender_male">{{ app()->getLocale() == 'id' ? 'Laki-laki' : 'Male' }}</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" type="radio" name="gender" id="gender_female" value="female" {{ old('gender') == 'female' ? 'checked' : '' }} required>
                                                    <label class="form-check-label" for="gender_female">{{ app()->getLocale() == 'id' ? 'Perempuan' : 'Female' }}</label>
                                                </div>
                                                <div class="invalid-feedback">
                                                    {{ app()->getLocale() == 'id' ? 'Jenis kelamin wajib dipilih.' : 'Gender is required.' }}
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="place_of_birth" class="form-label">{{ app()->getLocale() == 'id' ? 'Tempat Lahir' : 'Place of Birth' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase" id="place_of_birth" name="place_of_birth" value="{{ old('place_of_birth') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Tempat lahir wajib diisi.' : 'Place of birth is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="date_of_birth" class="form-label">{{ app()->getLocale() == 'id' ? 'Tanggal Lahir' : 'Date of Birth' }} <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Tanggal lahir wajib diisi.' : 'Date of birth is required.' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Parent Information -->
                                <div class="mb-4">
                                    <h5 class="border-bottom pb-2 text-success">{{ app()->getLocale() == 'id' ? 'Informasi Orang Tua/Wali' : 'Parent/Guardian Information' }}</h5>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="father_name" class="form-label">{{ app()->getLocale() == 'id' ? 'Nama Ayah' : 'Father\'s Name' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase name-field" id="father_name" name="father_name" value="{{ old('father_name') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Nama ayah wajib diisi.' : 'Father\'s name is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="father_occupation" class="form-label">{{ app()->getLocale() == 'id' ? 'Pekerjaan Ayah' : 'Father\'s Occupation' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase" id="father_occupation" name="father_occupation" value="{{ old('father_occupation') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Pekerjaan ayah wajib diisi.' : 'Father\'s occupation is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="mother_name" class="form-label">{{ app()->getLocale() == 'id' ? 'Nama Ibu' : 'Mother\'s Name' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase name-field" id="mother_name" name="mother_name" value="{{ old('mother_name') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Nama ibu wajib diisi.' : 'Mother\'s name is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="mother_occupation" class="form-label">{{ app()->getLocale() == 'id' ? 'Pekerjaan Ibu' : 'Mother\'s Occupation' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase" id="mother_occupation" name="mother_occupation" value="{{ old('mother_occupation') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Pekerjaan ibu wajib diisi.' : 'Mother\'s occupation is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="parent_name" class="form-label">{{ app()->getLocale() == 'id' ? 'Nama Wali (jika ada)' : 'Guardian Name (if any)' }}</label>
                                            <input type="text" class="form-control text-uppercase name-field" id="parent_name" name="parent_name" value="{{ old('parent_name') }}">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="parent_occupation" class="form-label">{{ app()->getLocale() == 'id' ? 'Pekerjaan Wali' : 'Guardian Occupation' }}</label>
                                            <input type="text" class="form-control text-uppercase" id="parent_occupation" name="parent_occupation" value="{{ old('parent_occupation') }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="mb-4">
                                    <h5 class="border-bottom pb-2 text-success">{{ app()->getLocale() == 'id' ? 'Informasi Kontak' : 'Contact Information' }}</h5>

                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="address" class="form-label">{{ app()->getLocale() == 'id' ? 'Alamat Lengkap' : 'Full Address' }} <span class="text-danger">*</span></label>
                                            <textarea class="form-control text-uppercase" id="address" name="address" rows="3" required>{{ old('address') }}</textarea>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Alamat lengkap wajib diisi.' : 'Full address is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">{{ app()->getLocale() == 'id' ? 'Nomor Telepon' : 'Phone Number' }} <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control" id="phone" name="phone" value="{{ old('phone') }}" required>
                                            <input type="hidden" id="phone_full" name="phone_full">
                                            <div class="invalid-feedback phone-error">
                                                {{ app()->getLocale() == 'id' ? 'Nomor telepon wajib diisi dengan format yang benar.' : 'Phone number is required in the correct format.' }}
                                            </div>
                                            <small class="form-text text-muted">{{ app()->getLocale() == 'id' ? 'Pilih kode negara dan masukkan nomor tanpa angka 0 di awal' : 'Select country code and enter number without leading 0' }}</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">{{ app()->getLocale() == 'id' ? 'Email' : 'Email' }}</label>
                                            <input type="email" class="form-control" id="email" name="email" value="{{ old('email') }}">
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Format email tidak valid.' : 'Invalid email format.' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Educational Background -->
                                <div class="mb-4">
                                    <h5 class="border-bottom pb-2 text-success">{{ app()->getLocale() == 'id' ? 'Latar Belakang Pendidikan' : 'Educational Background' }}</h5>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="last_education" class="form-label">{{ app()->getLocale() == 'id' ? 'Pendidikan Terakhir' : 'Last Education' }} <span class="text-danger">*</span></label>
                                            <select class="form-select" id="last_education" name="last_education" required>
                                                <option value="" selected disabled>{{ app()->getLocale() == 'id' ? 'Pilih Pendidikan Terakhir' : 'Select Last Education' }}</option>
                                                <option value="SD/MI" {{ old('last_education') == 'SD/MI' ? 'selected' : '' }}>{{ app()->getLocale() == 'id' ? 'SD/MI' : 'Elementary School' }}</option>
                                                <option value="SMP/MTs" {{ old('last_education') == 'SMP/MTs' ? 'selected' : '' }}>{{ app()->getLocale() == 'id' ? 'SMP/MTs' : 'Junior High School' }}</option>
                                                <option value="SMA/MA/SMK" {{ old('last_education') == 'SMA/MA/SMK' ? 'selected' : '' }}>{{ app()->getLocale() == 'id' ? 'SMA/MA/SMK' : 'Senior High School' }}</option>
                                            </select>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Pendidikan terakhir wajib dipilih.' : 'Last education is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="previous_school" class="form-label">{{ app()->getLocale() == 'id' ? 'Sekolah Asal' : 'Previous School' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control text-uppercase" id="previous_school" name="previous_school" value="{{ old('previous_school') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Sekolah asal wajib diisi.' : 'Previous school is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="nisn" class="form-label">{{ app()->getLocale() == 'id' ? 'NISN' : 'Student ID Number' }}</label>
                                            <input type="text" class="form-control numeric-only" id="nisn" name="nisn" value="{{ old('nisn') }}" pattern="[0-9]*" inputmode="numeric" maxlength="10">
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'NISN harus berupa angka.' : 'Student ID Number must be numbers only.' }}
                                            </div>
                                            <small class="form-text text-muted">{{ app()->getLocale() == 'id' ? 'Masukkan NISN jika ada (hanya angka)' : 'Enter Student ID Number if available (numbers only)' }}</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="graduation_year" class="form-label">{{ app()->getLocale() == 'id' ? 'Tahun Lulus' : 'Graduation Year' }} <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control numeric-only" id="graduation_year" name="graduation_year" value="{{ old('graduation_year') }}" min="2000" max="{{ date('Y') }}" required inputmode="numeric">
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Tahun lulus wajib diisi (hanya angka).' : 'Graduation year is required (numbers only).' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Interests and Goals -->
                                <div class="mb-4">
                                    <h5 class="border-bottom pb-2 text-success">{{ app()->getLocale() == 'id' ? 'Minat dan Tujuan' : 'Interests and Goals' }}</h5>

                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="education_unit_id" class="form-label">{{ app()->getLocale() == 'id' ? 'Jenjang yang Dipilih' : 'Selected Education Level' }} <span class="text-danger">*</span></label>
                                            <select class="form-select" id="education_unit_id" name="education_unit_id" required>
                                                <option value="" selected disabled>{{ app()->getLocale() == 'id' ? 'Pilih Jenjang Pendidikan' : 'Select Education Level' }}</option>
                                                @foreach(\App\Models\EducationUnit::formal()->active()->get() as $unit)
                                                    <option value="{{ $unit->id }}" {{ old('education_unit_id') == $unit->id ? 'selected' : '' }}>
                                                        {{ app()->getLocale() == 'id' ? $unit->name : $unit->name_en }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Jenjang pendidikan wajib dipilih.' : 'Education level is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-12 mb-3">
                                            <label for="reason" class="form-label">{{ app()->getLocale() == 'id' ? 'Alasan Ingin Mondok' : 'Reason for Joining Pesantren' }} <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="reason" name="reason" rows="3" required>{{ old('reason') }}</textarea>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Alasan ingin mondok wajib diisi.' : 'Reason for joining pesantren is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="hobby" class="form-label">{{ app()->getLocale() == 'id' ? 'Hobi' : 'Hobby' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control capitalize-words" id="hobby" name="hobby" value="{{ old('hobby') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Hobi wajib diisi.' : 'Hobby is required.' }}
                                            </div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="ambition" class="form-label">{{ app()->getLocale() == 'id' ? 'Cita-cita' : 'Ambition' }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control capitalize-words" id="ambition" name="ambition" value="{{ old('ambition') }}" required>
                                            <div class="invalid-feedback">
                                                {{ app()->getLocale() == 'id' ? 'Cita-cita wajib diisi.' : 'Ambition is required.' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="mb-4">
                                    <h5 class="border-bottom pb-2 text-success">{{ app()->getLocale() == 'id' ? 'Informasi Tambahan' : 'Additional Information' }}</h5>

                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="notes" class="form-label">{{ app()->getLocale() == 'id' ? 'Catatan Tambahan' : 'Additional Notes' }}</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms_accepted" name="terms_accepted" required>
                                        <label class="form-check-label" for="terms_accepted">
                                            @if(app()->getLocale() == 'id')
                                                Saya menyatakan bahwa informasi yang diberikan adalah benar dan saya setuju dengan <a href="#" class="text-success terms-link">syarat dan ketentuan</a> pendaftaran.
                                            @else
                                                I declare that the information provided is true and I agree to the <a href="#" class="text-success terms-link">terms and conditions</a> of registration.
                                            @endif
                                        </label>
                                        <div class="invalid-feedback">
                                            {{ app()->getLocale() == 'id' ? 'Anda harus menyetujui syarat dan ketentuan.' : 'You must agree to the terms and conditions.' }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Terms and Conditions Dialog -->
                                <div id="terms-dialog">
                                    <div class="terms-dialog-content">
                                        <span class="terms-dialog-close">&times;</span>
                                        <h4 class="text-success fw-bold">{{ app()->getLocale() == 'id' ? 'Syarat dan Ketentuan Pendaftaran' : 'Registration Terms and Conditions' }}</h4>
                                        <div class="terms-dialog-body">
                                            @if(app()->getLocale() == 'id')
                                                <h6 class="fw-bold">Syarat dan Ketentuan Pendaftaran Santri Baru</h6>
                                                <p>Dengan mendaftar sebagai santri baru di Pondok Pesantren Nurul Hayah 4, calon santri dan orang tua/wali menyetujui syarat dan ketentuan berikut:</p>

                                                <h6 class="mt-4 fw-bold">1. Persyaratan Umum</h6>
                                                <ul>
                                                    <li>Calon santri harus memenuhi persyaratan usia sesuai dengan jenjang pendidikan yang dipilih.</li>
                                                    <li>Calon santri harus dalam kondisi sehat jasmani dan rohani.</li>
                                                    <li>Calon santri tidak memiliki riwayat penyakit kronis yang dapat mengganggu proses belajar.</li>
                                                    <li>Calon santri dan orang tua/wali bersedia mematuhi seluruh peraturan yang berlaku di Pondok Pesantren Nurul Hayah 4.</li>
                                                </ul>

                                                <h6 class="mt-4 fw-bold">2. Proses Pendaftaran</h6>
                                                <ul>
                                                    <li>Pendaftaran dilakukan secara online melalui website resmi Pondok Pesantren Nurul Hayah 4.</li>
                                                    <li>Seluruh data yang diisi dalam formulir pendaftaran harus benar dan dapat dipertanggungjawabkan.</li>
                                                    <li>Pemberian informasi palsu dapat mengakibatkan pembatalan pendaftaran atau penerimaan.</li>
                                                    <li>Pendaftaran dianggap sah setelah melengkapi seluruh dokumen yang dipersyaratkan dan melakukan pembayaran biaya pendaftaran.</li>
                                                </ul>

                                                <h6 class="mt-4 fw-bold">3. Seleksi dan Penerimaan</h6>
                                                <ul>
                                                    <li>Seluruh calon santri akan mengikuti proses seleksi yang meliputi tes tertulis, wawancara, dan tes baca Al-Qur'an.</li>
                                                    <li>Keputusan penerimaan santri baru sepenuhnya menjadi hak prerogatif Pondok Pesantren Nurul Hayah 4 dan tidak dapat diganggu gugat.</li>
                                                    <li>Hasil seleksi akan diumumkan melalui website resmi dan/atau dihubungi langsung oleh pihak pesantren.</li>
                                                </ul>

                                                <h6 class="mt-4 fw-bold">4. Keuangan</h6>
                                                <ul>
                                                    <li>Biaya pendaftaran tidak dapat dikembalikan dengan alasan apapun.</li>
                                                    <li>Calon santri yang diterima wajib membayar biaya masuk dan biaya pendidikan sesuai dengan ketentuan yang berlaku.</li>
                                                    <li>Pembayaran biaya pendidikan dilakukan sesuai dengan jadwal yang telah ditentukan.</li>
                                                    <li>Keterlambatan pembayaran dapat dikenakan denda sesuai ketentuan yang berlaku.</li>
                                                </ul>

                                                <p class="mt-4">Dengan mencentang kotak persetujuan pada formulir pendaftaran, calon santri dan orang tua/wali menyatakan telah membaca, memahami, dan menyetujui seluruh syarat dan ketentuan pendaftaran santri baru Pondok Pesantren Nurul Hayah 4.</p>
                                            @else
                                                <h6 class="fw-bold">Terms and Conditions for New Student Registration</h6>
                                                <p>By registering as a new student at Nurul Hayah 4 Islamic Cyber Boarding School, prospective students and parents/guardians agree to the following terms and conditions:</p>

                                                <h6 class="mt-4 fw-bold">1. General Requirements</h6>
                                                <ul>
                                                    <li>Prospective students must meet the age requirements according to the chosen education level.</li>
                                                    <li>Prospective students must be in good physical and mental health.</li>
                                                    <li>Prospective students do not have a history of chronic diseases that may interfere with the learning process.</li>
                                                    <li>Prospective students and parents/guardians are willing to comply with all regulations applicable at Nurul Hayah 4 Islamic Cyber Boarding School.</li>
                                                </ul>

                                                <h6 class="mt-4 fw-bold">2. Registration Process</h6>
                                                <ul>
                                                    <li>Registration is done online through the official website of Nurul Hayah 4 Islamic Cyber Boarding School.</li>
                                                    <li>All data filled in the registration form must be true and accountable.</li>
                                                    <li>Providing false information may result in cancellation of registration or admission.</li>
                                                    <li>Registration is considered valid after completing all required documents and making payment of the registration fee.</li>
                                                </ul>

                                                <h6 class="mt-4 fw-bold">3. Selection and Admission</h6>
                                                <ul>
                                                    <li>All prospective students will undergo a selection process that includes written tests, interviews, and Qur'an reading tests.</li>
                                                    <li>The decision to admit new students is entirely the prerogative of Nurul Hayah 4 Islamic Cyber Boarding School and cannot be contested.</li>
                                                    <li>Selection results will be announced through the official website and/or contacted directly by the school.</li>
                                                </ul>

                                                <h6 class="mt-4 fw-bold">4. Finance</h6>
                                                <ul>
                                                    <li>Registration fees cannot be refunded for any reason.</li>
                                                    <li>Accepted students are required to pay entrance fees and education fees in accordance with applicable regulations.</li>
                                                    <li>Payment of education fees is made according to the predetermined schedule.</li>
                                                    <li>Late payments may be subject to fines according to applicable regulations.</li>
                                                </ul>

                                                <p class="mt-4">By checking the approval box on the registration form, prospective students and parents/guardians declare that they have read, understood, and agreed to all the terms and conditions for new student registration at Nurul Hayah 4 Islamic Cyber Boarding School.</p>
                                            @endif
                                        </div>
                                        <div class="terms-dialog-buttons">
                                            <button type="button" class="btn btn-secondary" id="decline-terms">{{ app()->getLocale() == 'id' ? 'Tidak Setuju' : 'Decline' }}</button>
                                            <button type="button" class="btn btn-success" id="accept-terms">{{ app()->getLocale() == 'id' ? 'Saya Setuju' : 'I Agree' }}</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <!-- Display any error messages -->
                                    @if(session('error'))
                                    <div class="alert alert-danger mb-3">
                                        {{ session('error') }}
                                    </div>
                                    @endif

                                    <button type="submit" class="btn btn-success btn-lg" id="submit-registration-btn" {{ !$isOpen ? 'disabled' : '' }}>{{ app()->getLocale() == 'id' ? 'Kirim Pendaftaran' : 'Submit Registration' }}</button>
                                    @if(!$isOpen)
                                        <div class="text-danger mt-2">
                                            <small>{{ app()->getLocale() == 'id' ? 'Pendaftaran saat ini ditutup.' : 'Registration is currently closed.' }}</small>
                                        </div>
                                    @endif
                                    <div id="form-submission-status" class="mt-3" style="display: none;"></div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

@endsection

@push('scripts')
@if($hasSchedule)
<!-- International Telephone Input JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/intlTelInput.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/utils.js"></script>

<!-- Form validation is handled in registration-form.js -->

<!-- Custom Registration Form JS -->
<script src="{{ asset('js/registration-form.js') }}"></script>

<!-- Sticky Image Position JS -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Adjust sticky position based on scroll
        const stickyImage = document.querySelector('.sticky-registration-image');
        if (stickyImage) {
            // Function to update sticky position
            function updateStickyPosition() {
                const navbar = document.querySelector('.navbar');
                const navbarHeight = navbar ? navbar.offsetHeight : 0;

                // Set top position with significant padding to ensure card is fully visible
                stickyImage.style.top = (navbarHeight + 50) + 'px';
            }

            // Initial call to set correct position on page load
            updateStickyPosition();

            // Update on scroll
            window.addEventListener('scroll', updateStickyPosition);

            // Also update on window resize (in case navbar height changes)
            window.addEventListener('resize', updateStickyPosition);
        }

        // Handle terms dialog close button
        const termsDialogClose = document.querySelector('.terms-dialog-close');
        if (termsDialogClose) {
            termsDialogClose.addEventListener('click', function() {
                const termsDialog = document.getElementById('terms-dialog');
                if (termsDialog) {
                    termsDialog.style.display = 'none';
                    document.body.style.overflow = ''; // Restore scrolling

                    // Uncheck the checkbox when closing with X
                    const termsCheckbox = document.getElementById('terms_accepted');
                    if (termsCheckbox) {
                        termsCheckbox.checked = false;
                    }
                }
            });
        }

        // Handle terms link click
        const termsLinks = document.querySelectorAll('.terms-link');
        termsLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const termsDialog = document.getElementById('terms-dialog');
                if (termsDialog) {
                    termsDialog.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // Prevent scrolling behind dialog
                }
            });
        });
    });
</script>
@endif
@endpush





