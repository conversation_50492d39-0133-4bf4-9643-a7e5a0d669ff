<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Webhook;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WebhookController extends Controller
{
    /**
     * The webhook service instance.
     *
     * @var \App\Services\WebhookService
     */
    protected $webhookService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Display a listing of the webhooks.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $webhooks = Webhook::all();

        return response()->json([
            'success' => true,
            'data' => $webhooks,
        ]);
    }

    /**
     * Store a newly created webhook in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:255',
            'event' => 'required|string|max:255',
            'secret' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'headers' => 'nullable|array',
            'payload_template' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Create the webhook
        $webhook = Webhook::create($data);

        // Generate API key
        $apiKey = $webhook->generateApiKey();

        return response()->json([
            'success' => true,
            'message' => 'Webhook created successfully',
            'data' => $webhook,
            'api_key' => $apiKey, // Include API key in response
        ], 201);
    }

    /**
     * Display the specified webhook.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $webhook = Webhook::findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $webhook,
        ]);
    }

    /**
     * Update the specified webhook in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $webhook = Webhook::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'url' => 'url|max:255',
            'event' => 'string|max:255',
            'secret' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'headers' => 'nullable|array',
            'payload_template' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $webhook->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Webhook updated successfully',
            'data' => $webhook,
        ]);
    }

    /**
     * Remove the specified webhook from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $webhook = Webhook::findOrFail($id);
        $webhook->delete();

        return response()->json([
            'success' => true,
            'message' => 'Webhook deleted successfully',
        ]);
    }

    /**
     * Test the specified webhook.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function test($id)
    {
        $webhook = Webhook::findOrFail($id);

        $result = $this->webhookService->testWebhook($webhook);

        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'Webhook test was successful',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Webhook test failed',
                'error' => $webhook->last_error,
            ], 500);
        }
    }

    /**
     * Regenerate API key for the specified webhook.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function regenerateApiKey($id)
    {
        $webhook = Webhook::findOrFail($id);

        $apiKey = $webhook->generateApiKey();

        return response()->json([
            'success' => true,
            'message' => 'API key regenerated successfully',
            'api_key' => $apiKey,
        ]);
    }
}
