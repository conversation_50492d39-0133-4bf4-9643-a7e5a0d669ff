<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Display a listing of the pages.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $pages = Page::active()->get();
        return view('public.pages.index', compact('pages'));
    }

    /**
     * Display the specified page.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $page = Page::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return view('public.pages.show', compact('page'));
    }
}
