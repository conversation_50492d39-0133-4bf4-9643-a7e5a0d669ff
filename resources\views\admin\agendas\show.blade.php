@extends('admin.layouts.app')

@section('title', 'View Agenda')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Agenda</h1>
        <div>
            <a href="{{ route('admin.agendas.edit', $agenda) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.agendas.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body text-center">
                    @if($agenda->image)
                        <img src="{{ asset('storage/' . $agenda->image) }}" alt="{{ $agenda->title }}" class="img-fluid rounded">
                    @else
                        <div class="alert alert-info">No image available</div>
                    @endif
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Details</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>ID:</th>
                            <td>{{ $agenda->id }}</td>
                        </tr>
                        <tr>
                            <th>Date:</th>
                            <td>{{ $agenda->date ? $agenda->date->format('d M Y') : 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <th>Time:</th>
                            <td>{{ $agenda->time ? $agenda->time->format('H:i') : 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <th>Order:</th>
                            <td>{{ $agenda->order }}</td>
                        </tr>

                        <tr>
                            <th>Status:</th>
                            <td>
                                @if($agenda->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Created:</th>
                            <td>{{ $agenda->created_at ? $agenda->created_at->format('d M Y H:i') : 'Not available' }}</td>
                        </tr>
                        <tr>
                            <th>Updated:</th>
                            <td>{{ $agenda->updated_at ? $agenda->updated_at->format('d M Y H:i') : 'Not available' }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <h5 class="mt-3">{{ $agenda->title }}</h5>

                            <h6 class="mt-3">Description:</h6>
                            <div class="p-3 bg-light rounded">
                                {!! $agenda->description ?? 'No description available.' !!}
                            </div>

                            <h6 class="mt-3">Location:</h6>
                            <p>{{ $agenda->location ?? 'Not specified' }}</p>

                            <h6 class="mt-3">Organizer:</h6>
                            <p>{{ $agenda->organizer ?? 'Not specified' }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <h5 class="mt-3">{{ $agenda->title_en }}</h5>

                            <h6 class="mt-3">Description:</h6>
                            <div class="p-3 bg-light rounded">
                                {!! $agenda->description_en ?? 'No description available.' !!}
                            </div>

                            <h6 class="mt-3">Location:</h6>
                            <p>{{ $agenda->location_en ?? 'Not specified' }}</p>

                            <h6 class="mt-3">Organizer:</h6>
                            <p>{{ $agenda->organizer_en ?? 'Not specified' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
