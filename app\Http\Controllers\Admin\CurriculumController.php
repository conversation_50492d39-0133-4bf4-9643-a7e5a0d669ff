<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Curriculum;
use App\Models\EducationUnit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CurriculumController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Get active tab from request or default to 'formal'
        $activeTab = $request->query('tab', 'formal');

        // Get curricula based on the active tab
        $curricula = Curriculum::where('edu_type', $activeTab)
            ->orderBy('order', 'asc')
            ->get();

        return view('admin.curricula.index', compact('curricula', 'activeTab'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // Get education type from request or default to 'formal'
        $eduType = $request->query('type', 'formal');

        // Get all education units for dropdown (both formal and non-formal)
        // Order by ID to maintain the order of creation
        $educationUnits = EducationUnit::orderBy('id', 'asc')->get();

        return view('admin.curricula.create', compact('educationUnits', 'eduType'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Log request data for debugging
        \Log::info('Curriculum Store Request Data:', $request->all());

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'edu_type' => 'required|in:formal,non-formal',
            'education_unit_id' => 'nullable|exists:education_units,id',

            // Formal curriculum fields
            'national_curriculum' => 'nullable|string',
            'national_curriculum_en' => 'nullable|string',
            'general_subjects' => 'nullable|string',
            'general_subjects_en' => 'nullable|string',
            'religious_subjects' => 'nullable|string',
            'religious_subjects_en' => 'nullable|string',
            'local_content' => 'nullable|string',
            'local_content_en' => 'nullable|string',
            'supporting_activities' => 'nullable|string',
            'supporting_activities_en' => 'nullable|string',
            'extracurricular' => 'nullable|string',
            'extracurricular_en' => 'nullable|string',
            'special_program' => 'nullable|string',
            'special_program_en' => 'nullable|string',

            'learning_approach' => 'nullable|string',
            'learning_approach_en' => 'nullable|string',
            'assessment_system' => 'nullable|string',
            'assessment_system_en' => 'nullable|string',
            'assessment_evaluation' => 'nullable|string',
            'assessment_evaluation_en' => 'nullable|string',
            'graduation_certificates' => 'nullable|string',
            'graduation_certificates_en' => 'nullable|string',

            // Non-formal curriculum fields
            'educational_goals' => 'nullable|string',
            'educational_goals_en' => 'nullable|string',
            'core_textbooks_studied' => 'nullable|string',
            'core_textbooks_studied_en' => 'nullable|string',
            'fields_of_study' => 'nullable|string',
            'fields_of_study_en' => 'nullable|string',
            'class_levels' => 'nullable|string',
            'class_levels_en' => 'nullable|string',
            'study_schedule' => 'nullable|string',
            'study_schedule_en' => 'nullable|string',
            'teaching_methods' => 'nullable|string',
            'teaching_methods_en' => 'nullable|string',
            'language_of_instruction' => 'nullable|string',
            'language_of_instruction_en' => 'nullable|string',
            'graduation_certificate' => 'nullable|string',
            'graduation_certificate_en' => 'nullable|string',
        ]);

        $data = $request->all();

        // Set default values
        $data['is_active'] = true; // Always set to active when creating a new curriculum
        $data['order'] = $data['order'] ?? 0;

        // Log data before create
        \Log::info('Curriculum Create Data:', $data);

        // Create curriculum with explicit handling of problematic fields
        $curriculum = new Curriculum();
        $curriculum->fill($data);

        // First save the curriculum to get an ID
        $result = $curriculum->save();

        // Then update the problematic fields directly in the database
        \DB::table('curricula')
            ->where('id', $curriculum->id)
            ->update([
                'supporting_activities' => $request->input('supporting_activities_hidden') ?: $request->input('supporting_activities'),
                'supporting_activities_en' => $request->input('supporting_activities_en_hidden') ?: $request->input('supporting_activities_en'),
                'language_of_instruction' => $request->input('language_of_instruction_hidden') ?: $request->input('language_of_instruction'),
                'language_of_instruction_en' => $request->input('language_of_instruction_en_hidden') ?: $request->input('language_of_instruction_en'),
                'teaching_methods' => $request->input('teaching_methods_hidden') ?: $request->input('teaching_methods'),
                'teaching_methods_en' => $request->input('teaching_methods_en_hidden') ?: $request->input('teaching_methods_en'),
                'assessment_evaluation' => $request->input('assessment_evaluation_hidden') ?: $request->input('assessment_evaluation') ?: $request->input('assessment_system'),
                'assessment_evaluation_en' => $request->input('assessment_evaluation_en_hidden') ?: $request->input('assessment_evaluation_en') ?: $request->input('assessment_system_en'),
                'graduation_certificates' => $request->input('graduation_certificates_hidden') ?: $request->input('graduation_certificates') ?: $request->input('graduation_certificate'),
                'graduation_certificates_en' => $request->input('graduation_certificates_en_hidden') ?: $request->input('graduation_certificates_en') ?: $request->input('graduation_certificate_en'),
            ]);

        // Log result and created model
        \Log::info('Curriculum Create Result:', [$result]);
        \Log::info('Curriculum After Create:', $curriculum->toArray());

        return redirect()->route('admin.curricula.index', ['tab' => $request->edu_type])
            ->with('success', 'Curriculum created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $curriculum = Curriculum::findOrFail($id);
        return view('admin.curricula.show', compact('curriculum'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $curriculum = Curriculum::findOrFail($id);

        // Get all education units for dropdown (both formal and non-formal)
        // Order by ID to maintain the order of creation
        $educationUnits = EducationUnit::orderBy('id', 'asc')->get();

        return view('admin.curricula.edit', compact('curriculum', 'educationUnits'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $curriculum = Curriculum::findOrFail($id);

        // Log request data for debugging
        \Log::info('Curriculum Update Request Data:', $request->all());

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'edu_type' => 'required|in:formal,non-formal',
            'education_unit_id' => 'nullable|exists:education_units,id',

            // Formal curriculum fields
            'national_curriculum' => 'nullable|string',
            'national_curriculum_en' => 'nullable|string',
            'general_subjects' => 'nullable|string',
            'general_subjects_en' => 'nullable|string',
            'religious_subjects' => 'nullable|string',
            'religious_subjects_en' => 'nullable|string',
            'local_content' => 'nullable|string',
            'local_content_en' => 'nullable|string',
            'supporting_activities' => 'nullable|string',
            'supporting_activities_en' => 'nullable|string',
            'extracurricular' => 'nullable|string',
            'extracurricular_en' => 'nullable|string',
            'special_program' => 'nullable|string',
            'special_program_en' => 'nullable|string',

            'learning_approach' => 'nullable|string',
            'learning_approach_en' => 'nullable|string',
            'assessment_system' => 'nullable|string',
            'assessment_system_en' => 'nullable|string',
            'assessment_evaluation' => 'nullable|string',
            'assessment_evaluation_en' => 'nullable|string',
            'graduation_certificates' => 'nullable|string',
            'graduation_certificates_en' => 'nullable|string',

            // Non-formal curriculum fields
            'educational_goals' => 'nullable|string',
            'educational_goals_en' => 'nullable|string',
            'core_textbooks_studied' => 'nullable|string',
            'core_textbooks_studied_en' => 'nullable|string',
            'fields_of_study' => 'nullable|string',
            'fields_of_study_en' => 'nullable|string',
            'class_levels' => 'nullable|string',
            'class_levels_en' => 'nullable|string',
            'study_schedule' => 'nullable|string',
            'study_schedule_en' => 'nullable|string',
            'teaching_methods' => 'nullable|string',
            'teaching_methods_en' => 'nullable|string',
            'language_of_instruction' => 'nullable|string',
            'language_of_instruction_en' => 'nullable|string',
            'graduation_certificate' => 'nullable|string',
            'graduation_certificate_en' => 'nullable|string',
        ]);

        $data = $request->all();

        // Set default values
        $data['is_active'] = true; // Always set to active
        $data['order'] = $data['order'] ?? 0;

        // Log data before update
        \Log::info('Curriculum Update Data:', $data);

        // Explicitly set fields that might be problematic - with direct DB access
        \DB::table('curricula')
            ->where('id', $curriculum->id)
            ->update([
                'supporting_activities' => $request->input('supporting_activities_hidden') ?: $request->input('supporting_activities'),
                'supporting_activities_en' => $request->input('supporting_activities_en_hidden') ?: $request->input('supporting_activities_en'),
                'language_of_instruction' => $request->input('language_of_instruction_hidden') ?: $request->input('language_of_instruction'),
                'language_of_instruction_en' => $request->input('language_of_instruction_en_hidden') ?: $request->input('language_of_instruction_en'),
                'teaching_methods' => $request->input('teaching_methods_hidden') ?: $request->input('teaching_methods'),
                'teaching_methods_en' => $request->input('teaching_methods_en_hidden') ?: $request->input('teaching_methods_en'),
                'assessment_evaluation' => $request->input('assessment_evaluation_hidden') ?: $request->input('assessment_evaluation') ?: $request->input('assessment_system'),
                'assessment_evaluation_en' => $request->input('assessment_evaluation_en_hidden') ?: $request->input('assessment_evaluation_en') ?: $request->input('assessment_system_en'),
                'graduation_certificates' => $request->input('graduation_certificates_hidden') ?: $request->input('graduation_certificates') ?: $request->input('graduation_certificate'),
                'graduation_certificates_en' => $request->input('graduation_certificates_en_hidden') ?: $request->input('graduation_certificates_en') ?: $request->input('graduation_certificate_en'),
            ]);

        // Update the model with the rest of the data
        $result = $curriculum->update($data);

        // Log result and updated model
        \Log::info('Curriculum Update Result:', [$result]);
        \Log::info('Curriculum After Update:', $curriculum->toArray());

        return redirect()->route('admin.curricula.index', ['tab' => $curriculum->edu_type])
            ->with('success', 'Curriculum updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $curriculum = Curriculum::findOrFail($id);
        $eduType = $curriculum->edu_type;



        $curriculum->delete();

        return redirect()->route('admin.curricula.index', ['tab' => $eduType])
            ->with('success', 'Curriculum deleted successfully.');
    }
}
