<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixLeadersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if the columns exist
        if (!Schema::hasColumn('leaders', 'motto')) {
            // Add the motto column
            DB::statement('ALTER TABLE leaders ADD COLUMN motto TEXT NULL AFTER position_en');
        }

        if (!Schema::hasColumn('leaders', 'motto_en')) {
            // Add the motto_en column
            DB::statement('ALTER TABLE leaders ADD COLUMN motto_en TEXT NULL AFTER motto');
        }

        $this->command->info('Motto columns added to leaders table.');
    }
}
