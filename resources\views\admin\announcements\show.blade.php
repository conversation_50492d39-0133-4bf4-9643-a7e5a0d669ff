@extends('admin.layouts.app')

@section('title', 'View Announcement')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Announcement</h1>
        <div>
            <a href="{{ route('admin.announcements.edit', $announcement) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.announcements.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body text-center">
                    @if($announcement->image)
                        <img src="{{ asset('storage/' . $announcement->image) }}" alt="{{ $announcement->title }}" class="img-fluid rounded">
                    @else
                        <div class="alert alert-info">No image available</div>
                    @endif
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">File Attachment</h5>
                </div>
                <div class="card-body text-center">
                    @if($announcement->file)
                        <a href="{{ asset('storage/' . $announcement->file) }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-file-pdf me-2"></i> View PDF File
                        </a>
                        <p class="mt-2 text-muted small">{{ basename($announcement->file) }}</p>
                    @else
                        <div class="alert alert-info">No file attached</div>
                    @endif
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Details</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>ID:</th>
                            <td>{{ $announcement->id }}</td>
                        </tr>
                        <tr>
                            <th>Start Date:</th>
                            <td>{{ $announcement->start_date->format('d M Y') }}</td>
                        </tr>
                        <tr>
                            <th>End Date:</th>
                            <td>{{ $announcement->end_date->format('d M Y') }}</td>
                        </tr>
                        <tr>
                            <th>Order:</th>
                            <td>{{ $announcement->order }}</td>
                        </tr>
                        <tr>
                            <th>Featured:</th>
                            <td>
                                @if($announcement->is_featured)
                                    <span class="badge bg-warning">Yes</span>
                                @else
                                    <span class="badge bg-secondary">No</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                @if($announcement->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Created:</th>
                            <td>{{ $announcement->created_at->format('d M Y H:i') }}</td>
                        </tr>
                        <tr>
                            <th>Updated:</th>
                            <td>{{ $announcement->updated_at->format('d M Y H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Content</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <h5 class="mt-3">{{ $announcement->title }}</h5>
                            <div class="p-3 bg-light rounded">
                                {!! $announcement->content !!}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <h5 class="mt-3">{{ $announcement->title_en }}</h5>
                            <div class="p-3 bg-light rounded">
                                {!! $announcement->content_en !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
