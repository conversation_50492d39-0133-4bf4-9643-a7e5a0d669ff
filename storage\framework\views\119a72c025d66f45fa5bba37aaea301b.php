<?php $__env->startSection('title', app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('agenda')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Agenda' : 'Agenda'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($agendaItem->title, 30) : \Illuminate\Support\Str::limit($agendaItem->title_en, 30)); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Agenda Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 main-content">
                    <div class="card shadow-sm mb-4" data-aos="fade-up">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-4">
                                <div class="agenda-date me-4">
                                    <div class="day"><?php echo e($agendaItem->date->format('d')); ?></div>
                                    <div class="month"><?php echo e($agendaItem->date->format('M')); ?></div>
                                </div>
                                <div>
                                    <h3><?php echo e(app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en); ?></h3>
                                    <div class="agenda-info">
                                        <p><i class="far fa-clock me-1"></i> <?php echo e($agendaItem->time->format('H:i')); ?></p>
                                        <p><i class="fas fa-map-marker-alt me-1"></i> <?php echo e(app()->getLocale() == 'id' ? $agendaItem->location : $agendaItem->location_en); ?></p>
                                        <?php if($agendaItem->organizer): ?>
                                            <p><i class="fas fa-user me-1"></i> <?php echo e(app()->getLocale() == 'id' ? $agendaItem->organizer : $agendaItem->organizer_en); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <?php if($agendaItem->image): ?>
                                <div class="text-center mb-4">
                                    <img src="<?php echo e(asset('storage/' . $agendaItem->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en); ?>" class="img-fluid rounded">
                                </div>
                            <?php endif; ?>

                            <div class="agenda-content">
                                <h4><?php echo e(app()->getLocale() == 'id' ? 'Deskripsi' : 'Description'); ?></h4>
                                <div class="mb-4">
                                    <?php echo app()->getLocale() == 'id' ? $agendaItem->description : $agendaItem->description_en; ?>

                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="<?php echo e(route('agenda')); ?>" class="btn btn-outline-success">
                                    <i class="fas fa-arrow-left me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Kembali ke Daftar Agenda' : 'Back to Agenda'); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                    <!-- Related Agenda -->
                    <div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Agenda Terkait' : 'Related Agenda'); ?></h5>
                        </div>
                        <div class="card-body">
                            <?php $__empty_1 = true; $__currentLoopData = $relatedAgendaItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="related-item mb-3 pb-3 <?php echo e(!$loop->last ? 'border-bottom' : ''); ?>">
                                    <div class="d-flex">
                                        <div class="agenda-date-small me-3">
                                            <div class="day"><?php echo e($related->date->format('d')); ?></div>
                                            <div class="month"><?php echo e($related->date->format('M')); ?></div>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                <a href="<?php echo e(route('agenda.show', $related->id)); ?>" class="text-decoration-none">
                                                    <?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($related->title, 50) : \Illuminate\Support\Str::limit($related->title_en, 50)); ?>

                                                </a>
                                            </h6>
                                            <div class="small text-muted">
                                                <i class="far fa-clock me-1"></i> <?php echo e($related->time->format('H:i')); ?>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada agenda terkait.' : 'No related agenda.'); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Latest Announcements -->
                    <?php echo $__env->make('public.partials.latest-announcements', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <!-- Latest News -->
                    <?php echo $__env->make('public.partials.latest-news', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <!-- CTA -->
                    <div class="card border-0 bg-success text-white shadow-sm" data-aos="fade-up">
                        <div class="card-body p-4 text-center">
                            <h5 class="mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></h5>
                            <p><?php echo e(app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family.'); ?></p>
                            <a href="<?php echo e(route('registration')); ?>" class="btn btn-light"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></a>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->startPush('scripts'); ?>
    <script src="<?php echo e(asset('js/sticky-sidebar.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/agenda/show.blade.php ENDPATH**/ ?>