<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Webhook;
use Illuminate\Support\Str;

class RegisterWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:register 
                            {--name= : The name of the webhook}
                            {--url= : The URL to send the webhook to}
                            {--event= : The event to trigger the webhook}
                            {--secret= : The secret key for the webhook (optional)}
                            {--active=1 : Whether the webhook is active (1 or 0)}
                            {--bulk : Register multiple webhooks from a JSON file}
                            {--file= : Path to JSON file containing webhook definitions (required with --bulk)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Register a new webhook or multiple webhooks from a JSON file';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->option('bulk')) {
            return $this->registerBulkWebhooks();
        } else {
            return $this->registerSingleWebhook();
        }
    }

    /**
     * Register a single webhook.
     *
     * @return int
     */
    protected function registerSingleWebhook()
    {
        // Validate required options
        if (!$this->option('name') || !$this->option('url') || !$this->option('event')) {
            $this->error('The name, url, and event options are required.');
            return Command::FAILURE;
        }

        // Create webhook data
        $webhookData = [
            'name' => $this->option('name'),
            'url' => $this->option('url'),
            'event' => $this->option('event'),
            'secret' => $this->option('secret') ?: Str::random(32),
            'is_active' => (bool) $this->option('active'),
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Source' => 'Nurul Hayah Webhook System'
            ],
            'payload_template' => [
                'source' => 'Nurul Hayah CMS',
                'version' => '1.0'
            ]
        ];

        try {
            // Check if webhook already exists
            $existingWebhook = Webhook::where('name', $webhookData['name'])
                ->where('event', $webhookData['event'])
                ->first();
            
            if ($existingWebhook) {
                $this->warn("A webhook with the name '{$webhookData['name']}' and event '{$webhookData['event']}' already exists.");
                
                if ($this->confirm('Do you want to update it?', false)) {
                    $existingWebhook->update($webhookData);
                    $apiKey = $existingWebhook->generateApiKey();
                    
                    $this->info("Webhook updated successfully.");
                    $this->info("API Key: {$apiKey}");
                    
                    return Command::SUCCESS;
                } else {
                    $this->info("Operation cancelled.");
                    return Command::SUCCESS;
                }
            }
            
            // Create the webhook
            $webhook = Webhook::create($webhookData);
            
            // Generate API key
            $apiKey = $webhook->generateApiKey();
            
            $this->info("Webhook registered successfully.");
            $this->info("API Key: {$apiKey}");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Failed to register webhook: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Register multiple webhooks from a JSON file.
     *
     * @return int
     */
    protected function registerBulkWebhooks()
    {
        // Validate file option
        if (!$this->option('file')) {
            $this->error('The file option is required when using --bulk.');
            return Command::FAILURE;
        }

        $filePath = $this->option('file');
        
        // Check if file exists
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return Command::FAILURE;
        }

        try {
            // Read and parse JSON file
            $jsonContent = file_get_contents($filePath);
            $webhooks = json_decode($jsonContent, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error("Invalid JSON format: " . json_last_error_msg());
                return Command::FAILURE;
            }
            
            if (!is_array($webhooks) || !isset($webhooks['webhooks']) || !is_array($webhooks['webhooks'])) {
                $this->error("Invalid JSON structure. Expected a 'webhooks' array.");
                return Command::FAILURE;
            }
            
            $results = [];
            $apiKeys = [];
            $count = 0;
            
            foreach ($webhooks['webhooks'] as $webhookData) {
                // Validate required fields
                if (!isset($webhookData['name']) || !isset($webhookData['url']) || !isset($webhookData['event'])) {
                    $this->warn("Skipping webhook: missing required fields (name, url, or event).");
                    continue;
                }
                
                // Set default values for optional fields
                $webhookData['secret'] = $webhookData['secret'] ?? Str::random(32);
                $webhookData['is_active'] = $webhookData['is_active'] ?? true;
                $webhookData['headers'] = $webhookData['headers'] ?? [
                    'Content-Type' => 'application/json',
                    'X-Source' => 'Nurul Hayah Webhook System'
                ];
                $webhookData['payload_template'] = $webhookData['payload_template'] ?? [
                    'source' => 'Nurul Hayah CMS',
                    'version' => '1.0'
                ];
                
                // Check if webhook already exists
                $existingWebhook = Webhook::where('name', $webhookData['name'])
                    ->where('event', $webhookData['event'])
                    ->first();
                
                if ($existingWebhook) {
                    $this->line("Updating existing webhook: {$webhookData['name']} ({$webhookData['event']})");
                    $existingWebhook->update($webhookData);
                    $apiKey = $existingWebhook->generateApiKey();
                    $results[] = $existingWebhook;
                    $apiKeys[$existingWebhook->id] = $apiKey;
                } else {
                    $this->line("Creating new webhook: {$webhookData['name']} ({$webhookData['event']})");
                    $webhook = Webhook::create($webhookData);
                    $apiKey = $webhook->generateApiKey();
                    $results[] = $webhook;
                    $apiKeys[$webhook->id] = $apiKey;
                }
                
                $count++;
            }
            
            $this->info("{$count} webhooks processed successfully.");
            
            // Display API keys
            $this->newLine();
            $this->info("API Keys:");
            $this->newLine();
            
            $this->table(
                ['ID', 'Name', 'Event', 'API Key'],
                collect($results)->map(function ($webhook) use ($apiKeys) {
                    return [
                        'id' => $webhook->id,
                        'name' => $webhook->name,
                        'event' => $webhook->event,
                        'api_key' => $apiKeys[$webhook->id] ?? 'N/A'
                    ];
                })->toArray()
            );
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Failed to register webhooks: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
