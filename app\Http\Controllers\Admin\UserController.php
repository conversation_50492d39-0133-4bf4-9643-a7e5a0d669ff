<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Services\EmailService;
use App\Notifications\NewUserAccountNotification;

class UserController extends Controller
{
    /**
     * Display a listing of the users.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $users = User::all();
        return view('admin.settings.users.index', compact('users'));
    }

    /**
     * Store a newly created user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'role' => 'nullable|string',
        ]);

        try {
            // Generate a random password
            $randomPassword = Str::random(10);

            \Log::info('Creating new user', [
                'name' => $request->name,
                'username' => $request->username,
                'email' => $request->email,
                'role' => $request->role ?? 'admin'
            ]);

            // Create the user with the random password
            $user = User::create([
                'name' => $request->name,
                'username' => $request->username,
                'email' => $request->email,
                'password' => Hash::make($randomPassword),
                'role' => $request->role ?? 'admin',
            ]);

            \Log::info('User created successfully', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            try {
                // Send notification with the password
                \Log::info('Sending notification to user', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);

                // Send email directly instead of using notification
                $emailService = new \App\Services\EmailService();
                $emailSent = $emailService->sendEmailWithView(
                    $user->email,
                    __('emails.new_user_subject'),
                    'emails.new-user-account',
                    [
                        'username' => $user->username,
                        'email' => $user->email,
                        'password' => $randomPassword
                    ],
                    'notifications'
                );

                if ($emailSent) {
                    \Log::info('Email sent successfully', [
                        'user_id' => $user->id,
                        'email' => $user->email
                    ]);
                } else {
                    throw new \Exception('Email service returned false');
                }
            } catch (\Exception $emailError) {
                \Log::error('Failed to send notification', [
                    'user_id' => $user->id,
                    'error' => $emailError->getMessage(),
                    'trace' => $emailError->getTraceAsString()
                ]);

                // Continue execution even if email fails
                return redirect()->route('admin.settings.users.index')
                    ->with('warning', 'User created successfully but failed to send email notification. Password: ' . $randomPassword);
            }

            return redirect()->route('admin.settings.users.index')
                ->with('success', 'User created successfully and login details sent to their email.');
        } catch (\Exception $e) {
            \Log::error('Failed to create user', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.settings.users.index')
                ->with('error', 'Failed to create user: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Debugging
        \Log::info('Update user request', [
            'id' => $id,
            'all_data' => $request->all()
        ]);

        $user = User::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $id,
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'nullable|string',
        ]);

        $data = [
            'name' => $request->name,
            'username' => $request->username,
            'email' => $request->email,
            'role' => $request->role ?? 'admin',
            'telegram_id' => $request->telegram_id,
        ];

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        try {
            $user->update($data);

            // Log untuk debugging
            \Log::info('User updated successfully', [
                'user_id' => $user->id,
                'data' => $data
            ]);

            return redirect()->route('admin.settings.users.index')
                ->with('success', 'User updated successfully.');
        } catch (\Exception $e) {
            // Log error
            \Log::error('Failed to update user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return redirect()->route('admin.settings.users.index')
                ->with('error', 'Failed to update user: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified user from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);

        // Prevent deleting the currently logged in user
        if (auth()->id() == $user->id) {
            return redirect()->route('admin.settings.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        // Prevent deleting the last admin user
        $adminCount = User::where('role', 'admin')->count();
        if ($user->role === 'admin' && $adminCount <= 1) {
            return redirect()->route('admin.settings.users.index')
                ->with('error', 'Cannot delete the last admin user.');
        }

        $user->delete();

        return redirect()->route('admin.settings.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Reset the password for a specific user and send email notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changePassword(Request $request, $id)
    {
        $user = User::findOrFail($id);

        try {
            // Generate a random password
            $randomPassword = Str::random(10);

            \Log::info('Resetting password for user', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            // Update user with new password
            $user->update([
                'password' => Hash::make($randomPassword),
            ]);

            \Log::info('Password reset successfully', [
                'user_id' => $user->id
            ]);

            try {
                // Send email with the new password
                \Log::info('Sending password reset notification to user', [
                    'user_id' => $user->id,
                    'email' => $user->email
                ]);

                // Send email directly using EmailService
                $emailService = new EmailService();
                $emailSent = $emailService->sendEmailWithView(
                    $user->email,
                    __('Password Reset Notification'),
                    'emails.password-reset',
                    [
                        'username' => $user->username,
                        'name' => $user->name,
                        'email' => $user->email,
                        'password' => $randomPassword
                    ],
                    'notifications'
                );

                if ($emailSent) {
                    \Log::info('Password reset email sent successfully', [
                        'user_id' => $user->id,
                        'email' => $user->email
                    ]);

                    return redirect()->route('admin.settings.users.index')
                        ->with('success', 'Password reset successfully and sent to user\'s email.');
                } else {
                    throw new \Exception('Email service returned false');
                }
            } catch (\Exception $emailError) {
                \Log::error('Failed to send password reset notification', [
                    'user_id' => $user->id,
                    'error' => $emailError->getMessage(),
                    'trace' => $emailError->getTraceAsString()
                ]);

                // Password was reset but email failed
                return redirect()->route('admin.settings.users.index')
                    ->with('warning', 'Password reset successfully but failed to send email notification.');
            }
        } catch (\Exception $e) {
            // Log error
            \Log::error('Failed to reset user password', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('admin.settings.users.index')
                ->with('error', 'Failed to reset password: ' . $e->getMessage());
        }
    }
}
