<?php $__env->startSection('title', 'View Program'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Program</h1>
        <div>
            <a href="<?php echo e(route('admin.programs.edit', $program)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.programs.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Program Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID:</div>
                        <div class="col-md-9"><?php echo e($program->id); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Featured:</div>
                        <div class="col-md-9">
                            <?php if($program->is_featured): ?>
                                <span class="badge bg-success">Yes</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">No</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Icon:</div>
                        <div class="col-md-9">
                            <?php if($program->icon): ?>
                                <i class="<?php echo e($program->icon); ?> fa-2x me-2"></i> <?php echo e($program->icon); ?>

                            <?php else: ?>
                                <span class="text-muted">No icon available</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At:</div>
                        <div class="col-md-9"><?php echo e($program->created_at->format('d M Y H:i:s')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Updated At:</div>
                        <div class="col-md-9"><?php echo e($program->updated_at->format('d M Y H:i:s')); ?></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Indonesian Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Name:</div>
                                <div class="col-md-9"><?php echo e($program->name); ?></div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Description:</div>
                                <div class="col-md-9 formatted-content"><?php echo $program->description; ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">English Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Name:</div>
                                <div class="col-md-9"><?php echo e($program->name_en); ?></div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 fw-bold">Description:</div>
                                <div class="col-md-9 formatted-content"><?php echo $program->description_en; ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body">
                    <?php if($program->image): ?>
                        <img src="<?php echo e(asset('storage/' . $program->image)); ?>" alt="<?php echo e($program->name); ?>" class="img-fluid rounded">
                    <?php else: ?>
                        <div class="text-center py-5 bg-light rounded">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="mt-3 text-muted">No image available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="<?php echo e(route('programs')); ?>" class="btn btn-info w-100 mb-2" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i> View on Website
                    </a>

                    <a href="<?php echo e(route('admin.programs.edit', $program)); ?>" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Program
                    </a>

                    <form action="<?php echo e(route('admin.programs.destroy', $program)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this program?')">
                            <i class="fas fa-trash me-1"></i> Delete Program
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Formatted content styling */
    .formatted-content {
        line-height: 1.6;
    }

    .formatted-content img {
        max-width: 100%;
        height: auto;
        margin: 0.5rem 0;
    }

    .formatted-content ul,
    .formatted-content ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    .formatted-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .formatted-content table td,
    .formatted-content table th {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .formatted-content blockquote {
        padding: 0.5rem 1rem;
        margin: 1rem 0;
        border-left: 4px solid #6c757d;
        background-color: #f8f9fa;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/programs/show.blade.php ENDPATH**/ ?>