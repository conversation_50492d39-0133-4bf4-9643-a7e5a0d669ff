<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use App\Mail\NewUserAccountMail;

class NewUserAccountNotification extends Notification
{
    use Queueable;

    /**
     * The password for the new user.
     *
     * @var string
     */
    protected $password;

    /**
     * Create a new notification instance.
     *
     * @param string $password
     * @return void
     */
    public function __construct($password)
    {
        $this->password = $password;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Use the notifications mailer configuration
        \Illuminate\Support\Facades\Config::set('mail.default', 'notifications');

        // Get the locale from the user or use the current app locale
        $locale = $notifiable->locale ?? app()->getLocale();

        // Set the locale for this email
        app()->setLocale($locale);

        // Return the mailable
        return new NewUserAccountMail(
            $notifiable->username,
            $notifiable->email,
            $this->password
        );
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
