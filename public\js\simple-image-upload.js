/**
 * Simple Image Upload for Summernote
 * A direct, no-frills approach to image uploads
 */

$(document).ready(function() {
    console.log('Simple Image Upload loaded');

    // Disable the default Summernote image upload
    // Instead, we'll use a direct approach

    // Skip description fields in swiper pages
    let selector = '.summernote, #content, #content_en, #bio, #bio_en';

    // Only include description fields if we're not on a swiper page
    if (!window.location.href.includes('/admin/swipers/')) {
        selector += ', #description, #description_en';
    }

    $(selector).each(function() {
        // Skip elements with data-no-summernote attribute
        if ($(this).attr('data-no-summernote') === 'true') return;

        // Get the editor instance
        var $editor = $(this);

        // Find the image button
        var $imageBtn = $editor.next('.note-editor').find('.note-btn[data-original-title="Picture"]');

        // Remove the default click handler
        $imageBtn.off('click');

        // Add our custom click handler
        $imageBtn.on('click', function(e) {
            e.preventDefault();

            // Create a file input element
            var $fileInput = $('<input type="file" accept="image/*" style="display:none">');
            $('body').append($fileInput);

            // When a file is selected
            $fileInput.on('change', function() {
                if (this.files && this.files[0]) {
                    var file = this.files[0];

                    // Check file size (max 10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        alert('Image size exceeds 10MB limit. Please choose a smaller image.');
                        $fileInput.remove();
                        return;
                    }

                    // Check file type
                    if (!file.type.match('image.*')) {
                        alert('Please select an image file.');
                        $fileInput.remove();
                        return;
                    }

                    // Create a FormData object
                    var formData = new FormData();
                    formData.append('image', file);
                    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                    // Show loading message
                    var $loading = $('<div class="simple-loading">Uploading image...</div>');
                    $('body').append($loading);

                    // Upload the image
                    $.ajax({
                        url: '/admin/upload-summernote-image',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            // Remove loading message
                            $loading.remove();

                            if (response.success) {
                                // Get the current content
                                var content = $editor.summernote('code');

                                // Create image HTML
                                var img = '<img src="' + response.url + '" alt="' + response.filename + '" style="max-width:100%;">';

                                // Insert the image
                                $editor.summernote('code', content + img);

                                console.log('Image inserted successfully');
                            } else {
                                alert('Failed to upload image: ' + response.message);
                            }

                            // Remove the file input
                            $fileInput.remove();
                        },
                        error: function(xhr) {
                            // Remove loading message
                            $loading.remove();

                            // Show error message
                            var errorMsg = 'Failed to upload image';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMsg = xhr.responseJSON.message;
                            }
                            alert(errorMsg);

                            // Remove the file input
                            $fileInput.remove();
                        }
                    });
                }
            });

            // Trigger the file input
            $fileInput.click();
        });
    });

    // Add CSS for loading message
    $('<style>')
        .text('.simple-loading { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 9999; }')
        .appendTo('head');

    // Re-initialize when new editors are added
    var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                // Skip description fields in swiper pages
                let selector = '.summernote, #content, #content_en, #bio, #bio_en';

                // Only include description fields if we're not on a swiper page
                if (!window.location.href.includes('/admin/swipers/')) {
                    selector += ', #description, #description_en';
                }

                // Check if any of the added nodes are Summernote editors
                $(mutation.addedNodes).find(selector).each(function() {
                    // Skip elements with data-no-summernote attribute
                    if ($(this).attr('data-no-summernote') === 'true') return;

                    // Wait for Summernote to initialize
                    setTimeout(function() {
                        // Find the image button
                        var $imageBtn = $(this).next('.note-editor').find('.note-btn[data-original-title="Picture"]');

                        // Remove the default click handler
                        $imageBtn.off('click');

                        // Add our custom click handler
                        $imageBtn.on('click', function(e) {
                            e.preventDefault();

                            // Create a file input element
                            var $fileInput = $('<input type="file" accept="image/*" style="display:none">');
                            $('body').append($fileInput);

                            // When a file is selected
                            $fileInput.on('change', function() {
                                if (this.files && this.files[0]) {
                                    var file = this.files[0];

                                    // Check file size (max 10MB)
                                    if (file.size > 10 * 1024 * 1024) {
                                        alert('Image size exceeds 10MB limit. Please choose a smaller image.');
                                        $fileInput.remove();
                                        return;
                                    }

                                    // Check file type
                                    if (!file.type.match('image.*')) {
                                        alert('Please select an image file.');
                                        $fileInput.remove();
                                        return;
                                    }

                                    // Create a FormData object
                                    var formData = new FormData();
                                    formData.append('image', file);
                                    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                                    // Show loading message
                                    var $loading = $('<div class="simple-loading">Uploading image...</div>');
                                    $('body').append($loading);

                                    // Upload the image
                                    $.ajax({
                                        url: '/admin/upload-summernote-image',
                                        type: 'POST',
                                        data: formData,
                                        processData: false,
                                        contentType: false,
                                        success: function(response) {
                                            // Remove loading message
                                            $loading.remove();

                                            if (response.success) {
                                                // Get the current content
                                                var content = $editor.summernote('code');

                                                // Create image HTML
                                                var img = '<img src="' + response.url + '" alt="' + response.filename + '" style="max-width:100%;">';

                                                // Insert the image
                                                $editor.summernote('code', content + img);

                                                console.log('Image inserted successfully');
                                            } else {
                                                alert('Failed to upload image: ' + response.message);
                                            }

                                            // Remove the file input
                                            $fileInput.remove();
                                        },
                                        error: function(xhr) {
                                            // Remove loading message
                                            $loading.remove();

                                            // Show error message
                                            var errorMsg = 'Failed to upload image';
                                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                                errorMsg = xhr.responseJSON.message;
                                            }
                                            alert(errorMsg);

                                            // Remove the file input
                                            $fileInput.remove();
                                        }
                                    });
                                }
                            });

                            // Trigger the file input
                            $fileInput.click();
                        });
                    }.bind(this), 500);
                });
            }
        });
    });

    // Observe the entire document
    observer.observe(document.body, { childList: true, subtree: true });
});
