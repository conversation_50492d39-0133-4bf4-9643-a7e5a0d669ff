@extends('admin.layouts.app')

@section('title', 'Registration Management')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Registration Management</h1>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'management' ? 'active' : '' }}" href="{{ route('admin.registrations.index', ['tab' => 'management']) }}">
                        <i class="fas fa-cogs me-1"></i> Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ $activeTab == 'registration' ? 'active' : '' }}" href="{{ route('admin.registrations.index', ['tab' => 'registration']) }}">
                        <i class="fas fa-list me-1"></i> Registration
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <h5 class="mb-3">Filter Registrations</h5>
            <form action="{{ route('admin.registrations.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="education_unit_id" class="form-label">Education Unit</label>
                    <select class="form-select" id="education_unit_id" name="education_unit_id">
                        <option value="">All Education Units</option>
                        @foreach($educationUnits as $unit)
                            <option value="{{ $unit->id }}" {{ request('education_unit_id') == $unit->id ? 'selected' : '' }}>{{ $unit->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="Name, Email, Phone...">
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="{{ route('admin.registrations.index') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="d-flex justify-content-end mb-3">
                <a href="{{ route('admin.registrations.export', array_merge(request()->query(), ['tab' => 'registration'])) }}" class="btn btn-success">
                    <i class="fas fa-file-excel me-1"></i> Ekspor ke Excel
                </a>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Registration #</th>
                            <th>Full Name</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($registrations as $registration)
                            <tr>
                                <td>{{ $registration->registration_number }}</td>
                                <td>{{ $registration->full_name }}</td>
                                <td>{{ $registration->phone }}</td>
                                <td>
                                    @if($registration->status == 'pending')
                                        <span class="badge bg-warning">Pending</span>
                                    @elseif($registration->status == 'approved')
                                        <span class="badge bg-success">Approved</span>
                                    @elseif($registration->status == 'rejected')
                                        <span class="badge bg-danger">Rejected</span>
                                    @endif
                                </td>
                                <td>{{ $registration->created_at ? $registration->created_at->format('d M Y') : 'N/A' }}</td>
                                <td>
                                    <a href="{{ route('admin.registrations.show', $registration) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <form action="{{ route('admin.registrations.destroy', $registration) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this registration?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">No registrations found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $registrations->links() }}
            </div>
        </div>
    </div>
@endsection
