@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $category->name : $category->name_en)

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? $category->name : $category->name_en }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('news') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Berita' : 'News' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? $category->name : $category->name_en }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- News List -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                @forelse($news as $item)
                    <div class="col-lg-4 col-md-6 mb-4 fade-in">
                        <div class="news-card card h-100">
                            @if($item->thumbnail)
                                <img src="{{ asset('storage/' . $item->thumbnail) }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}">
                            @else
                                <img src="{{ asset('images/news-placeholder.jpg') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}">
                            @endif
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="news-date">
                                        <i class="far fa-calendar-alt me-1"></i> {{ $item->published_at->format('d M Y') }}
                                    </div>
                                    <div class="news-category">
                                        <span class="badge bg-success">
                                            {{ app()->getLocale() == 'id' ? $category->name : $category->name_en }}
                                        </span>
                                    </div>
                                </div>
                                <h5 class="card-title">{{ app()->getLocale() == 'id' ? $item->title : $item->title_en }}</h5>
                                <p class="card-text">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($item->content), 150) : \Illuminate\Support\Str::limit(strip_tags($item->content_en), 150) }}</p>
                                <a href="{{ route('news.show', app()->getLocale() == 'id' ? $item->slug : $item->slug_en) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}</a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada berita dalam kategori ini.' : 'No news available in this category yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $news->links() }}
            </div>
        </div>
    </section>
@endsection
