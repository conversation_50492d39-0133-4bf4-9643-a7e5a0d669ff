<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only add the thumbnail column since is_published is already added in the main migration
        Schema::table('news', function (Blueprint $table) {
            if (!Schema::hasColumn('news', 'thumbnail')) {
                $table->string('thumbnail')->nullable()->after('image');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('news', function (Blueprint $table) {
            if (Schema::hasColumn('news', 'thumbnail')) {
                $table->dropColumn('thumbnail');
            }
        });
    }
};
