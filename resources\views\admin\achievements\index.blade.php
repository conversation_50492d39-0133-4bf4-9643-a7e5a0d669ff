@extends('admin.layouts.app')

@section('title', 'Achievements')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Achievements</h1>
        <a href="{{ route('admin.achievements.create') }}" class="btn btn-success">
            <i class="fas fa-plus me-1"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            @if($achievements->isEmpty())
                <div class="text-center py-5">
                    <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                    <p class="mb-0">No achievements found. Click the "Add New" button to create one.</p>
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="80">Image</th>
                                <th>Title</th>
                                <th>Award By</th>
                                <th>Date</th>
                                <th width="100">Featured</th>
                                <th width="120">Order</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($achievements as $achievement)
                                <tr>
                                    <td>
                                        @if($achievement->image)
                                            <img src="{{ asset('storage/' . $achievement->image) }}" alt="{{ $achievement->title }}" class="img-thumbnail" width="60">
                                        @else
                                            <div class="bg-light text-center p-2 rounded">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ $achievement->title }}</div>
                                        <small class="text-muted">{{ $achievement->title_en }}</small>
                                    </td>
                                    <td>{{ $achievement->award_by }}</td>
                                    <td>{{ $achievement->achievement_date ? $achievement->achievement_date->format('d M Y') : '-' }}</td>
                                    <td>
                                        @if($achievement->is_featured)
                                            <span class="badge bg-success">Featured</span>
                                        @else
                                            <span class="badge bg-secondary">No</span>
                                        @endif
                                    </td>
                                    <td>{{ $achievement->order }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('admin.achievements.edit', $achievement) }}" class="btn btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.achievements.show', $achievement) }}" class="btn btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $achievement->id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ $achievement->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $achievement->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel{{ $achievement->id }}">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Are you sure you want to delete the achievement "{{ $achievement->title }}"?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="{{ route('admin.achievements.destroy', $achievement) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>
@endsection
