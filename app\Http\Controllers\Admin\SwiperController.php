<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Swiper;
use App\Services\ImageService;
use Illuminate\Http\Request;

class SwiperController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $swipers = Swiper::orderBy('order', 'asc')->get();
        $canAddMore = $swipers->count() < 5;
        return view('admin.swipers.index', compact('swipers', 'canAddMore'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Check if we already have 5 swiper images
        $count = Swiper::count();
        if ($count >= 5) {
            return redirect()->route('admin.swipers.index')
                ->with('error', 'Maximum number of swiper images (5) has been reached.');
        }

        return view('admin.swipers.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Check if we already have 5 swiper images
        $count = Swiper::count();
        if ($count >= 5) {
            return redirect()->route('admin.swipers.index')
                ->with('error', 'Maximum number of swiper images (5) has been reached.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp',
            'button_text' => 'nullable|string|max:255',
            'button_text_en' => 'nullable|string|max:255',
            'button_url' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
        ]);

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Swiper::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'swipers',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        Swiper::create($data);

        return redirect()->route('admin.swipers.index')
            ->with('success', 'Swiper image created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $swiper = Swiper::findOrFail($id);
        return view('admin.swipers.show', compact('swiper'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $swiper = Swiper::findOrFail($id);
        return view('admin.swipers.edit', compact('swiper'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp',
            'button_text' => 'nullable|string|max:255',
            'button_text_en' => 'nullable|string|max:255',
            'button_url' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
        ]);

        $swiper = Swiper::findOrFail($id);
        $data = $request->all();

        // Always set is_active to true
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($swiper->image) {
                $this->imageService->deleteImage($swiper->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'swipers',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $swiper->update($data);

        return redirect()->route('admin.swipers.index')
            ->with('success', 'Swiper image updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $swiper = Swiper::findOrFail($id);

        // Delete image file
        if ($swiper->image) {
            $this->imageService->deleteImage($swiper->image);
        }

        $swiper->delete();

        return redirect()->route('admin.swipers.index')
            ->with('success', 'Swiper image deleted successfully.');
    }
}

