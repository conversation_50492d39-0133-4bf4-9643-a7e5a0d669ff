<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create artwork_categories table if it doesn't exist
        if (!Schema::hasTable('artwork_categories')) {
            Schema::create('artwork_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en');
                $table->string('slug')->unique();
                $table->string('slug_en')->unique();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create paints table if it doesn't exist
        if (!Schema::hasTable('paints')) {
            Schema::create('paints', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en');
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('image');
                $table->string('artist')->nullable();
                $table->string('artist_en')->nullable();
                $table->integer('year')->nullable();
                $table->string('medium')->nullable();
                $table->string('medium_en')->nullable();
                $table->string('dimensions')->nullable();
                $table->foreignId('category_id')->nullable()->constrained('artwork_categories')->onDelete('set null');
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create literature table if it doesn't exist
        if (!Schema::hasTable('literature')) {
            Schema::create('literature', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en');
                $table->text('content');
                $table->text('content_en');
                $table->string('author')->nullable();
                $table->string('author_en')->nullable();
                $table->integer('year')->nullable();
                $table->string('type')->nullable();
                $table->string('type_en')->nullable();
                $table->string('image')->nullable();
                $table->string('file')->nullable();
                $table->foreignId('category_id')->nullable()->constrained('artwork_categories')->onDelete('set null');
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop tables in reverse order to avoid foreign key constraints
        Schema::dropIfExists('literature');
        Schema::dropIfExists('paints');
        Schema::dropIfExists('artwork_categories');
    }
};
