<?php $__env->startSection('title', 'Academic Calendar Details'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Academic Calendar Details</h1>
        <div>
            <a href="<?php echo e(route('admin.calendars.edit', $calendar)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.calendars.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Calendar Information</h5>
                    <span class="badge <?php echo e($calendar->is_active ? 'bg-success' : 'bg-secondary'); ?>">
                        <?php echo e($calendar->is_active ? 'Active' : 'Inactive'); ?>

                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID</div>
                        <div class="col-md-9"><?php echo e($calendar->id); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title (Indonesian)</div>
                        <div class="col-md-9"><?php echo e($calendar->title); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title (English)</div>
                        <div class="col-md-9"><?php echo e($calendar->title_en); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Description (Indonesian)</div>
                        <div class="col-md-9"><?php echo e($calendar->description ?? 'N/A'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Description (English)</div>
                        <div class="col-md-9"><?php echo e($calendar->description_en ?? 'N/A'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Start Date</div>
                        <div class="col-md-9"><?php echo e($calendar->start_date->format('d F Y')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">End Date</div>
                        <div class="col-md-9"><?php echo e($calendar->end_date->format('d F Y')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Duration</div>
                        <div class="col-md-9">
                            <?php
                                $duration = $calendar->start_date->diffInDays($calendar->end_date) + 1;
                            ?>
                            <?php echo e($duration); ?> <?php echo e($duration > 1 ? 'days' : 'day'); ?>

                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Location (Indonesian)</div>
                        <div class="col-md-9"><?php echo e($calendar->location ?? 'N/A'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Location (English)</div>
                        <div class="col-md-9"><?php echo e($calendar->location_en ?? 'N/A'); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Type</div>
                        <div class="col-md-9">
                            <?php if($calendar->type): ?>
                                <span class="badge bg-info"><?php echo e(ucfirst($calendar->type)); ?></span>
                            <?php else: ?>
                                <span class="text-muted">N/A</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Color</div>
                        <div class="col-md-9">
                            <?php if($calendar->color): ?>
                                <div class="d-flex align-items-center">
                                    <div style="width: 20px; height: 20px; background-color: <?php echo e($calendar->color); ?>; border-radius: 4px; margin-right: 10px;"></div>
                                    <?php echo e($calendar->color); ?>

                                </div>
                            <?php else: ?>
                                <span class="text-muted">Default</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Display Order</div>
                        <div class="col-md-9"><?php echo e($calendar->order); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At</div>
                        <div class="col-md-9"><?php echo e($calendar->created_at->format('d F Y, H:i:s')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Last Updated</div>
                        <div class="col-md-9"><?php echo e($calendar->updated_at->format('d F Y, H:i:s')); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/calendars/show.blade.php ENDPATH**/ ?>