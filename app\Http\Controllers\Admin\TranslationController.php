<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TranslationController extends Controller
{
    /**
     * Translate text from one language to another using MyMemory API.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function translate(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'text' => 'required|string',
            'source_lang' => 'required|string|size:2',
            'target_lang' => 'required|string|size:2',
        ]);

        try {
            // Get the text to translate
            $text = $validated['text'];
            $sourceLang = $validated['source_lang'];
            $targetLang = $validated['target_lang'];
            
            // Get the email for MyMemory API
            $email = env('MYMEMORY_TRANSLATE_EMAIL', '<EMAIL>');
            
            // Call the MyMemory API
            $response = Http::get('https://api.mymemory.translated.net/get', [
                'q' => $text,
                'langpair' => "{$sourceLang}|{$targetLang}",
                'de' => $email,
            ]);
            
            // Check if the request was successful
            if ($response->successful()) {
                $data = $response->json();
                
                // Check if we have a translation
                if (isset($data['responseData']['translatedText'])) {
                    return response()->json([
                        'success' => true,
                        'translated_text' => $data['responseData']['translatedText'],
                    ]);
                }
            }
            
            // If we get here, something went wrong
            Log::warning('Translation failed', [
                'response' => $response->json(),
                'text' => $text,
                'source_lang' => $sourceLang,
                'target_lang' => $targetLang,
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Translation failed',
            ], 500);
        } catch (\Exception $e) {
            Log::error('Translation error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred during translation',
            ], 500);
        }
    }
}
