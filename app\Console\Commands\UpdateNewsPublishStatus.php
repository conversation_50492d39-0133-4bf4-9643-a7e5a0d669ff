<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\News;

class UpdateNewsPublishStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'news:publish';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all news to be published';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $news = News::all();
        $count = 0;

        foreach ($news as $article) {
            $article->is_published = true;
            $article->save();
            $count++;
        }

        $this->info("Updated $count news articles to be published.");

        return Command::SUCCESS;
    }
}
