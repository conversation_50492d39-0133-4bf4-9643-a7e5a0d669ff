// Custom JavaScript for Nurul Hayah 4 Website

document.addEventListener('DOMContentLoaded', function() {
    // Scroll animation function
    function checkScroll() {
        const scrollElements = document.querySelectorAll('.scroll-section, .fade-in, .fade-in-left, .fade-in-right, .zoom-in');

        scrollElements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            // If element is in viewport
            if (elementPosition < windowHeight * 0.85) {
                if (element.classList.contains('directors-insight-section')) {
                    element.classList.add('active');
                } else {
                    element.classList.add('active');
                }
            }
        });
    }

    // Run on page load
    checkScroll();

    // Run on scroll
    window.addEventListener('scroll', checkScroll);

    // News category tabs animation
    const newsCategoryTabs = document.getElementById('newsCategoryTabs');
    if (newsCategoryTabs) {
        const tabLinks = newsCategoryTabs.querySelectorAll('.nav-link');

        tabLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Get the target tab content
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                // Add a slight delay to allow the Bootstrap tab transition to complete
                setTimeout(() => {
                    // Trigger animations for elements inside the active tab
                    const animElements = targetPane.querySelectorAll('.fade-in, .fade-in-left, .fade-in-right, .zoom-in');
                    animElements.forEach(element => {
                        // Reset animation by removing and re-adding the class
                        element.classList.remove('active');

                        // Force a reflow to restart the animation
                        void element.offsetWidth;

                        // Add the active class to start the animation
                        element.classList.add('active');
                    });
                }, 150);
            });
        });
    }

    // Hero Slider is initialized in home.blade.php to avoid duplicate initialization

    // Initialize Swiper for Testimonial Slider
    if (document.querySelector('.testimonial-slider')) {
        new Swiper('.testimonial-slider', {
            slidesPerView: 1,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                },
                992: {
                    slidesPerView: 3,
                },
            },
        });
    }

    // Initialize Swiper for Gallery Slider
    if (document.querySelector('.gallery-slider')) {
        new Swiper('.gallery-slider', {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                576: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                992: {
                    slidesPerView: 4,
                },
            },
        });
    }

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            if (this.getAttribute('href') !== '#') {
                e.preventDefault();

                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    window.scrollTo({
                        top: target.offsetTop - 100,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });

    // Back to top button
    const backToTopButton = document.querySelector('.back-to-top');
    if (backToTopButton) {
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('show');
            } else {
                backToTopButton.classList.remove('show');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Gallery lightbox - only for pages that don't have specific gallery handlers
    // Skip this for home page and gallery page which have their own handlers
    if (!document.querySelector('.hero-slider') && !document.querySelector('.gallery-filter')) {
        const galleryItems = document.querySelectorAll('.gallery-item');
        if (galleryItems.length > 0) {
            galleryItems.forEach(item => {
                // Check if the item doesn't have a gallery-link
                if (!item.querySelector('.gallery-link')) {
                    item.addEventListener('click', function() {
                        const imgSrc = this.querySelector('img').getAttribute('src');
                        const lightbox = document.createElement('div');
                        lightbox.classList.add('lightbox');

                        lightbox.innerHTML = `
                            <div class="lightbox-content">
                                <img src="${imgSrc}" alt="Gallery Image">
                                <span class="lightbox-close">&times;</span>
                            </div>
                        `;
                        document.body.appendChild(lightbox);

                        // Prevent scrolling when lightbox is open
                        document.body.style.overflow = 'hidden';

                        // Close lightbox when clicking on the close button or outside the image
                        lightbox.addEventListener('click', function(e) {
                            if (e.target === this || e.target.classList.contains('lightbox-close')) {
                                document.body.removeChild(lightbox);
                                document.body.style.overflow = 'auto';
                            }
                        });
                    });
                }
            });
        }
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    if (forms.length > 0) {
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            }, false);
        });
    }
});
