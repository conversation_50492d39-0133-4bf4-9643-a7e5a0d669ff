<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ArtworkCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ArtworkCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = ArtworkCategory::orderBy('name')->paginate(10);
        return view('admin.artwork.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.artwork.categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:artwork_categories',
            'slug_en' => 'nullable|string|max:255|unique:artwork_categories',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
        ]);

        $data = $request->all();
        
        // Generate slugs if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }
        
        if (empty($data['slug_en'])) {
            $data['slug_en'] = Str::slug($data['name_en']);
        }
        
        // Set is_active to true by default
        $data['is_active'] = true;

        ArtworkCategory::create($data);

        return redirect()->route('admin.artwork.categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $category = ArtworkCategory::findOrFail($id);
        return view('admin.artwork.categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $category = ArtworkCategory::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:artwork_categories,slug,' . $id,
            'slug_en' => 'nullable|string|max:255|unique:artwork_categories,slug_en,' . $id,
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        
        // Generate slugs if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }
        
        if (empty($data['slug_en'])) {
            $data['slug_en'] = Str::slug($data['name_en']);
        }
        
        // Set is_active based on checkbox
        $data['is_active'] = $request->has('is_active');

        $category->update($data);

        return redirect()->route('admin.artwork.categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $category = ArtworkCategory::findOrFail($id);
        $category->delete();

        return redirect()->route('admin.artwork.categories.index')
            ->with('success', 'Category deleted successfully.');
    }
}
