<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Leader extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_en',
        'position',
        'position_en',
        'motto',
        'motto_en',
        'bio',
        'bio_en',
        'education_history',
        'education_history_en',
        'achievements',
        'achievements_en',
        'work_experience',
        'work_experience_en',
        'image',
        'email',
        'phone',
        'social_facebook',
        'social_twitter',
        'social_instagram',
        'social_linkedin',
        'order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
        'education_history' => 'json',
        'education_history_en' => 'json',
        'achievements' => 'json',
        'achievements_en' => 'json',
        'work_experience' => 'json',
        'work_experience_en' => 'json',
    ];

    /**
     * Scope a query to only include active leaders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order leaders by their order field.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc');
    }
}
