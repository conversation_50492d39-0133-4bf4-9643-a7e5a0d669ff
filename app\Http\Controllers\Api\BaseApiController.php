<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;
use App\Services\WebhookService;

class BaseApiController extends Controller
{
    /**
     * The model instance.
     *
     * @var \Illuminate\Database\Eloquent\Model
     */
    protected $model;

    /**
     * The webhook service instance.
     *
     * @var \App\Services\WebhookService
     */
    protected $webhookService;

    /**
     * The model name (for webhook events).
     *
     * @var string
     */
    protected $modelName;

    /**
     * The validation rules for store.
     *
     * @var array
     */
    protected $storeRules = [];

    /**
     * The validation rules for update.
     *
     * @var array
     */
    protected $updateRules = [];

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = $this->model->query();

        // Apply filters if provided
        if ($request->has('filter')) {
            $filters = $request->filter;
            foreach ($filters as $field => $value) {
                $query->where($field, $value);
            }
        }

        // Apply sorting if provided
        if ($request->has('sort')) {
            $sort = $request->sort;
            $direction = $request->has('direction') ? $request->direction : 'asc';
            $query->orderBy($sort, $direction);
        }

        // Apply pagination if provided
        $perPage = $request->has('per_page') ? (int) $request->per_page : 15;
        $items = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $items,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->storeRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $item = $this->model->create($request->all());

        // Dispatch webhook event
        if ($this->modelName) {
            $this->webhookService->dispatchEvent("{$this->modelName}.created", $item->toArray());
        }

        return response()->json([
            'success' => true,
            'message' => ucfirst($this->modelName ?? 'Item') . ' created successfully',
            'data' => $item,
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $item = $this->model->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $item,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $item = $this->model->findOrFail($id);

        $validator = Validator::make($request->all(), $this->updateRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $item->update($request->all());

        // Dispatch webhook event
        if ($this->modelName) {
            $this->webhookService->dispatchEvent("{$this->modelName}.updated", $item->toArray());
        }

        return response()->json([
            'success' => true,
            'message' => ucfirst($this->modelName ?? 'Item') . ' updated successfully',
            'data' => $item,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $item = $this->model->findOrFail($id);
        $itemData = $item->toArray();
        
        $item->delete();

        // Dispatch webhook event
        if ($this->modelName) {
            $this->webhookService->dispatchEvent("{$this->modelName}.deleted", $itemData);
        }

        return response()->json([
            'success' => true,
            'message' => ucfirst($this->modelName ?? 'Item') . ' deleted successfully',
        ]);
    }
}
