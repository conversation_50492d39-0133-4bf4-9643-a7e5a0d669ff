<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class UpdateTelegramSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:update-settings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Telegram settings for group chat and topic ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating Telegram settings...');
        
        Setting::updateOrCreate(
            ['key' => 'telegram_chat_id'],
            ['value' => '-1001958560972', 'group' => 'telegram']
        );
        
        Setting::updateOrCreate(
            ['key' => 'telegram_group_topic_id'],
            ['value' => '4', 'group' => 'telegram']
        );
        
        $this->info('Telegram settings updated successfully!');
        
        // Display the updated settings
        $chatId = Setting::getValue('telegram_chat_id');
        $topicId = Setting::getValue('telegram_group_topic_id');
        
        $this->info('Chat ID: ' . $chatId);
        $this->info('Topic ID: ' . $topicId);
        
        return 0;
    }
}
