<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the telegram_subscribers table if it exists
        if (Schema::hasTable('telegram_subscribers')) {
            Schema::dropIfExists('telegram_subscribers');
        }

        // Remove Telegram-related settings if the settings table exists
        if (Schema::hasTable('settings')) {
            DB::table('settings')->where('group', 'telegram')->delete();
        }

        // Remove telegram_chat_id column from users table if it exists
        if (Schema::hasTable('users') && Schema::hasColumn('users', 'telegram_chat_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('telegram_chat_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible
    }
};
