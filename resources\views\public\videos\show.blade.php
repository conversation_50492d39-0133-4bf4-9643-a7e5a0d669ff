@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $video->title : $video->title_en)

@section('meta_description', app()->getLocale() == 'id' ? $video->description : $video->description_en)

@push('styles')
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
    <link rel="stylesheet" href="{{ asset('css/plyr-custom.css') }}" />
    <style>
        .video-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .video-details {
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }

        .video-title {
            font-weight: 700;
            margin-bottom: 15px;
            color: #333;
        }

        .video-description {
            color: #666;
            line-height: 1.7;
            margin-bottom: 20px;
        }

        .page-header {
            background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('{{ asset('images/header-bg.jpg') }}') center/cover no-repeat;
            padding: 60px 0;
            margin-bottom: 50px;
        }

        .page-title {
            color: white;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .breadcrumb-item, .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
        }

        .breadcrumb-item.active {
            color: white;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.6);
        }
    </style>
@endpush

@section('content')
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">
                        {{ app()->getLocale() == 'id' ? $video->title : $video->title_en }}
                    </h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('videos') }}">{{ app()->getLocale() == 'id' ? 'Video' : 'Videos' }}</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ app()->getLocale() == 'id' ? $video->title : $video->title_en }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Detail Section -->
    <section class="video-detail-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8" data-aos="fade-up">
                    <a href="{{ route('videos') }}" class="back-to-videos">
                        <i class="fas fa-arrow-left"></i> {{ app()->getLocale() == 'id' ? 'Kembali ke Video' : 'Back to Videos' }}
                    </a>

                    <div class="video-container">
                        <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="{{ $video->youtube_id }}"></div>
                    </div>

                    <div class="video-details">
                        <h2 class="video-title">{{ app()->getLocale() == 'id' ? $video->title : $video->title_en }}</h2>
                        <div class="video-description summernote-content">
                            {!! app()->getLocale() == 'id' ? $video->description : $video->description_en !!}
                        </div>
                    </div>
                </div>

                <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
                    @if($relatedVideos->count() > 0)
                        <h3 class="related-videos-title">{{ app()->getLocale() == 'id' ? 'Video Terkait' : 'Related Videos' }}</h3>
                        <div class="row g-4">
                            @foreach($relatedVideos as $relatedVideo)
                                <div class="col-md-6 col-lg-12">
                                    <a href="{{ route('videos.show', $relatedVideo->id) }}" class="text-decoration-none">
                                        <div class="related-video-card">
                                            <div class="related-video-thumbnail">
                                                @if($relatedVideo->thumbnail)
                                                    <img src="{{ asset('storage/' . $relatedVideo->thumbnail) }}" alt="{{ $relatedVideo->title }}" class="img-fluid">
                                                @else
                                                    <div class="ratio ratio-16x9">
                                                        <iframe src="{{ $relatedVideo->youtube_embed_url }}" title="{{ $relatedVideo->title }}" allowfullscreen></iframe>
                                                    </div>
                                                @endif
                                                <div class="play-icon">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            </div>
                                            <div class="related-video-info">
                                                <h5 class="related-video-title">{{ app()->getLocale() == 'id' ? $relatedVideo->title : $relatedVideo->title_en }}</h5>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
    <script src="{{ asset('js/plyr-custom.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
        });
    </script>
@endpush
