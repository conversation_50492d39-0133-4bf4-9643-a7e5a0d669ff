<?php

namespace App\Helpers;

use App\Models\MenuItem;

class MenuHelper
{
    /**
     * Get the main menu items.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMainMenu()
    {
        try {
            // Load root items with their children recursively (up to 3 levels)
            $items = MenuItem::whereNull('parent_id')
                ->where('is_active', true)
                ->orderBy('order', 'asc')
                ->get();

            // For debugging
            echo "<!-- Found menu items count: {$items->count()} -->";

            // Log menu items for debugging
            \Log::info('Menu items retrieved: ' . $items->count());
            foreach ($items as $index => $item) {
                \Log::info("Menu item {$index}: ID={$item->id}, Title={$item->title}");
            }

            // Eager load children for each item
            foreach ($items as $item) {
                $item->load(['children' => function ($query) {
                    $query->where('is_active', true)
                        ->orderBy('order', 'asc');
                }]);

                // Log children for debugging
                \Log::info("Menu item {$item->id} ({$item->title}) has " . count($item->children) . " children");

                foreach ($item->children as $child) {
                    $child->load(['children' => function ($query) {
                        $query->where('is_active', true)
                            ->orderBy('order', 'asc');
                    }]);

                    // Log grandchildren for debugging
                    \Log::info("  Child {$child->id} ({$child->title}) has " . count($child->children) . " children");

                    foreach ($child->children as $grandchild) {
                        $grandchild->load(['children' => function ($query) {
                            $query->where('is_active', true)
                                ->orderBy('order', 'asc');
                        }]);

                        // Log great-grandchildren for debugging
                        \Log::info("    Grandchild {$grandchild->id} ({$grandchild->title}) has " . count($grandchild->children) . " children");
                    }
                }
            }

            return $items;
        } catch (\Exception $e) {
            // For debugging
            echo "<!-- Error in getMainMenu: {$e->getMessage()} -->";
            return collect([]);
        }
    }

    /**
     * Render the main navigation menu.
     *
     * @return string
     */
    public static function renderMainMenu()
    {
        try {
            $menuItems = self::getMainMenu();

            if ($menuItems->isEmpty()) {
                echo "<!-- Menu items collection is empty, using default menu -->";
                return self::renderDefaultMenu();
            }

            $html = '<ul class="navbar-nav ms-auto">';

            foreach ($menuItems as $item) {
                $html .= self::renderMenuItem($item);
            }

            // Add registration button
            $html .= '<li class="nav-item">';
            $html .= '<a class="nav-link btn btn-success text-white ms-lg-3 px-4" href="' . route('registration') . '">';
            $html .= app()->getLocale() == 'id' ? 'Pendaftaran' : 'Registration';
            $html .= '</a>';
            $html .= '</li>';

            $html .= '</ul>';

            return $html;
        } catch (\Exception $e) {
            echo "<!-- Error in renderMainMenu: {$e->getMessage()} -->";
            return self::renderDefaultMenu();
        }
    }

    /**
     * Render a menu item.
     *
     * @param  \App\Models\MenuItem  $item
     * @param  int  $level
     * @return string
     */
    protected static function renderMenuItem($item, $level = 0)
    {
        try {
            // Log the item being rendered
            \Log::info("Rendering menu item: ID={$item->id}, Title={$item->title}, Level={$level}");

            $hasChildren = !empty($item->children) && count($item->children) > 0;
            $isActive = self::isActive($item);
            $title = app()->getLocale() == 'id' ? $item->title : ($item->title_en ?: $item->title);

            // Log children count
            \Log::info("  Item has " . ($hasChildren ? count($item->children) : 0) . " children");

            // For top level items
            if ($level === 0) {
                $html = '<li class="nav-item' . ($hasChildren ? ' dropdown' : '') . '">';

                if ($hasChildren) {
                    $html .= '<a class="nav-link dropdown-toggle' . ($isActive ? ' active' : '') . '" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">';
                    $html .= $title;
                    $html .= '</a>';
                    $html .= '<ul class="dropdown-menu">';

                    foreach ($item->children as $child) {
                        $html .= self::renderMenuItem($child, 1);
                    }

                    $html .= '</ul>';
                } else {
                    $html .= '<a class="nav-link' . ($isActive ? ' active' : '') . '" href="' . $item->url . '" target="' . $item->target . '">';
                    $html .= $title;
                    $html .= '</a>';
                }

                $html .= '</li>';
            }
            // For second level items (submenu)
            else if ($level === 1) {
                $hasGrandchildren = !empty($item->children) && count($item->children) > 0;

                if ($hasGrandchildren) {
                    // This is a submenu with children (sub-submenu)
                    $html = '<li class="dropdown-submenu">';
                    $html .= '<a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">';
                    $html .= $title;
                    $html .= '</a>';
                    $html .= '<ul class="dropdown-menu dropdown-submenu">';

                    foreach ($item->children as $grandchild) {
                        $html .= self::renderMenuItem($grandchild, 2);
                    }

                    $html .= '</ul>';
                } else {
                    // This is a regular submenu item
                    $html = '<li>';
                    $html .= '<a class="dropdown-item" href="' . $item->url . '" target="' . $item->target . '">';
                    $html .= $title;
                    $html .= '</a>';
                }

                $html .= '</li>';
            }
            // For third level items (sub-submenu)
            else if ($level === 2) {
                $html = '<li>';
                $html .= '<a class="dropdown-item" href="' . $item->url . '" target="' . $item->target . '">';
                $html .= $title;
                $html .= '</a>';
                $html .= '</li>';
            }

            return $html;
        } catch (\Exception $e) {
            echo "<!-- Error in renderMenuItem for item ID {$item->id}: {$e->getMessage()} -->";
            return '';
        }
    }

    /**
     * Check if a menu item is active.
     *
     * @param  \App\Models\MenuItem  $item
     * @return bool
     */
    protected static function isActive($item)
    {
        if ($item->route_name && request()->routeIs($item->route_name . '*')) {
            return true;
        }

        $currentPath = '/' . request()->path();
        if ($item->url != '#' && $item->url != '/' && $currentPath != '/' && strpos($currentPath, $item->url) === 0) {
            return true;
        }

        if ($item->url == '/' && $currentPath == '/') {
            return true;
        }

        // Check if any child is active
        if (!empty($item->children)) {
            foreach ($item->children as $child) {
                if (self::isActive($child)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Render the default menu when no custom menu is defined.
     *
     * @return string
     */
    protected static function renderDefaultMenu()
    {
        $html = '<ul class="navbar-nav ms-auto">';

        // Home
        $html .= '<li class="nav-item">';
        $html .= '<a class="nav-link ' . (request()->routeIs('home') ? 'active' : '') . '" href="' . route('home') . '">';
        $html .= app()->getLocale() == 'id' ? 'Beranda' : 'Home';
        $html .= '</a>';
        $html .= '</li>';

        // Profile dropdown
        $html .= '<li class="nav-item dropdown">';
        $html .= '<a class="nav-link dropdown-toggle ' . (request()->routeIs('profile*') ? 'active' : '') . '" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">';
        $html .= app()->getLocale() == 'id' ? 'Profil' : 'Profile';
        $html .= '</a>';
        $html .= '<ul class="dropdown-menu">';
        $html .= '<li><a class="dropdown-item" href="' . route('profile.vision-mission') . '">' . (app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission') . '</a></li>';
        $html .= '<li><a class="dropdown-item" href="' . route('profile.history') . '">' . (app()->getLocale() == 'id' ? 'Sejarah' : 'History') . '</a></li>';
        $html .= '</ul>';
        $html .= '</li>';

        // Facilities
        $html .= '<li class="nav-item">';
        $html .= '<a class="nav-link ' . (request()->routeIs('facilities') ? 'active' : '') . '" href="' . route('facilities') . '">';
        $html .= app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities';
        $html .= '</a>';
        $html .= '</li>';

        // Programs
        $html .= '<li class="nav-item">';
        $html .= '<a class="nav-link ' . (request()->routeIs('programs') ? 'active' : '') . '" href="' . route('programs') . '">';
        $html .= app()->getLocale() == 'id' ? 'Program' : 'Programs';
        $html .= '</a>';
        $html .= '</li>';

        // Information dropdown
        $html .= '<li class="nav-item dropdown">';
        $html .= '<a class="nav-link dropdown-toggle ' . (request()->routeIs('news*') || request()->routeIs('announcements*') || request()->routeIs('agenda*') || request()->routeIs('calendars*') || request()->routeIs('achievements*') || request()->routeIs('videos*') ? 'active' : '') . '" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">';
        $html .= app()->getLocale() == 'id' ? 'Informasi' : 'Information';
        $html .= '</a>';
        $html .= '<ul class="dropdown-menu">';
        $html .= '<li><a class="dropdown-item" href="' . route('news') . '">' . (app()->getLocale() == 'id' ? 'Berita' : 'News') . '</a></li>';
        $html .= '<li><a class="dropdown-item" href="' . route('announcements') . '">' . (app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements') . '</a></li>';
        $html .= '<li><a class="dropdown-item" href="' . route('agenda') . '">' . (app()->getLocale() == 'id' ? 'Agenda' : 'Agenda') . '</a></li>';
        $html .= '<li><a class="dropdown-item" href="' . route('videos') . '">' . (app()->getLocale() == 'id' ? 'Video' : 'Videos') . '</a></li>';
        $html .= '<li><a class="dropdown-item" href="' . route('achievements') . '">' . (app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements') . '</a></li>';
        $html .= '<li><a class="dropdown-item" href="' . route('calendars.index') . '">' . (app()->getLocale() == 'id' ? 'Kalender Akademik' : 'Academic Calendar') . '</a></li>';
        $html .= '</ul>';
        $html .= '</li>';

        // Gallery
        $html .= '<li class="nav-item">';
        $html .= '<a class="nav-link ' . (request()->routeIs('gallery*') ? 'active' : '') . '" href="' . route('gallery') . '">';
        $html .= app()->getLocale() == 'id' ? 'Galeri' : 'Gallery';
        $html .= '</a>';
        $html .= '</li>';

        // Contact
        $html .= '<li class="nav-item">';
        $html .= '<a class="nav-link ' . (request()->routeIs('contact') ? 'active' : '') . '" href="' . route('contact') . '">';
        $html .= app()->getLocale() == 'id' ? 'Kontak' : 'Contact';
        $html .= '</a>';
        $html .= '</li>';

        // Registration button
        $html .= '<li class="nav-item">';
        $html .= '<a class="nav-link btn btn-success text-white ms-lg-3 px-4" href="' . route('registration') . '">';
        $html .= app()->getLocale() == 'id' ? 'Pendaftaran' : 'Registration';
        $html .= '</a>';
        $html .= '</li>';

        $html .= '</ul>';

        return $html;
    }
}
