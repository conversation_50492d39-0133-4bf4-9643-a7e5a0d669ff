<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\TelegramSubscriber;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class TelegramBotService
{
    /**
     * The HTTP client instance.
     *
     * @var \GuzzleHttp\Client
     */
    protected $client;

    /**
     * The bot token.
     *
     * @var string|null
     */
    protected $botToken;

    /**
     * Debug mode flag.
     *
     * @var bool
     */
    protected $debugMode;

    /**
     * Create a new Telegram Bot service instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->client = new Client();
        $this->botToken = Setting::getValue('telegram_bot_token');
        $this->debugMode = Setting::getValue('telegram_debug_mode') === '1';

        if ($this->debugMode) {
            \Illuminate\Support\Facades\Log::debug('TelegramBotService initialized in debug mode');
        }
    }

    /**
     * Send a message to a chat.
     *
     * @param string $chatId
     * @param string $text
     * @param array $options
     * @param int|null $topicId Topic ID for forum groups
     * @return array|null
     */
    public function sendMessage($chatId, $text, array $options = [], ?int $topicId = null)
    {
        Log::debug('TelegramBotService::sendMessage called', [
            'chat_id' => $chatId,
            'text_length' => strlen($text),
            'bot_token_exists' => !empty($this->botToken),
            'topic_id' => $topicId
        ]);

        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $data = array_merge([
                'chat_id' => $chatId,
                'text' => $text,
                'parse_mode' => 'HTML',
            ], $options);

            // Add message_thread_id if topic ID is provided
            if ($topicId !== null) {
                $data['message_thread_id'] = $topicId;
                Log::debug('Adding message_thread_id to request', ['topic_id' => $topicId]);
            }

            Log::debug('Sending Telegram API request', [
                'endpoint' => "https://api.telegram.org/bot{$this->botToken}/sendMessage",
                'data' => array_merge($data, ['text' => '(text content)']) // Don't log the actual text for privacy
            ]);

            // Log the full request details for debugging
            Log::debug('Full Telegram API request details', [
                'endpoint' => "https://api.telegram.org/bot{$this->botToken}/sendMessage",
                'chat_id' => $data['chat_id'],
                'text_length' => strlen($data['text']),
                'parse_mode' => $data['parse_mode'],
                'has_reply_markup' => isset($data['reply_markup']),
                'reply_markup' => isset($data['reply_markup']) ? $data['reply_markup'] : null,
                'message_thread_id' => $data['message_thread_id'] ?? null
            ]);

            $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/sendMessage", [
                'form_params' => $data,
            ]);

            $result = json_decode($response->getBody(), true);

            // Log the full response for debugging
            Log::debug('Telegram API response', [
                'status_code' => $response->getStatusCode(),
                'result' => $result,
                'success' => ($result && isset($result['ok']) && $result['ok']),
                'error_code' => ($result && isset($result['error_code'])) ? $result['error_code'] : null,
                'description' => ($result && isset($result['description'])) ? $result['description'] : null
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send Telegram message: ' . $e->getMessage());
            Log::error('Exception details: ' . get_class($e));
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Send a message to all active subscribers.
     *
     * @param string $text Text in Indonesian (default language)
     * @param string $textEn Text in English (optional)
     * @param array $options Additional options for the message
     * @param array $excludeChatIds Chat IDs to exclude from broadcast
     * @param string|null $imageUrl URL or path to an image to send with the message
     * @param string|null $documentUrl URL or path to a document to send with the message
     * @return int Number of successful messages sent
     */
    public function broadcastMessage($text, $textEn = null, array $options = [], array $excludeChatIds = [], $imageUrl = null, $documentUrl = null)
    {
        $subscribers = TelegramSubscriber::active()->get();

        if ($subscribers->isEmpty()) {
            return 0;
        }

        $successCount = 0;

        foreach ($subscribers as $subscriber) {
            // Skip if this chat ID should be excluded
            if (in_array($subscriber->chat_id, $excludeChatIds)) {
                continue;
            }

            // Determine which language to use based on subscriber preference
            $messageText = $text; // Default to Indonesian
            if ($subscriber->language === 'en' && !empty($textEn)) {
                $messageText = $textEn; // Use English if preferred and available
            }

            $result = null;

            // If we have an image, send it with the message as caption
            if (!empty($imageUrl)) {
                $result = $this->sendPhoto($subscriber->chat_id, $imageUrl, $messageText, $options);
            }
            // If we have a document, send it with the message as caption
            else if (!empty($documentUrl)) {
                $result = $this->sendDocument($subscriber->chat_id, $documentUrl, $messageText, $options);
            }
            // Otherwise, just send the text message
            else {
                $result = $this->sendMessage($subscriber->chat_id, $messageText, $options);
            }

            if ($result && isset($result['ok']) && $result['ok']) {
                $successCount++;

                // If we have both image and document, send the document as a follow-up
                if (!empty($imageUrl) && !empty($documentUrl)) {
                    // Send document without caption to avoid duplicate text
                    $this->sendDocument($subscriber->chat_id, $documentUrl);
                }
            } else {
                Log::warning('Failed to send broadcast message to subscriber', [
                    'subscriber_id' => $subscriber->id,
                    'chat_id' => $subscriber->chat_id,
                    'language' => $subscriber->language
                ]);
            }
        }

        return $successCount;
    }

    /**
     * Send a message to admin chat.
     *
     * @param string $text
     * @param array $options
     * @param bool $useTopicId Whether to use the configured topic ID for forum groups
     * @param bool $sendToPersonalChat Whether to send to admin's personal chat
     * @param bool $sendToGroupChat Whether to send to group chat
     * @return array|null
     */
    public function sendAdminMessage($text, array $options = [], bool $useTopicId = false, bool $sendToPersonalChat = true, bool $sendToGroupChat = true)
    {
        $adminPersonalChatId = Setting::getValue('telegram_admin_chat_id');
        $groupChatId = Setting::getValue('telegram_chat_id');
        $topicId = $useTopicId ? (int)Setting::getValue('telegram_group_topic_id') : null;

        // Check if group chat ID is actually a group (starts with -)
        $isGroupChat = !empty($groupChatId) && strpos($groupChatId, '-') === 0;

        Log::debug('Sending admin message', [
            'admin_personal_chat_id' => $adminPersonalChatId,
            'group_chat_id' => $groupChatId,
            'is_group_chat' => $isGroupChat,
            'text_length' => strlen($text),
            'options' => $options,
            'use_topic_id' => $useTopicId,
            'topic_id' => $topicId,
            'send_to_personal' => $sendToPersonalChat,
            'send_to_group' => $sendToGroupChat
        ]);

        $results = [];

        // Send to admin's personal chat if configured
        if ($sendToPersonalChat && !empty($adminPersonalChatId)) {
            $personalResult = $this->sendMessage($adminPersonalChatId, $text, $options);
            $results['personal'] = $personalResult;

            Log::debug('Admin personal message send result', [
                'success' => ($personalResult && isset($personalResult['ok']) && $personalResult['ok']),
                'result' => $personalResult
            ]);
        } elseif ($sendToPersonalChat) {
            Log::warning('Admin personal chat ID is not configured.');
        }

        // Send to group chat if configured and it's a group
        if ($sendToGroupChat && $isGroupChat) {
            $groupResult = $this->sendMessage($groupChatId, $text, $options, $topicId);
            $results['group'] = $groupResult;

            Log::debug('Admin group message send result', [
                'success' => ($groupResult && isset($groupResult['ok']) && $groupResult['ok']),
                'result' => $groupResult
            ]);
        } elseif ($sendToGroupChat && !$isGroupChat && !empty($groupChatId)) {
            Log::warning('Group chat ID is configured but does not appear to be a group chat ID (should start with -).');
        } elseif ($sendToGroupChat) {
            Log::warning('Group chat ID is not configured.');
        }

        // For backward compatibility, return the personal chat result if available, otherwise the group result
        if (!empty($results['personal'])) {
            return $results['personal'];
        } elseif (!empty($results['group'])) {
            return $results['group'];
        }

        // If no messages were sent successfully
        if (empty($results)) {
            Log::error('Failed to send admin message: No valid chat IDs configured.');
            return null;
        }

        return reset($results); // Return the first result
    }

    /**
     * Process an incoming update from Telegram.
     *
     * @param array $update
     * @return array|null
     */
    public function processUpdate(array $update)
    {
        try {
            // Log the update for debugging
            Log::info('Processing Telegram update', [
                'update_type' => isset($update['message']) ? 'message' :
                                (isset($update['callback_query']) ? 'callback_query' : 'unknown'),
                'update_id' => $update['update_id'] ?? 'unknown'
            ]);

            // Check if this is a message
            if (isset($update['message'])) {
                Log::info('Processing message', [
                    'chat_id' => $update['message']['chat']['id'] ?? 'unknown',
                    'from_id' => $update['message']['from']['id'] ?? 'unknown',
                    'text' => isset($update['message']['text']) ?
                        (strlen($update['message']['text']) > 50 ?
                            substr($update['message']['text'], 0, 50) . '...' :
                            $update['message']['text']) :
                        'no text',
                    'has_entities' => isset($update['message']['entities']) ? 'yes' : 'no'
                ]);

                return $this->processMessage($update['message']);
            }

            // Check if this is a callback query (for inline buttons)
            if (isset($update['callback_query'])) {
                Log::info('Processing callback query', [
                    'chat_id' => $update['callback_query']['message']['chat']['id'] ?? 'unknown',
                    'from_id' => $update['callback_query']['from']['id'] ?? 'unknown',
                    'data' => $update['callback_query']['data'] ?? 'no data'
                ]);

                return $this->processCallbackQuery($update['callback_query']);
            }

            Log::warning('Unknown update type received', [
                'update_keys' => array_keys($update)
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Error processing Telegram update: ' . $e->getMessage());
            Log::error('Exception details: ' . get_class($e));
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Process a message from Telegram.
     *
     * @param array $message
     * @return array|null
     */
    protected function processMessage(array $message)
    {
        // Extract message data
        $chatId = $message['chat']['id'] ?? null;
        $text = $message['text'] ?? '';
        $username = $message['from']['username'] ?? null;
        $firstName = $message['from']['first_name'] ?? null;
        $lastName = $message['from']['last_name'] ?? null;

        if (!$chatId) {
            return null;
        }

        // Check if this chat ID belongs to an admin user
        $adminPersonalChatId = Setting::getValue('telegram_admin_chat_id');
        $isAdmin = ($chatId == $adminPersonalChatId);

        // Check if this chat ID is registered to any user in the system
        $userWithChatId = \App\Models\User::where('telegram_chat_id', $chatId)->first();

        // If this is an admin chat or a registered user, don't add them as a subscriber
        if ($isAdmin || $userWithChatId) {
            Log::info('Admin or registered user detected, not adding as subscriber', [
                'chat_id' => $chatId,
                'is_admin' => $isAdmin,
                'has_user_account' => ($userWithChatId ? true : false)
            ]);

            // Just update the existing subscriber record if it exists
            $subscriber = TelegramSubscriber::where('chat_id', $chatId)->first();

            // If no subscriber record exists, create a temporary one for this request
            if (!$subscriber) {
                $subscriber = new TelegramSubscriber([
                    'chat_id' => $chatId,
                    'username' => $username,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'is_active' => false, // Not active as a subscriber
                    'last_interaction_at' => now(),
                ]);
            } else {
                // Update the last interaction time
                $subscriber->last_interaction_at = now();
                $subscriber->save();
            }
        } else {
            // Regular user, update or create subscriber record and automatically activate
            $subscriber = TelegramSubscriber::where('chat_id', $chatId)->first();

            if (!$subscriber) {
                // New subscriber - create and activate automatically
                $subscriber = new TelegramSubscriber([
                    'chat_id' => $chatId,
                    'username' => $username,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'is_active' => true, // Automatically activate
                    'subscribed_at' => now(),
                    'last_interaction_at' => now(),
                ]);
                $subscriber->save();

                Log::info('New subscriber automatically activated', [
                    'chat_id' => $chatId,
                    'username' => $username,
                    'is_active' => true
                ]);
            } else {
                // Existing subscriber - update information
                $subscriber->username = $username;
                $subscriber->first_name = $firstName;
                $subscriber->last_name = $lastName;
                $subscriber->last_interaction_at = now();

                // If not already active, activate now
                if (!$subscriber->is_active) {
                    $subscriber->is_active = true;
                    $subscriber->subscribed_at = now();

                    Log::info('Existing subscriber automatically activated', [
                        'chat_id' => $chatId,
                        'username' => $username,
                        'is_active' => true
                    ]);
                }

                $subscriber->save();
            }
        }

        // Process commands
        if (isset($message['entities']) && is_array($message['entities'])) {
            foreach ($message['entities'] as $entity) {
                if ($entity['type'] === 'bot_command') {
                    $command = substr($text, $entity['offset'], $entity['length']);
                    return $this->processCommand($command, $chatId, $subscriber);
                }
            }
        }

        // Check if this is a reply to a message
        if (isset($message['reply_to_message'])) {
            return $this->processReplyToMessage($message, $chatId, $subscriber);
        }

        // Default response for non-command messages
        $adminPersonalChatId = \App\Models\Setting::getValue('telegram_admin_chat_id');

        if ($chatId == $adminPersonalChatId) {
            // For admin chat, show help for replying to contacts
            $helpMessage = "Untuk membalas pesan kontak, gunakan salah satu cara berikut:\n\n";
            $helpMessage .= "1. Gunakan perintah: <code>/reply [ID] [Pesan]</code>\n";
            $helpMessage .= "2. Balas langsung ke pesan notifikasi kontak\n\n";
            $helpMessage .= "Untuk melihat daftar perintah lainnya, gunakan /help";

            return $this->sendMessage($chatId, $helpMessage);
        } else {
            // For non-admin chats, show standard help
            return $this->sendMessage($chatId, 'Terima kasih atas pesan Anda. Gunakan /help untuk melihat perintah yang tersedia.');
        }
    }

    /**
     * Process a reply to a message from Telegram.
     *
     * @param array $message
     * @param string $chatId
     * @param \App\Models\TelegramSubscriber $subscriber
     * @return array|null
     */
    protected function processReplyToMessage(array $message, $chatId, $subscriber)
    {
        // Get the admin chat ID from settings
        $adminPersonalChatId = \App\Models\Setting::getValue('telegram_admin_chat_id');

        // Log the incoming reply message
        \Illuminate\Support\Facades\Log::info('Processing reply to message', [
            'chat_id' => $chatId,
            'admin_personal_chat_id' => $adminPersonalChatId,
            'is_admin' => ($chatId == $adminPersonalChatId),
            'message_id' => $message['message_id'] ?? 'unknown'
        ]);

        // Only allow replies from the admin chat
        if ($chatId != $adminPersonalChatId) {
            \Illuminate\Support\Facades\Log::warning('Non-admin attempted to reply to a message', [
                'chat_id' => $chatId
            ]);
            return $this->sendMessage($chatId, "⚠️ Maaf, hanya admin yang dapat membalas pesan kontak.");
        }

        // Get the reply text
        $replyText = $message['text'] ?? '';
        if (empty($replyText)) {
            \Illuminate\Support\Facades\Log::warning('Empty reply text received');
            return $this->sendMessage($chatId, "⚠️ Pesan balasan tidak boleh kosong.");
        }

        // Get the original message text
        $originalMessage = $message['reply_to_message']['text'] ?? '';
        if (empty($originalMessage)) {
            \Illuminate\Support\Facades\Log::warning('Empty original message text', [
                'reply_to_message' => $message['reply_to_message'] ?? 'not set'
            ]);
            return $this->sendMessage($chatId, "⚠️ Tidak dapat menemukan pesan asli untuk dibalas.");
        }

        // Log the original message for debugging
        \Illuminate\Support\Facades\Log::info('Original message content', [
            'message_length' => strlen($originalMessage),
            'message_preview' => substr($originalMessage, 0, 100) . '...',
        ]);



        // Log the full message content for debugging
        \Illuminate\Support\Facades\Log::debug('Full original message content', [
            'message' => $originalMessage
        ]);

        // Try multiple patterns to extract the contact ID from the original message
        $contactId = null;
        $allMatches = [];

        // Pattern 1: Look for "ID: X" format with HTML tags
        if (preg_match('/ID:\s*<b>(\d+)<\/b>/i', $originalMessage, $matches)) {
            $contactId = $matches[1];
            $allMatches['pattern1_html'] = $matches;
            \Illuminate\Support\Facades\Log::info('Found contact ID using pattern 1 (HTML)', ['contact_id' => $contactId]);
        }

        // Pattern 2: Look for "ID: X" format without HTML tags
        if (!$contactId && preg_match('/ID:\s*(\d+)/i', $originalMessage, $matches)) {
            $contactId = $matches[1];
            $allMatches['pattern2_plain'] = $matches;
            \Illuminate\Support\Facades\Log::info('Found contact ID using pattern 2 (plain)', ['contact_id' => $contactId]);
        }

        // Pattern 3: Look for "ID[: ]X" anywhere in the text
        if (!$contactId && preg_match('/ID[:\s]+(\d+)/im', $originalMessage, $matches)) {
            $contactId = $matches[1];
            $allMatches['pattern3_anywhere'] = $matches;
            \Illuminate\Support\Facades\Log::info('Found contact ID using pattern 3 (anywhere)', ['contact_id' => $contactId]);
        }

        // Pattern 4: Look for any number after "PESAN KONTAK BARU"
        if (!$contactId && preg_match('/PESAN KONTAK BARU.*?(\d+)/is', $originalMessage, $matches)) {
            $contactId = $matches[1];
            $allMatches['pattern4_after_header'] = $matches;
            \Illuminate\Support\Facades\Log::info('Found contact ID using pattern 4 (after header)', ['contact_id' => $contactId]);
        }

        // Pattern 5: Look for any number in the message (risky, but as a last resort)
        if (!$contactId && preg_match('/\b(\d+)\b/i', $originalMessage, $matches)) {
            // Check if it's a reasonable ID (not too large)
            if (strlen($matches[1]) < 10) {
                $contactId = $matches[1];
                $allMatches['pattern5_any_number'] = $matches;
                \Illuminate\Support\Facades\Log::info('Found contact ID using pattern 5 (any number)', ['contact_id' => $contactId]);
            }
        }

        // Pattern 6: Extract from reply_to_message_id if available
        if (!$contactId && isset($message['reply_to_message']['message_id'])) {
            // Try to find a contact with this message ID in the database
            // This would require storing message IDs when sending notifications
            $messageId = $message['reply_to_message']['message_id'];
            \Illuminate\Support\Facades\Log::info('Trying to use message_id as fallback', ['message_id' => $messageId]);

            // For now, just log it - we'll implement a proper lookup if needed
        }

        // If we still don't have a contact ID, check if there's a recent contact
        if (!$contactId) {
            // Get the most recent contact as a fallback
            $recentContact = \App\Models\Contact::latest()->first();
            if ($recentContact) {
                $contactId = $recentContact->id;
                \Illuminate\Support\Facades\Log::warning('Using most recent contact as fallback', ['contact_id' => $contactId]);

                // Inform the user that we're using a fallback
                $this->sendMessage($chatId, "⚠️ Tidak dapat menemukan ID kontak dari pesan asli. Menggunakan kontak terbaru (ID: {$contactId}) sebagai fallback.");
            }
        }

        if (!$contactId) {
            \Illuminate\Support\Facades\Log::error('Failed to extract contact ID from message', [
                'original_message_sample' => substr($originalMessage, 0, 200),
                'all_attempted_matches' => $allMatches
            ]);
            return $this->sendMessage($chatId, "⚠️ Tidak dapat menemukan ID kontak dari pesan asli. Gunakan format /reply [ID] [Pesan] sebagai gantinya.");
        }

        try {
            // Find the contact
            $contact = \App\Models\Contact::find($contactId);

            if (!$contact) {
                return $this->sendMessage($chatId, "⚠️ Kontak dengan ID {$contactId} tidak ditemukan.");
            }

            // Check if the contact has already been replied to
            if ($contact->hasReply()) {
                // Ask for confirmation to override the previous reply
                $confirmMessage = "⚠️ Kontak ini sudah pernah dibalas sebelumnya.\n\n";
                $confirmMessage .= "<b>Balasan sebelumnya:</b>\n";
                $confirmMessage .= "<i>{$contact->reply_message}</i>\n\n";
                $confirmMessage .= "Untuk mengganti balasan, gunakan:\n";
                $confirmMessage .= "/reply_confirm {$contactId} {$replyText}";

                return $this->sendMessage($chatId, $confirmMessage);
            }

            // Update the contact with the reply
            $contact->update([
                'reply_message' => $replyText,
                'replied_at' => now(),
                'replied_by' => null, // Will be updated with the user ID if available
                'is_read' => true,
                'read_at' => now(),
            ]);

            // Try to find a user associated with this Telegram chat
            $user = \App\Models\User::where('telegram_chat_id', $chatId)->first();
            if ($user) {
                $contact->update(['replied_by' => $user->id]);
            }

            // Send email notification to the contact
            $this->sendReplyEmail($contact);

            // Send confirmation message
            $successMessage = "✅ Balasan berhasil dikirim ke {$contact->name}.\n\n";
            $successMessage .= "<b>Subjek:</b> {$contact->subject}\n";
            $successMessage .= "<b>Balasan:</b> {$replyText}";

            return $this->sendMessage($chatId, $successMessage);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing reply to message: ' . $e->getMessage());
            return $this->sendMessage($chatId, "⚠️ Terjadi kesalahan saat memproses balasan: " . $e->getMessage());
        }
    }

    /**
     * Process a command from Telegram.
     *
     * @param string $command
     * @param string $chatId
     * @param \App\Models\TelegramSubscriber $subscriber
     * @return array|null
     */
    protected function processCommand($command, $chatId, $subscriber)
    {
        // Add detailed logging for debugging
        Log::info('Processing Telegram command', [
            'command' => $command,
            'chat_id' => $chatId,
            'subscriber_id' => $subscriber->id ?? 'new',
            'subscriber_username' => $subscriber->username ?? 'none'
        ]);

        // Check if it's a reply command
        if (strpos($command, '/reply_confirm') === 0) {
            return $this->processReplyConfirmCommand($command, $chatId, $subscriber);
        } else if (strpos($command, '/reply') === 0) {
            return $this->processReplyCommand($command, $chatId, $subscriber);
        }

        switch ($command) {
            case '/start':
                // Add detailed logging for /start command
                Log::info('Processing /start command', [
                    'chat_id' => $chatId,
                    'bot_token_exists' => !empty($this->botToken),
                    'subscriber_exists' => ($subscriber->exists ?? false)
                ]);

                // Check if this is an admin or registered user
                $adminPersonalChatId = Setting::getValue('telegram_admin_chat_id');
                $isAdmin = ($chatId == $adminPersonalChatId);
                $userWithChatId = \App\Models\User::where('telegram_chat_id', $chatId)->first();

                Log::info('User status check for /start', [
                    'admin_personal_chat_id' => $adminPersonalChatId,
                    'is_admin' => $isAdmin,
                    'has_user_account' => ($userWithChatId ? true : false)
                ]);

                // Subscription is now handled automatically in processMessage
                // Just log that we're processing the start command
                Log::info('Processing /start command for subscriber', [
                    'subscriber_id' => $subscriber->id ?? 'unknown',
                    'is_active' => $subscriber->is_active ?? false,
                    'subscribed_at' => $subscriber->subscribed_at ?? 'not set'
                ]);

                // Islamic greeting in Indonesian
                $welcomeMessageId = "السَّلاَمُ عَلَيْكُمْ وَرَحْمَةُ اللهِ وَبَرَكَاتُهُ\n\n";
                $welcomeMessageId .= "Selamat datang di Bot Telegram Pondok Pesantren Nurul Hayah 4! 🌟\n\n";

                // Islamic greeting in English
                $welcomeMessageEn = "Assalamu'alaikum Warahmatullahi Wabarakatuh\n\n";
                $welcomeMessageEn .= "Welcome to Nurul Hayah 4 Islamic Boarding School Telegram Bot! 🌟\n\n";

                // Combined message with both languages
                $welcomeMessage = $welcomeMessageId . "\n" . $welcomeMessageEn . "\n";

                // Show user ID
                $welcomeMessage .= "ID Telegram Anda / Your Telegram ID: <code>{$chatId}</code>\n\n";

                // Subscription status message based on user type
                if ($isAdmin || $userWithChatId) {
                    if ($isAdmin) {
                        $welcomeMessage .= "Anda terdaftar sebagai Admin. / You are registered as an Admin.\n\n";
                    } else {
                        $welcomeMessage .= "Anda terdaftar sebagai Pengguna Sistem. / You are registered as a System User.\n\n";
                    }
                } else {
                    $welcomeMessage .= "Anda telah otomatis berlangganan untuk menerima pengumuman terbaru dari kami. / You have been automatically subscribed to receive our latest announcements.\n\n";
                }

                // Language selection prompt
                $welcomeMessage .= "Silakan pilih bahasa yang Anda inginkan: / Please select your preferred language:\n";

                // Create inline keyboard for language selection
                $keyboard = [
                    'inline_keyboard' => [
                        [
                            ['text' => '🇮🇩 Bahasa Indonesia', 'callback_data' => 'lang_id'],
                            ['text' => '🇬🇧 English', 'callback_data' => 'lang_en']
                        ]
                    ]
                ];

                // Log before sending welcome message
                Log::info('Sending welcome message', [
                    'chat_id' => $chatId,
                    'message_length' => strlen($welcomeMessage),
                    'has_keyboard' => true
                ]);

                // Send welcome message with language selection buttons
                $result = $this->sendMessage($chatId, $welcomeMessage, [
                    'reply_markup' => json_encode($keyboard)
                ]);

                // Log the result of sending welcome message
                Log::info('Welcome message send result', [
                    'success' => ($result && isset($result['ok']) && $result['ok']),
                    'result' => $result ? 'Message sent' : 'Failed to send message'
                ]);

                return $result;

            case '/help':
                $helpMessage = "Perintah yang tersedia:\n\n";
                $helpMessage .= "• /start - Menampilkan pesan selamat datang\n";
                $helpMessage .= "• /help - Menampilkan bantuan ini\n";
                $helpMessage .= "• /info - Menampilkan informasi tentang Anda\n";
                $helpMessage .= "• /unsubscribe - Berhenti berlangganan pengumuman\n";
                $helpMessage .= "• /subscribe - Mulai berlangganan kembali\n";
                $helpMessage .= "• /reply [ID] [Pesan] - Balas pesan kontak dengan ID tertentu\n";

                // Add note about automatic subscription
                $helpMessage .= "\nCatatan: Anda telah otomatis berlangganan saat pertama kali menggunakan bot ini.\n";

                return $this->sendMessage($chatId, $helpMessage);

            case '/info':
                $infoMessage = "Informasi Anda:\n\n";
                $infoMessage .= "• ID Chat: <code>{$chatId}</code>\n";
                if ($subscriber->username) {
                    $infoMessage .= "• Username: @{$subscriber->username}\n";
                }
                $infoMessage .= "• Nama: {$subscriber->first_name} {$subscriber->last_name}\n";
                $infoMessage .= "• Status: " . ($subscriber->is_active ? "Berlangganan ✅" : "Tidak Berlangganan ❌") . "\n";
                $infoMessage .= "• Berlangganan sejak: " . $subscriber->subscribed_at->format('d M Y H:i') . "\n";

                return $this->sendMessage($chatId, $infoMessage);

            case '/unsubscribe':
                $subscriber->is_active = false;
                $subscriber->save();

                return $this->sendMessage($chatId, "Anda telah berhenti berlangganan pengumuman. Gunakan /subscribe untuk berlangganan kembali.");

            case '/subscribe':
                $subscriber->is_active = true;
                $subscriber->save();

                return $this->sendMessage($chatId, "Anda telah berlangganan kembali dan akan menerima pengumuman terbaru dari kami.");

            default:
                return $this->sendMessage($chatId, "Perintah tidak dikenali. Gunakan /help untuk melihat perintah yang tersedia.");
        }
    }

    /**
     * Process a reply command from Telegram.
     * Format: /reply [contact_id] [message]
     *
     * @param string $command
     * @param string $chatId
     * @param \App\Models\TelegramSubscriber $subscriber
     * @return array|null
     */
    protected function processReplyCommand($command, $chatId, $subscriber)
    {
        // Get the admin chat ID from settings
        $adminPersonalChatId = \App\Models\Setting::getValue('telegram_admin_chat_id');

        // Only allow replies from the admin chat
        if ($chatId != $adminPersonalChatId) {
            return $this->sendMessage($chatId, "⚠️ Maaf, hanya admin yang dapat membalas pesan kontak.");
        }

        // Parse the command: /reply [contact_id] [message]
        $parts = explode(' ', $command, 3);

        // Check if the command format is correct
        if (count($parts) < 3) {
            return $this->sendMessage($chatId, "⚠️ Format perintah salah. Gunakan: /reply [ID] [Pesan]");
        }

        $contactId = trim($parts[1]);
        $replyMessage = trim($parts[2]);

        // Check if the contact ID is valid
        if (!is_numeric($contactId)) {
            return $this->sendMessage($chatId, "⚠️ ID kontak harus berupa angka.");
        }

        // Check if the reply message is not empty
        if (empty($replyMessage)) {
            return $this->sendMessage($chatId, "⚠️ Pesan balasan tidak boleh kosong.");
        }

        try {
            // Find the contact
            $contact = \App\Models\Contact::find($contactId);

            if (!$contact) {
                return $this->sendMessage($chatId, "⚠️ Kontak dengan ID {$contactId} tidak ditemukan.");
            }

            // Check if the contact has already been replied to
            if ($contact->hasReply()) {
                // Ask for confirmation to override the previous reply
                $confirmMessage = "⚠️ Kontak ini sudah pernah dibalas sebelumnya.\n\n";
                $confirmMessage .= "<b>Balasan sebelumnya:</b>\n";
                $confirmMessage .= "<i>{$contact->reply_message}</i>\n\n";
                $confirmMessage .= "Untuk mengganti balasan, gunakan:\n";
                $confirmMessage .= "/reply_confirm {$contactId} {$replyMessage}";

                return $this->sendMessage($chatId, $confirmMessage);
            }

            // Update the contact with the reply
            $contact->update([
                'reply_message' => $replyMessage,
                'replied_at' => now(),
                'replied_by' => null, // Will be updated with the user ID if available
                'is_read' => true,
                'read_at' => now(),
            ]);

            // Try to find a user associated with this Telegram chat
            $user = \App\Models\User::where('telegram_chat_id', $chatId)->first();
            if ($user) {
                $contact->update(['replied_by' => $user->id]);
            }

            // Send email notification to the contact
            $this->sendReplyEmail($contact);

            // Send confirmation message
            $successMessage = "✅ Balasan berhasil dikirim ke {$contact->name}.\n\n";
            $successMessage .= "<b>Subjek:</b> {$contact->subject}\n";
            $successMessage .= "<b>Balasan:</b> {$replyMessage}";

            return $this->sendMessage($chatId, $successMessage);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing reply command: ' . $e->getMessage());
            return $this->sendMessage($chatId, "⚠️ Terjadi kesalahan saat memproses balasan: " . $e->getMessage());
        }
    }

    /**
     * Process a reply confirmation command from Telegram.
     * Format: /reply_confirm [contact_id] [message]
     *
     * @param string $command
     * @param string $chatId
     * @param \App\Models\TelegramSubscriber $subscriber
     * @return array|null
     */
    protected function processReplyConfirmCommand($command, $chatId, $subscriber)
    {
        // Get the admin chat ID from settings
        $adminPersonalChatId = \App\Models\Setting::getValue('telegram_admin_chat_id');

        // Only allow replies from the admin chat
        if ($chatId != $adminPersonalChatId) {
            return $this->sendMessage($chatId, "⚠️ Maaf, hanya admin yang dapat membalas pesan kontak.");
        }

        // Parse the command: /reply_confirm [contact_id] [message]
        $parts = explode(' ', $command, 3);

        // Check if the command format is correct
        if (count($parts) < 3) {
            return $this->sendMessage($chatId, "⚠️ Format perintah salah. Gunakan: /reply_confirm [ID] [Pesan]");
        }

        $contactId = trim($parts[1]);
        $replyMessage = trim($parts[2]);

        // Check if the contact ID is valid
        if (!is_numeric($contactId)) {
            return $this->sendMessage($chatId, "⚠️ ID kontak harus berupa angka.");
        }

        // Check if the reply message is not empty
        if (empty($replyMessage)) {
            return $this->sendMessage($chatId, "⚠️ Pesan balasan tidak boleh kosong.");
        }

        try {
            // Find the contact
            $contact = \App\Models\Contact::find($contactId);

            if (!$contact) {
                return $this->sendMessage($chatId, "⚠️ Kontak dengan ID {$contactId} tidak ditemukan.");
            }

            // Store the old reply message for logging
            $oldReplyMessage = $contact->reply_message;

            // Update the contact with the new reply
            $contact->update([
                'reply_message' => $replyMessage,
                'replied_at' => now(),
                'is_read' => true,
                'read_at' => now(),
            ]);

            // Try to find a user associated with this Telegram chat
            $user = \App\Models\User::where('telegram_chat_id', $chatId)->first();
            if ($user) {
                $contact->update(['replied_by' => $user->id]);
            }

            // Send email notification to the contact
            $this->sendReplyEmail($contact);

            // Log the reply update
            \Illuminate\Support\Facades\Log::info("Contact reply updated via Telegram", [
                'contact_id' => $contact->id,
                'old_reply' => $oldReplyMessage,
                'new_reply' => $replyMessage,
                'telegram_chat_id' => $chatId
            ]);

            // Send confirmation message
            $successMessage = "✅ Balasan berhasil diperbarui untuk {$contact->name}.\n\n";
            $successMessage .= "<b>Subjek:</b> {$contact->subject}\n";
            $successMessage .= "<b>Balasan baru:</b> {$replyMessage}";

            return $this->sendMessage($chatId, $successMessage);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing reply confirm command: ' . $e->getMessage());
            return $this->sendMessage($chatId, "⚠️ Terjadi kesalahan saat memproses konfirmasi balasan: " . $e->getMessage());
        }
    }

    /**
     * Send a reply email to the contact.
     *
     * @param \App\Models\Contact $contact
     * @return bool
     */
    protected function sendReplyEmail($contact)
    {
        // Only send email if an email address is provided
        if (!$contact->email) {
            \Illuminate\Support\Facades\Log::warning("Cannot send reply email: No email address provided for contact ID {$contact->id}");
            return false;
        }

        try {
            // Get the email service
            $emailService = app(\App\Services\EmailService::class);

            // Determine the locale based on the contact's message or default to Indonesian
            $locale = app()->getLocale();

            // Prepare data for the email
            $data = [
                'name' => $contact->name,
                'subject' => $contact->subject,
                'message' => $contact->message,
                'reply_message' => $contact->reply_message,
            ];

            \Illuminate\Support\Facades\Log::info("Attempting to send reply email to: {$contact->email} with subject: " . __('emails.contact_reply_subject'));

            // Send email using the 'notifications' email type with the template
            $result = $emailService->sendEmailWithView(
                $contact->email,
                __('emails.contact_reply_subject'),
                'emails.contact-reply',
                $data,
                'notifications',
                $locale
            );

            if ($result) {
                // Log that we sent an email
                \Illuminate\Support\Facades\Log::info("Contact reply email sent successfully to: {$contact->email} in {$locale} language");
                return true;
            } else {
                \Illuminate\Support\Facades\Log::error("Failed to send contact reply email to: {$contact->email}");
                return false;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Error sending reply email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process a callback query from Telegram (for inline buttons).
     *
     * @param array $callbackQuery
     * @return array|null
     */
    protected function processCallbackQuery(array $callbackQuery)
    {
        $chatId = $callbackQuery['message']['chat']['id'] ?? null;
        $data = $callbackQuery['data'] ?? '';
        $callbackQueryId = $callbackQuery['id'] ?? null;

        if (!$chatId || !$callbackQueryId) {
            return null;
        }

        // Find the subscriber
        $subscriber = TelegramSubscriber::where('chat_id', $chatId)->first();

        if (!$subscriber) {
            return null;
        }

        $subscriber->updateLastInteraction();

        // Process the callback data
        if ($data === 'subscribe') {
            $subscriber->is_active = true;
            $subscriber->save();

            // Answer the callback query
            $this->answerCallbackQuery($callbackQueryId, 'Anda telah berlangganan!');

            // Update the message
            return $this->sendMessage($chatId, "Anda telah berlangganan dan akan menerima pengumuman terbaru dari kami.");
        } elseif ($data === 'unsubscribe') {
            $subscriber->is_active = false;
            $subscriber->save();

            // Answer the callback query
            $this->answerCallbackQuery($callbackQueryId, 'Anda telah berhenti berlangganan!');

            // Update the message
            return $this->sendMessage($chatId, "Anda telah berhenti berlangganan pengumuman. Gunakan /subscribe untuk berlangganan kembali.");
        } elseif ($data === 'lang_id') {
            // Set language preference to Indonesian
            $subscriber->language = 'id';
            $subscriber->save();

            // Answer the callback query
            $this->answerCallbackQuery($callbackQueryId, 'Bahasa Indonesia dipilih!');

            // Send confirmation message
            return $this->sendMessage($chatId, "✅ Bahasa Indonesia telah dipilih sebagai bahasa pilihan Anda.\n\nAnda akan menerima pengumuman dalam Bahasa Indonesia.");
        } elseif ($data === 'lang_en') {
            // Set language preference to English
            $subscriber->language = 'en';
            $subscriber->save();

            // Answer the callback query
            $this->answerCallbackQuery($callbackQueryId, 'English selected!');

            // Send confirmation message
            return $this->sendMessage($chatId, "✅ English has been selected as your preferred language.\n\nYou will receive announcements in English.");
        }

        return null;
    }

    /**
     * Answer a callback query.
     *
     * @param string $callbackQueryId
     * @param string $text
     * @param bool $showAlert
     * @return array|null
     */
    protected function answerCallbackQuery($callbackQueryId, $text = '', $showAlert = false)
    {
        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $data = [
                'callback_query_id' => $callbackQueryId,
                'text' => $text,
                'show_alert' => $showAlert,
            ];

            $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/answerCallbackQuery", [
                'form_params' => $data,
            ]);

            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::error('Failed to answer callback query: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Set the webhook URL for the bot.
     *
     * @param string $url
     * @return array|null
     */
    public function setWebhook($url)
    {
        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/setWebhook", [
                'form_params' => [
                    'url' => $url,
                ],
            ]);

            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::error('Failed to set webhook: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get information about the current webhook.
     *
     * @return array|null
     */
    public function getWebhookInfo()
    {
        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $response = $this->client->get("https://api.telegram.org/bot{$this->botToken}/getWebhookInfo");
            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::error('Failed to get webhook info: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete the webhook.
     *
     * @return array|null
     */
    public function deleteWebhook()
    {
        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/deleteWebhook");
            return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::error('Failed to delete webhook: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Debug a message structure.
     * This method is useful for debugging the structure of messages received from Telegram.
     *
     * @param array $message
     * @return array
     */
    public function debugMessageStructure(array $message)
    {
        // Create a sanitized version of the message for logging
        $sanitized = $this->sanitizeMessageForLogging($message);

        // Log the message structure
        Log::info('Telegram message structure debug', [
            'message' => $sanitized
        ]);

        return $sanitized;
    }

    /**
     * Process a command for testing purposes.
     * This is a public wrapper around the protected processCommand method for testing.
     *
     * @param string $command
     * @param string $chatId
     * @param \App\Models\TelegramSubscriber $subscriber
     * @return array|null
     */
    public function processCommandForTesting($command, $chatId, $subscriber)
    {
        return $this->processCommand($command, $chatId, $subscriber);
    }

    /**
     * Process a reply to message for testing purposes.
     * This is a public wrapper around the protected processReplyToMessage method for testing.
     *
     * @param array $message
     * @param string $chatId
     * @param \App\Models\TelegramSubscriber $subscriber
     * @return array|null
     */
    public function processReplyToMessageForTesting(array $message, $chatId, $subscriber)
    {
        return $this->processReplyToMessage($message, $chatId, $subscriber);
    }

    /**
     * Sanitize a message for logging.
     * This method removes sensitive information from the message.
     *
     * @param array $data
     * @return array
     */
    protected function sanitizeMessageForLogging(array $data)
    {
        $result = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $result[$key] = $this->sanitizeMessageForLogging($value);
            } else if (is_string($value) && strlen($value) > 100) {
                // Truncate long strings
                $result[$key] = substr($value, 0, 100) . '... [truncated]';
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * Send a photo to a chat.
     *
     * @param string $chatId
     * @param string $photo URL or file path
     * @param string $caption
     * @param array $options
     * @param int|null $topicId Topic ID for forum groups
     * @return array|null
     */
    public function sendPhoto($chatId, $photo, $caption = '', array $options = [], ?int $topicId = null)
    {
        Log::debug('TelegramBotService::sendPhoto called', [
            'chat_id' => $chatId,
            'photo' => $photo,
            'caption_length' => strlen($caption),
            'bot_token_exists' => !empty($this->botToken),
            'topic_id' => $topicId
        ]);

        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $data = array_merge([
                'chat_id' => $chatId,
                'caption' => $caption,
                'parse_mode' => 'HTML',
            ], $options);

            // Add message_thread_id if topic ID is provided
            if ($topicId !== null) {
                $data['message_thread_id'] = $topicId;
                Log::debug('Adding message_thread_id to photo request', ['topic_id' => $topicId]);
            }

            // Check if photo is a URL or a file path
            if (filter_var($photo, FILTER_VALIDATE_URL)) {
                $data['photo'] = $photo;

                Log::debug('Sending photo via URL', [
                    'endpoint' => "https://api.telegram.org/bot{$this->botToken}/sendPhoto",
                    'photo_url' => $photo
                ]);

                $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/sendPhoto", [
                    'form_params' => $data,
                ]);
            } else {
                // It's a file path, use multipart/form-data
                $multipart = [];
                foreach ($data as $key => $value) {
                    $multipart[] = [
                        'name' => $key,
                        'contents' => $value
                    ];
                }

                // Add the photo file
                $multipart[] = [
                    'name' => 'photo',
                    'contents' => fopen(public_path('storage/' . $photo), 'r')
                ];

                Log::debug('Sending photo via file upload', [
                    'endpoint' => "https://api.telegram.org/bot{$this->botToken}/sendPhoto",
                    'photo_path' => $photo
                ]);

                $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/sendPhoto", [
                    'multipart' => $multipart,
                ]);
            }

            $result = json_decode($response->getBody(), true);

            Log::debug('Telegram API response for sendPhoto', [
                'status_code' => $response->getStatusCode(),
                'result' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send Telegram photo: ' . $e->getMessage());
            Log::error('Exception details: ' . get_class($e));
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Send a document to a chat.
     *
     * @param string $chatId
     * @param string $document URL or file path
     * @param string $caption
     * @param array $options
     * @param int|null $topicId Topic ID for forum groups
     * @return array|null
     */
    public function sendDocument($chatId, $document, $caption = '', array $options = [], ?int $topicId = null)
    {
        Log::debug('TelegramBotService::sendDocument called', [
            'chat_id' => $chatId,
            'document' => $document,
            'caption_length' => strlen($caption),
            'bot_token_exists' => !empty($this->botToken),
            'topic_id' => $topicId
        ]);

        if (empty($this->botToken)) {
            Log::error('Telegram bot token is not configured.');
            return null;
        }

        try {
            $data = array_merge([
                'chat_id' => $chatId,
                'caption' => $caption,
                'parse_mode' => 'HTML',
            ], $options);

            // Add message_thread_id if topic ID is provided
            if ($topicId !== null) {
                $data['message_thread_id'] = $topicId;
                Log::debug('Adding message_thread_id to document request', ['topic_id' => $topicId]);
            }

            // Check if document is a URL or a file path
            if (filter_var($document, FILTER_VALIDATE_URL)) {
                $data['document'] = $document;

                Log::debug('Sending document via URL', [
                    'endpoint' => "https://api.telegram.org/bot{$this->botToken}/sendDocument",
                    'document_url' => $document
                ]);

                $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/sendDocument", [
                    'form_params' => $data,
                ]);
            } else {
                // It's a file path, use multipart/form-data
                $multipart = [];
                foreach ($data as $key => $value) {
                    $multipart[] = [
                        'name' => $key,
                        'contents' => $value
                    ];
                }

                // Add the document file
                $multipart[] = [
                    'name' => 'document',
                    'contents' => fopen(public_path('storage/' . $document), 'r')
                ];

                Log::debug('Sending document via file upload', [
                    'endpoint' => "https://api.telegram.org/bot{$this->botToken}/sendDocument",
                    'document_path' => $document
                ]);

                $response = $this->client->post("https://api.telegram.org/bot{$this->botToken}/sendDocument", [
                    'multipart' => $multipart,
                ]);
            }

            $result = json_decode($response->getBody(), true);

            Log::debug('Telegram API response for sendDocument', [
                'status_code' => $response->getStatusCode(),
                'result' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send Telegram document: ' . $e->getMessage());
            Log::error('Exception details: ' . get_class($e));
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return null;
        }
    }
}
