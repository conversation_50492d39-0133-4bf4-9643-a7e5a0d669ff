<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the session has a locale set
        if (session()->has('locale')) {
            app()->setLocale(session('locale'));
        } else {
            // Default to Indonesian
            app()->setLocale('id');
        }

        return $next($request);
    }
}
