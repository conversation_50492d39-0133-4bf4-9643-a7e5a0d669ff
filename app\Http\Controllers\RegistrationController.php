<?php

namespace App\Http\Controllers;

use App\Http\Requests\RegistrationRequest;
use App\Models\Registration;
use App\Models\RegistrationSchedule;
use App\Models\RegistrationRequirement;
use App\Models\RegistrationFee;
use App\Models\RegistrationInfo;
use App\Events\RegistrationCreated;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;

class RegistrationController extends Controller
{
    /**
     * Display the registration form.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Check if registration is open
        $isOpen = RegistrationSchedule::isRegistrationOpen();
        $currentSchedule = RegistrationSchedule::getCurrentSchedule();

        // Check if any registration schedule exists (active or not)
        $hasSchedule = RegistrationSchedule::where('is_active', true)->exists();

        // Get registration requirements
        $requirements = RegistrationRequirement::where('is_active', true)
            ->orderBy('order', 'asc')
            ->get();

        // Get registration fees
        $fees = RegistrationFee::where('is_active', true)
            ->orderBy('order', 'asc')
            ->get();

        // Get additional information
        $info = RegistrationInfo::where('is_active', true)->first();

        return view('public.registration.index', compact(
            'isOpen',
            'currentSchedule',
            'hasSchedule',
            'requirements',
            'fees',
            'info'
        ));
    }

    /**
     * Store a newly created registration in storage.
     *
     * @param  \App\Http\Requests\RegistrationRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(RegistrationRequest $request)
    {
        try {
            // Generate registration number
            $registrationNumber = 'REG-' . date('Ymd') . '-' . rand(1000, 9999);

            // Get validated data - this preserves the case formatting from the frontend
            $data = $request->validated();

            // Use the full phone number (with country code) if available
            if (!empty($data['phone_full'])) {
                $data['phone'] = $data['phone_full']; // Save the complete phone number with country code
            }

            // Set status and registration number
            $data['status'] = 'pending';
            $data['registration_number'] = $registrationNumber;

            // Create registration
            $registration = Registration::create($data);

            // Send email notification
            $this->sendRegistrationEmail($registration);

            // Dispatch event for Telegram notification
            try {
                \Illuminate\Support\Facades\Log::info('Dispatching RegistrationCreated event from controller', ['registration_id' => $registration->id]);
                event(new RegistrationCreated($registration));
                \Illuminate\Support\Facades\Log::info('RegistrationCreated event dispatched from controller');
            } catch (\Exception $e) {
                // Log the error but don't fail the registration process
                \Illuminate\Support\Facades\Log::error('Error dispatching RegistrationCreated event: ' . $e->getMessage());
                \Illuminate\Support\Facades\Log::error('Stack trace: ' . $e->getTraceAsString());
            }

            // Check if request is AJAX
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => app()->getLocale() == 'id' ? 'Pendaftaran berhasil dikirim!' : 'Registration submitted successfully!',
                    'redirect' => route('registration.success', ['registration' => $registration->id])
                ]);
            }

            // Regular form submission
            return redirect()->route('registration.success', ['registration' => $registration->id])
                ->with('success', app()->getLocale() == 'id' ?
                    'Pendaftaran berhasil dikirim!' :
                    'Registration submitted successfully!');
        } catch (\Exception $e) {
            // Log the error with detailed information
            \Illuminate\Support\Facades\Log::error("Registration error: " . $e->getMessage());
            \Illuminate\Support\Facades\Log::error("Stack trace: " . $e->getTraceAsString());

            // Add more detailed error information for debugging
            if (config('app.debug')) {
                \Illuminate\Support\Facades\Log::debug("Registration data: " . json_encode($request->all()));
            }

            // Check if request is AJAX
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => app()->getLocale() == 'id' ?
                        'Terjadi kesalahan saat memproses pendaftaran. Silakan coba lagi atau hubungi kami jika masalah berlanjut.' :
                        'An error occurred while processing your registration. Please try again or contact us if the problem persists.'
                ], 422);
            }

            // Regular form submission
            return redirect()->back()
                ->withInput()
                ->with('error', app()->getLocale() == 'id' ?
                    'Terjadi kesalahan saat memproses pendaftaran. Silakan coba lagi atau hubungi kami jika masalah berlanjut.' :
                    'An error occurred while processing your registration. Please try again or contact us if the problem persists.');
        }
    }

    /**
     * Display the registration success page.
     *
     * @param  \App\Models\Registration  $registration
     * @return \Illuminate\View\View
     */
    public function success(\App\Models\Registration $registration)
    {
        return view('public.registration.success', compact('registration'));
    }

    /**
     * Display the printable registration receipt.
     *
     * @param  \App\Models\Registration  $registration
     * @return \Illuminate\View\View
     */
    public function printReceipt(\App\Models\Registration $registration)
    {
        return view('public.registration.print', compact('registration'));
    }

    /**
     * Check registration status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function checkStatus(Request $request)
    {
        $request->validate([
            'registration_number' => 'required|string',
        ]);

        $registration = Registration::where('registration_number', $request->registration_number)
            ->first();

        if (!$registration) {
            return redirect()->back()
                ->with('error', 'Registration number not found!');
        }

        return view('public.registration.status', compact('registration'));
    }

    /**
     * Send registration confirmation email.
     *
     * @param  \App\Models\Registration  $registration
     * @return void
     */
    protected function sendRegistrationEmail(Registration $registration)
    {
        // Only send email if an email address is provided
        if (!$registration->email) {
            return;
        }

        try {
            // Use EmailService to send registration confirmation email
            $emailService = new \App\Services\EmailService();

            // Get the current locale
            $locale = app()->getLocale();

            // Prepare data for the email template
            $data = [
                'name' => $registration->name,
                'email' => $registration->email,
                'registration_number' => $registration->registration_number,
            ];

            // Send email using the 'confirmations' email type with the template
            $emailService->sendEmailWithView(
                $registration->email,
                __('emails.registration_subject'),
                'emails.registration',
                $data,
                'confirmations',
                $locale
            );

            // Log that we sent an email
            \Illuminate\Support\Facades\Log::info("Registration confirmation email sent to: {$registration->email} in {$locale} language");
        } catch (\Exception $e) {
            // Log the error but don't fail the registration process
            \Illuminate\Support\Facades\Log::error("Failed to send registration email: " . $e->getMessage());
        }
    }
}


