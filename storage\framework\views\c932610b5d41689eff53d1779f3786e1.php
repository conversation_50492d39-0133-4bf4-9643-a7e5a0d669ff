<?php $__env->startSection('title', 'View Profile'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Profile</h1>
        <div>
            <a href="<?php echo e(route('admin.profiles.edit', $profile)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.profiles.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Profile Details</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-3 fw-bold">ID:</div>
                <div class="col-md-9"><?php echo e($profile->id); ?></div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3 fw-bold">Type:</div>
                <div class="col-md-9">
                    <?php if($profile->type == 'motto'): ?>
                        <span class="badge bg-warning">Motto</span>
                    <?php elseif($profile->type == 'vision'): ?>
                        <span class="badge bg-primary">Vision</span>
                    <?php elseif($profile->type == 'mission'): ?>
                        <span class="badge bg-success">Mission</span>
                    <?php elseif($profile->type == 'history'): ?>
                        <span class="badge bg-info">History</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3 fw-bold">Created At:</div>
                <div class="col-md-9"><?php echo e($profile->created_at->format('d M Y H:i:s')); ?></div>
            </div>

            <div class="row mb-3">
                <div class="col-md-3 fw-bold">Updated At:</div>
                <div class="col-md-9"><?php echo e($profile->updated_at->format('d M Y H:i:s')); ?></div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Indonesian Content</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title:</div>
                        <div class="col-md-9"><?php echo e($profile->title); ?></div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Content:</div>
                        <div class="col-md-9"><?php echo $profile->content; ?></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">English Content</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Title:</div>
                        <div class="col-md-9"><?php echo e($profile->title_en); ?></div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Content:</div>
                        <div class="col-md-9"><?php echo $profile->content_en; ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if($profile->type === 'history' && $profile->image): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Content Image</h5>
            </div>
            <div class="card-body text-center">
                <img src="<?php echo e(asset('storage/' . $profile->image)); ?>" alt="Content Image" class="img-thumbnail" style="max-height: 300px;">
            </div>
        </div>
    <?php endif; ?>

    <?php if($profile->type === 'history' && count($timelines) > 0): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Timeline Entries</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Year</th>
                                <th>Title (ID)</th>
                                <th>Title (EN)</th>
                                <th>Content (ID)</th>
                                <th>Content (EN)</th>
                                <th>Image</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $timelines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $timeline): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($timeline->year); ?></td>
                                    <td><?php echo e($timeline->title); ?></td>
                                    <td><?php echo e($timeline->title_en); ?></td>
                                    <td><?php echo e(Str::limit($timeline->content, 50)); ?></td>
                                    <td><?php echo e(Str::limit($timeline->content_en, 50)); ?></td>
                                    <td>
                                        <?php if($timeline->image): ?>
                                            <img src="<?php echo e(asset('storage/' . $timeline->image)); ?>" alt="Timeline Image" class="img-thumbnail" style="max-height: 50px;">
                                        <?php else: ?>
                                            <span class="text-muted">No image</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/profiles/show.blade.php ENDPATH**/ ?>