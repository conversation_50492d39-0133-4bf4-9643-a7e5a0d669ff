<?php $__env->startSection('title', 'View Education Unit'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Education Unit</h1>
        <div>
            <a href="<?php echo e(route('admin.education-units.edit', $educationUnit)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.education-units.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body text-center">
                    <?php if($educationUnit->image): ?>
                        <img src="<?php echo e(asset('storage/' . $educationUnit->image)); ?>" alt="<?php echo e($educationUnit->name); ?>" class="img-fluid rounded">
                    <?php else: ?>
                        <div class="alert alert-info">No image available</div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Status Information</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Education Type:</th>
                            <td>
                                <?php if($educationUnit->edu_type == 'formal'): ?>
                                    <span class="badge bg-primary">Formal</span>
                                <?php else: ?>
                                    <span class="badge bg-info">Non-Formal</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <?php if($educationUnit->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Display Order:</th>
                            <td><?php echo e($educationUnit->order); ?></td>
                        </tr>
                        <tr>
                            <th>Created:</th>
                            <td><?php echo e($educationUnit->created_at->format('d M Y H:i')); ?></td>
                        </tr>
                        <tr>
                            <th>Last Updated:</th>
                            <td><?php echo e($educationUnit->updated_at->format('d M Y H:i')); ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Contact Information</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>Address:</th>
                            <td><?php echo e($educationUnit->address ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td><?php echo e($educationUnit->phone ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td><?php echo e($educationUnit->email ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Website:</th>
                            <td>
                                <?php if($educationUnit->website): ?>
                                    <a href="<?php echo e($educationUnit->website); ?>" target="_blank"><?php echo e($educationUnit->website); ?></a>
                                <?php else: ?>
                                    N/A
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Basic Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <table class="table">
                                <tr>
                                    <th>Name:</th>
                                    <td><?php echo e($educationUnit->name); ?></td>
                                </tr>
                                <tr>
                                    <th>Level:</th>
                                    <td><?php echo e($educationUnit->level ?? 'N/A'); ?></td>
                                </tr>
                            </table>
                            <h6 class="mt-3">Description:</h6>
                            <div class="p-3 bg-light rounded">
                                <?php echo $educationUnit->description ?? 'No description available.'; ?>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <table class="table">
                                <tr>
                                    <th>Name:</th>
                                    <td><?php echo e($educationUnit->name_en); ?></td>
                                </tr>
                                <tr>
                                    <th>Level:</th>
                                    <td><?php echo e($educationUnit->level_en ?? 'N/A'); ?></td>
                                </tr>
                            </table>
                            <h6 class="mt-3">Description:</h6>
                            <div class="p-3 bg-light rounded">
                                <?php echo $educationUnit->description_en ?? 'No description available.'; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Facilities</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <div class="p-3 bg-light rounded">
                                <?php echo $educationUnit->facilities ?? 'No facilities information available.'; ?>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <div class="p-3 bg-light rounded">
                                <?php echo $educationUnit->facilities_en ?? 'No facilities information available.'; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/education_units/show.blade.php ENDPATH**/ ?>