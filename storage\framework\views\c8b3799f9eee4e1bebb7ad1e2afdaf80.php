<?php $__env->startSection('title', 'Videos'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Videos</h1>
        <a href="<?php echo e(route('admin.videos.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Video
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Thumbnail</th>
                            <th>Title</th>
                            <th>YouTube URL</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $videos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $video): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php if($video->thumbnail): ?>
                                        <img src="<?php echo e(asset('storage/' . $video->thumbnail)); ?>" alt="<?php echo e($video->title); ?>" width="50" height="50" class="img-thumbnail">
                                    <?php else: ?>
                                        <div class="ratio ratio-16x9" style="width: 80px; height: 45px;">
                                            <iframe src="<?php echo e($video->youtube_embed_url); ?>" title="<?php echo e($video->title); ?>" allowfullscreen></iframe>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($video->title); ?></td>
                                <td>
                                    <a href="<?php echo e($video->youtube_url); ?>" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                                        <?php echo e($video->youtube_url); ?>

                                    </a>
                                </td>
                                <td><?php echo e($video->order); ?></td>
                                <td>
                                    <?php if($video->is_active): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.videos.show', $video)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.videos.edit', $video)); ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.videos.destroy', $video)); ?>" method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this video?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="text-center">No videos found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <?php echo e($videos->links()); ?>

            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/videos/index.blade.php ENDPATH**/ ?>