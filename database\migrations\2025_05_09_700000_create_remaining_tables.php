<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create contacts table
        if (!Schema::hasTable('contacts')) {
            Schema::create('contacts', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email');
                $table->string('phone')->nullable();
                $table->string('subject');
                $table->text('message');
                $table->text('reply_message')->nullable();
                $table->unsignedBigInteger('replied_by')->nullable();
                $table->timestamp('replied_at')->nullable();
                $table->boolean('is_read')->default(false);
                $table->timestamps();

                $table->foreign('replied_by')->references('id')->on('users')->onDelete('set null');
            });
        }

        // Create registrations table
        if (!Schema::hasTable('registrations')) {
            Schema::create('registrations', function (Blueprint $table) {
                $table->id();
                $table->string('registration_number')->unique();
                $table->string('full_name');
                $table->string('nik')->nullable();
                $table->string('nisn')->nullable();
                $table->enum('gender', ['male', 'female']);
                $table->string('place_of_birth');
                $table->date('date_of_birth');
                $table->string('father_name')->nullable();
                $table->string('father_occupation')->nullable();
                $table->string('mother_name')->nullable();
                $table->string('mother_occupation')->nullable();
                $table->text('address');
                $table->string('phone');
                $table->string('email')->nullable();
                $table->string('last_education');
                $table->string('previous_school')->nullable();
                $table->string('graduation_year')->nullable();
                $table->unsignedBigInteger('education_unit_id')->nullable();
                $table->text('reason')->nullable();
                $table->string('hobby')->nullable();
                $table->string('ambition')->nullable();
                $table->text('notes')->nullable();
                $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
                $table->text('rejection_reason')->nullable();
                $table->timestamps();

                $table->foreign('education_unit_id')->references('id')->on('education_units')->onDelete('set null');
            });
        }

        // Create registration_schedules table
        if (!Schema::hasTable('registration_schedules')) {
            Schema::create('registration_schedules', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->date('start_date');
                $table->date('end_date');
                $table->text('description')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create registration_requirements table
        if (!Schema::hasTable('registration_requirements')) {
            Schema::create('registration_requirements', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->text('description')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create registration_fees table
        if (!Schema::hasTable('registration_fees')) {
            Schema::create('registration_fees', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->decimal('amount', 10, 2);
                $table->text('description')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create registration_info table
        if (!Schema::hasTable('registration_info')) {
            Schema::create('registration_info', function (Blueprint $table) {
                $table->id();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create directors table
        if (!Schema::hasTable('directors')) {
            Schema::create('directors', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('position');
                $table->string('position_en')->nullable();
                $table->text('bio')->nullable();
                $table->text('bio_en')->nullable();
                $table->string('image')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create insights table
        if (!Schema::hasTable('insights')) {
            Schema::create('insights', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('director_id');
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->string('image')->nullable();
                $table->boolean('is_featured')->default(false);
                $table->boolean('is_active')->default(true);
                $table->timestamp('published_at')->nullable();
                $table->timestamps();

                $table->foreign('director_id')->references('id')->on('directors')->onDelete('cascade');
            });
        }

        // Create pages table
        if (!Schema::hasTable('pages')) {
            Schema::create('pages', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->string('slug')->unique();
                $table->string('slug_en')->nullable()->unique();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->string('featured_image')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create swipers table
        if (!Schema::hasTable('swipers')) {
            Schema::create('swipers', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('image');
                $table->string('button_text')->nullable();
                $table->string('button_text_en')->nullable();
                $table->string('button_url')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create timelines table
        if (!Schema::hasTable('timelines')) {
            Schema::create('timelines', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('image')->nullable();
                $table->date('timeline_date');
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Create webhooks table
        if (!Schema::hasTable('webhooks')) {
            Schema::create('webhooks', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('url');
                $table->string('secret')->nullable();
                $table->json('events')->nullable();
                $table->json('headers')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop all tables in reverse order to avoid foreign key constraints
        Schema::dropIfExists('webhooks');
        Schema::dropIfExists('timelines');
        Schema::dropIfExists('swipers');
        Schema::dropIfExists('pages');
        Schema::dropIfExists('insights');
        Schema::dropIfExists('directors');
        Schema::dropIfExists('registration_info');
        Schema::dropIfExists('registration_fees');
        Schema::dropIfExists('registration_requirements');
        Schema::dropIfExists('registration_schedules');
        Schema::dropIfExists('registrations');
        Schema::dropIfExists('contacts');
    }
};
