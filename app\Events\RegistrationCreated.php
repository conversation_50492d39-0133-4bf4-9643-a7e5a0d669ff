<?php

namespace App\Events;

use App\Models\Registration;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RegistrationCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The registration instance.
     *
     * @var \App\Models\Registration
     */
    public $registration;

    /**
     * Create a new event instance.
     */
    public function __construct(Registration $registration)
    {
        $this->registration = $registration;
    }
}
