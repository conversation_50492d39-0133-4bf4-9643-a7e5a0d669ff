/**
 * Artwork JavaScript for Nurul Hayah 4 Website
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animations
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
    });

    // Lightbox functionality for artwork images
    initLightbox();

    // Filter buttons active state
    initFilterButtons();
});

/**
 * Initialize lightbox functionality for artwork images
 */
function initLightbox() {
    // Image zoom functionality
    const imageZoomLinks = document.querySelectorAll('.image-zoom, .view-fullscreen');
    if (imageZoomLinks.length > 0) {
        imageZoomLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const imgSrc = this.getAttribute('href');
                const imgAlt = this.getAttribute('title') || 'Artwork Image';
                createLightbox(imgSrc, imgAlt);
            });
        });
    }

    // Artwork overlay links
    const artworkLinks = document.querySelectorAll('.artwork-overlay .artwork-link');
    if (artworkLinks.length > 0) {
        artworkLinks.forEach(link => {
            if (link.classList.contains('lightbox-trigger')) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('href');
                    const imgAlt = this.getAttribute('title') || 'Artwork Image';
                    createLightbox(imgSrc, imgAlt);
                });
            }
        });
    }
}

/**
 * Create a lightbox with the given image source and alt text
 * 
 * @param {string} imgSrc - The source URL of the image
 * @param {string} imgAlt - The alt text for the image
 */
function createLightbox(imgSrc, imgAlt) {
    const lightbox = document.createElement('div');
    lightbox.classList.add('lightbox');

    lightbox.innerHTML = `
        <div class="lightbox-content">
            <img src="${imgSrc}" alt="${imgAlt}">
            <span class="lightbox-close">&times;</span>
        </div>
    `;
    document.body.appendChild(lightbox);

    // Prevent scrolling when lightbox is open
    document.body.style.overflow = 'hidden';

    // Close lightbox when clicking on the close button or outside the image
    lightbox.addEventListener('click', function(e) {
        if (e.target === this || e.target.classList.contains('lightbox-close')) {
            document.body.removeChild(lightbox);
            document.body.style.overflow = 'auto';
        }
    });

    // Close lightbox with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.querySelector('.lightbox')) {
            document.body.removeChild(lightbox);
            document.body.style.overflow = 'auto';
        }
    });
}

/**
 * Initialize filter buttons active state
 */
function initFilterButtons() {
    const filterButtons = document.querySelectorAll('.paint-filter .btn, .literature-filter .btn, .artwork-categories .btn');
    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => {
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-success');
                });
                
                // Add active class to clicked button
                this.classList.remove('btn-outline-success');
                this.classList.add('btn-success');
            });
        });
    }
}
