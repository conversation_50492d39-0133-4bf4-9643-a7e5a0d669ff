<?php $__env->startSection('title', 'Create Leader'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Create Leader</h1>
        <a href="<?php echo e(route('admin.leaders.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="<?php echo e(route('admin.leaders.store')); ?>" method="POST" enctype="multipart/form-data" id="leaderForm">
                <?php echo csrf_field(); ?>

                <!-- Section tabs -->
                <ul class="nav nav-tabs mb-4" id="sectionTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="identity-tab" data-bs-toggle="tab" data-bs-target="#identity-section" type="button" role="tab" aria-controls="identity-section" aria-selected="true">Identity</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="background-tab" data-bs-toggle="tab" data-bs-target="#background-section" type="button" role="tab" aria-controls="background-section" aria-selected="false">Background</button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="sectionTabsContent">
                    <!-- Section 1: Identity -->
                    <div class="tab-pane fade show active" id="identity-section" role="tabpanel" aria-labelledby="identity-tab">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="name" class="form-label">Name</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <input type="hidden" id="name_en" name="name_en" value="<?php echo e(old('name_en', old('name'))); ?>">
                                <small class="text-muted">Name will be displayed the same in both Indonesian and English.</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="position" class="form-label">Position (Indonesian)</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="position" name="position" value="<?php echo e(old('position')); ?>" required>
                                <?php $__errorArgs = ['position'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="position_en" class="form-label">Position (English)</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['position_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="position_en" name="position_en" value="<?php echo e(old('position_en')); ?>">
                                <?php $__errorArgs = ['position_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="motto" class="form-label">Motto (Indonesian)</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['motto'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="motto" name="motto" value="<?php echo e(old('motto')); ?>">
                                <?php $__errorArgs = ['motto'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="motto_en" class="form-label">Motto (English)</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['motto_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="motto_en" name="motto_en" value="<?php echo e(old('motto_en')); ?>">
                                <?php $__errorArgs = ['motto_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="bio" class="form-label">Bio (Indonesian) <small class="text-muted">(max 100 words)</small></label>
                                <textarea class="form-control summernote <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="bio" name="bio" rows="4"><?php echo e(old('bio')); ?></textarea>
                                <small class="form-text text-muted">Please keep your bio concise, maximum 100 words.</small>
                                <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="bio_en" class="form-label">Bio (English) <small class="text-muted">(max 100 words)</small></label>
                                <textarea class="form-control summernote <?php $__errorArgs = ['bio_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="bio_en" name="bio_en" rows="4"><?php echo e(old('bio_en')); ?></textarea>
                                <small class="form-text text-muted">Please keep your bio concise, maximum 100 words.</small>
                                <?php $__errorArgs = ['bio_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" value="<?php echo e(old('email')); ?>">
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="phone" class="form-label">Phone</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="phone" name="phone" value="<?php echo e(old('phone')); ?>">
                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="image" class="form-label">Image</label>
                                <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="image" name="image" accept="image/*">
                                <small class="text-muted">Recommended size: 400x400 pixels. Max size: 2MB.</small>
                                <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="order" class="form-label">Display Order</label>
                                <input type="number" class="form-control <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="order" name="order" value="<?php echo e(old('order')); ?>" min="1">
                                <small class="text-muted">Lower numbers will be displayed first. Leave empty for automatic ordering.</small>
                                <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <h4 class="mt-4 mb-3">Social Media</h4>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="social_facebook" class="form-label">Facebook</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-facebook-f"></i></span>
                                    <input type="text" class="form-control <?php $__errorArgs = ['social_facebook'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="social_facebook" name="social_facebook" value="<?php echo e(old('social_facebook')); ?>" placeholder="https://facebook.com/username">
                                </div>
                                <?php $__errorArgs = ['social_facebook'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="social_twitter" class="form-label">X</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-x-twitter"></i></span>
                                    <input type="text" class="form-control <?php $__errorArgs = ['social_twitter'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="social_twitter" name="social_twitter" value="<?php echo e(old('social_twitter')); ?>" placeholder="https://x.com/username">
                                </div>
                                <?php $__errorArgs = ['social_twitter'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="social_instagram" class="form-label">Instagram</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                    <input type="text" class="form-control <?php $__errorArgs = ['social_instagram'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="social_instagram" name="social_instagram" value="<?php echo e(old('social_instagram')); ?>" placeholder="https://instagram.com/username">
                                </div>
                                <?php $__errorArgs = ['social_instagram'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="col-md-6">
                                <label for="social_linkedin" class="form-label">Telegram</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-telegram"></i></span>
                                    <input type="text" class="form-control <?php $__errorArgs = ['social_linkedin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="social_linkedin" name="social_linkedin" value="<?php echo e(old('social_linkedin')); ?>" placeholder="https://t.me/username">
                                </div>
                                <?php $__errorArgs = ['social_linkedin'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="mt-3">
                                    <img id="image-preview" src="#" alt="Preview" class="img-thumbnail d-none" style="max-height: 300px;">
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="button" class="btn btn-primary" id="goToBackgroundBtn">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Section 2: Background -->
                    <div class="tab-pane fade" id="background-section" role="tabpanel" aria-labelledby="background-tab">
                        <!-- Education History -->
                        <h4 class="mb-3">Education History</h4>
                        <div id="education-container">
                            <div class="education-item card mb-3">
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Institution (Indonesian)</label>
                                            <input type="text" class="form-control" name="education_history[0][institution]" value="<?php echo e(old('education_history.0.institution')); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Institution (English)</label>
                                            <input type="text" class="form-control" name="education_history_en[0][institution]" value="<?php echo e(old('education_history_en.0.institution')); ?>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Degree (Indonesian)</label>
                                            <input type="text" class="form-control" name="education_history[0][degree]" value="<?php echo e(old('education_history.0.degree')); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Degree (English)</label>
                                            <input type="text" class="form-control" name="education_history_en[0][degree]" value="<?php echo e(old('education_history_en.0.degree')); ?>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Year</label>
                                            <input type="text" class="form-control" name="education_history[0][year]" value="<?php echo e(old('education_history.0.year')); ?>" placeholder="e.g., 2010-2014">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control" name="education_history[0][location]" value="<?php echo e(old('education_history.0.location')); ?>">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-education" style="display: none;">Remove</button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-success mb-4" id="add-education">
                            <i class="fas fa-plus"></i> Add Education
                        </button>

                        <!-- Achievements -->
                        <h4 class="mb-3">Achievements</h4>
                        <div id="achievements-container">
                            <div class="achievement-item card mb-3">
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Title (Indonesian)</label>
                                            <input type="text" class="form-control" name="achievements[0][title]" value="<?php echo e(old('achievements.0.title')); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Title (English)</label>
                                            <input type="text" class="form-control" name="achievements_en[0][title]" value="<?php echo e(old('achievements_en.0.title')); ?>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Description (Indonesian)</label>
                                            <textarea class="form-control" name="achievements[0][description]" rows="2"><?php echo e(old('achievements.0.description')); ?></textarea>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Description (English)</label>
                                            <textarea class="form-control" name="achievements_en[0][description]" rows="2"><?php echo e(old('achievements_en.0.description')); ?></textarea>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Year</label>
                                            <input type="text" class="form-control" name="achievements[0][year]" value="<?php echo e(old('achievements.0.year')); ?>">
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-achievement" style="display: none;">Remove</button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-success mb-4" id="add-achievement">
                            <i class="fas fa-plus"></i> Add Achievement
                        </button>

                        <!-- Work Experience -->
                        <h4 class="mb-3">Work Experience</h4>
                        <div id="work-experience-container">
                            <div class="work-experience-item card mb-3">
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Company/Organization (Indonesian)</label>
                                            <input type="text" class="form-control" name="work_experience[0][company]" value="<?php echo e(old('work_experience.0.company')); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Company/Organization (English)</label>
                                            <input type="text" class="form-control" name="work_experience_en[0][company]" value="<?php echo e(old('work_experience_en.0.company')); ?>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Position (Indonesian)</label>
                                            <input type="text" class="form-control" name="work_experience[0][position]" value="<?php echo e(old('work_experience.0.position')); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Position (English)</label>
                                            <input type="text" class="form-control" name="work_experience_en[0][position]" value="<?php echo e(old('work_experience_en.0.position')); ?>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Period</label>
                                            <input type="text" class="form-control" name="work_experience[0][period]" value="<?php echo e(old('work_experience.0.period')); ?>" placeholder="e.g., 2015-2020">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Location</label>
                                            <input type="text" class="form-control" name="work_experience[0][location]" value="<?php echo e(old('work_experience.0.location')); ?>">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Description (Indonesian)</label>
                                            <textarea class="form-control" name="work_experience[0][description]" rows="2"><?php echo e(old('work_experience.0.description')); ?></textarea>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Description (English)</label>
                                            <textarea class="form-control" name="work_experience_en[0][description]" rows="2"><?php echo e(old('work_experience_en.0.description')); ?></textarea>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger remove-work-experience" style="display: none;">Remove</button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-success mb-4" id="add-work-experience">
                            <i class="fas fa-plus"></i> Add Work Experience
                        </button>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-between mt-4">
                            <button type="button" class="btn btn-secondary" id="backToIdentityBtn">
                                <i class="fas fa-arrow-left"></i> Back
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save"></i> Save
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const preview = document.getElementById('image-preview');
        const file = e.target.files[0];

        if (file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            }

            reader.readAsDataURL(file);
        } else {
            preview.src = '#';
            preview.classList.add('d-none');
        }
    });

    // Initialize Summernote for bio fields with word limit
    $(document).ready(function() {
        // Special initialization for bio fields with word limit
        $('#bio, #bio_en').each(function() {
            $(this).summernote({
                placeholder: 'Write your bio here (max 100 words)...',
                tabsize: 2,
                height: 200,
                toolbar: [
                    ['font', ['bold', 'underline', 'italic', 'clear']],
                    ['fontname', ['fontname']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']]
                ],
                callbacks: {
                    onKeydown: function(e) {
                        var t = e.currentTarget.innerText;
                        var wordCount = t.trim().split(/\s+/).length;

                        // If we're over the word limit and not pressing backspace/delete
                        if (wordCount >= 100 && e.keyCode !== 8 && e.keyCode !== 46) {
                            // Check if it's a word-creating key (space or enter)
                            if (e.keyCode === 32 || e.keyCode === 13) {
                                e.preventDefault();
                                return false;
                            }
                        }
                    },
                    onKeyup: function(e) {
                        updateWordCount(this);
                    },
                    onPaste: function(e) {
                        setTimeout(function() {
                            updateWordCount(this);
                        }.bind(this), 100);
                    },
                    onInit: function() {
                        updateWordCount(this);
                    }
                }
            });
        });

        // Function to update word count display
        function updateWordCount(editor) {
            var text = $(editor).summernote('code');
            var plainText = $('<div>').html(text).text();
            var words = plainText.trim().split(/\s+/);
            var wordCount = plainText.trim() === '' ? 0 : words.length;

            // Find the counter element
            var $counter = $(editor).siblings('.word-counter');
            if ($counter.length === 0) {
                // Create counter if it doesn't exist
                $counter = $('<div class="word-counter mt-1 text-muted small"></div>');
                $(editor).after($counter);
            }

            // Update counter text and style
            $counter.text('Word count: ' + wordCount + ' / 100');

            // Add warning style if approaching limit
            if (wordCount > 90) {
                $counter.removeClass('text-muted').addClass('text-warning');
            } else if (wordCount >= 100) {
                $counter.removeClass('text-muted text-warning').addClass('text-danger');
            } else {
                $counter.removeClass('text-warning text-danger').addClass('text-muted');
            }
        }

        // Initialize word counters
        setTimeout(function() {
            $('#bio, #bio_en').each(function() {
                updateWordCount(this);
            });
        }, 500);
    });

    // Section navigation
    document.getElementById('goToBackgroundBtn').addEventListener('click', function() {
        const backgroundTab = document.getElementById('background-tab');
        bootstrap.Tab.getOrCreateInstance(backgroundTab).show();
    });

    document.getElementById('backToIdentityBtn').addEventListener('click', function() {
        const identityTab = document.getElementById('identity-tab');
        bootstrap.Tab.getOrCreateInstance(identityTab).show();
    });

    // Sync name to English version
    document.getElementById('name').addEventListener('input', function() {
        document.getElementById('name_en').value = this.value;
    });

    // Dynamic Education History fields
    let educationIndex = 0;

    document.getElementById('add-education').addEventListener('click', function() {
        educationIndex++;
        const template = `
            <div class="education-item card mb-3">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Institution (Indonesian)</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][institution]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Institution (English)</label>
                            <input type="text" class="form-control" name="education_history_en[${educationIndex}][institution]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Degree (Indonesian)</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][degree]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Degree (English)</label>
                            <input type="text" class="form-control" name="education_history_en[${educationIndex}][degree]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Year</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][year]" placeholder="e.g., 2010-2014">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control" name="education_history[${educationIndex}][location]">
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-education">Remove</button>
                </div>
            </div>
        `;

        document.getElementById('education-container').insertAdjacentHTML('beforeend', template);

        // Show remove buttons if there's more than one education item
        if (document.querySelectorAll('.education-item').length > 1) {
            document.querySelectorAll('.remove-education').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }
    });

    // Remove education item
    document.getElementById('education-container').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-education')) {
            e.target.closest('.education-item').remove();

            // Hide remove buttons if there's only one education item left
            if (document.querySelectorAll('.education-item').length <= 1) {
                document.querySelectorAll('.remove-education').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
    });

    // Dynamic Achievements fields
    let achievementIndex = 0;

    document.getElementById('add-achievement').addEventListener('click', function() {
        achievementIndex++;
        const template = `
            <div class="achievement-item card mb-3">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Title (Indonesian)</label>
                            <input type="text" class="form-control" name="achievements[${achievementIndex}][title]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Title (English)</label>
                            <input type="text" class="form-control" name="achievements_en[${achievementIndex}][title]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description (Indonesian)</label>
                            <textarea class="form-control" name="achievements[${achievementIndex}][description]" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Description (English)</label>
                            <textarea class="form-control" name="achievements_en[${achievementIndex}][description]" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Year</label>
                            <input type="text" class="form-control" name="achievements[${achievementIndex}][year]">
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-achievement">Remove</button>
                </div>
            </div>
        `;

        document.getElementById('achievements-container').insertAdjacentHTML('beforeend', template);

        // Show remove buttons if there's more than one achievement item
        if (document.querySelectorAll('.achievement-item').length > 1) {
            document.querySelectorAll('.remove-achievement').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }
    });

    // Remove achievement item
    document.getElementById('achievements-container').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-achievement')) {
            e.target.closest('.achievement-item').remove();

            // Hide remove buttons if there's only one achievement item left
            if (document.querySelectorAll('.achievement-item').length <= 1) {
                document.querySelectorAll('.remove-achievement').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
    });

    // Dynamic Work Experience fields
    let workExperienceIndex = 0;

    document.getElementById('add-work-experience').addEventListener('click', function() {
        workExperienceIndex++;
        const template = `
            <div class="work-experience-item card mb-3">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Company/Organization (Indonesian)</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][company]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Company/Organization (English)</label>
                            <input type="text" class="form-control" name="work_experience_en[${workExperienceIndex}][company]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Position (Indonesian)</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][position]">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Position (English)</label>
                            <input type="text" class="form-control" name="work_experience_en[${workExperienceIndex}][position]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Period</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][period]" placeholder="e.g., 2015-2020">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Location</label>
                            <input type="text" class="form-control" name="work_experience[${workExperienceIndex}][location]">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Description (Indonesian)</label>
                            <textarea class="form-control" name="work_experience[${workExperienceIndex}][description]" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Description (English)</label>
                            <textarea class="form-control" name="work_experience_en[${workExperienceIndex}][description]" rows="2"></textarea>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger remove-work-experience">Remove</button>
                </div>
            </div>
        `;

        document.getElementById('work-experience-container').insertAdjacentHTML('beforeend', template);

        // Show remove buttons if there's more than one work experience item
        if (document.querySelectorAll('.work-experience-item').length > 1) {
            document.querySelectorAll('.remove-work-experience').forEach(btn => {
                btn.style.display = 'inline-block';
            });
        }
    });

    // Remove work experience item
    document.getElementById('work-experience-container').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-work-experience')) {
            e.target.closest('.work-experience-item').remove();

            // Hide remove buttons if there's only one work experience item left
            if (document.querySelectorAll('.work-experience-item').length <= 1) {
                document.querySelectorAll('.remove-work-experience').forEach(btn => {
                    btn.style.display = 'none';
                });
            }
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/leaders/create.blade.php ENDPATH**/ ?>