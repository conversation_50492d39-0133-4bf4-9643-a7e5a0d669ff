<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agenda;
use App\Services\ImageService;
use Illuminate\Http\Request;

class AgendaController extends Controller
{
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param ImageService $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agendas = Agenda::orderBy('date', 'desc')->get();
        return view('admin.agendas.index', compact('agendas'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.agendas.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'date' => 'required|date',
            'time' => 'required',
            'location' => 'nullable|string|max:255',
            'location_en' => 'nullable|string|max:255',
            'organizer' => 'nullable|string|max:255',
            'organizer_en' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Agenda::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'agendas',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }



        Agenda::create($data);

        return redirect()->route('admin.agendas.index')
            ->with('success', 'Agenda created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show(string $id)
    {
        $agenda = Agenda::findOrFail($id);
        return view('admin.agendas.show', compact('agenda'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit(string $id)
    {
        $agenda = Agenda::findOrFail($id);
        return view('admin.agendas.edit', compact('agenda'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, string $id)
    {
        $agenda = Agenda::findOrFail($id);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'date' => 'required|date',
            'time' => 'required',
            'location' => 'nullable|string|max:255',
            'location_en' => 'nullable|string|max:255',
            'organizer' => 'nullable|string|max:255',
            'organizer_en' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->all();

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($agenda->image) {
                $this->imageService->deleteImage($agenda->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'agendas',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }



        $agenda->update($data);

        return redirect()->route('admin.agendas.index')
            ->with('success', 'Agenda updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(string $id)
    {
        $agenda = Agenda::findOrFail($id);

        // Delete image if exists
        if ($agenda->image) {
            $this->imageService->deleteImage($agenda->image);
        }

        $agenda->delete();

        return redirect()->route('admin.agendas.index')
            ->with('success', 'Agenda deleted successfully.');
    }
}
