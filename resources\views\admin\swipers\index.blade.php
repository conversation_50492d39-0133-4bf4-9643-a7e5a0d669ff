@extends('admin.layouts.app')

@section('title', 'Manage Swiper Images')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Manage Swiper Images</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Swiper Images</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div><i class="fas fa-images me-1"></i> Swiper Images</div>
            @if($canAddMore)
                <a href="{{ route('admin.swipers.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> Add New Image
                </a>
            @else
                <button class="btn btn-secondary btn-sm" disabled title="Maximum of 3 swiper images reached">
                    <i class="fas fa-plus me-1"></i> Add New Image
                </button>
            @endif
        </div>
        <div class="card-body">

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-1"></i> These images will be displayed in the carousel on the home page. Maximum of 5 images allowed.
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Image</th>
                            <th>Title</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($swipers as $swiper)
                            <tr>
                                <td>{{ $swiper->order }}</td>
                                <td>
                                    <img src="{{ asset('storage/' . $swiper->image) }}" alt="{{ $swiper->title }}" class="img-thumbnail" style="max-height: 100px;">
                                </td>
                                <td>
                                    <strong>{{ $swiper->title }}</strong>
                                    @if($swiper->title_en)
                                        <br><small class="text-muted">{{ $swiper->title_en }}</small>
                                    @endif
                                </td>

                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.swipers.edit', $swiper->id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('admin.swipers.show', $swiper->id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form action="{{ route('admin.swipers.destroy', $swiper->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this swiper image?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center">No swiper images found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

