@extends('admin.layouts.app')

@section('title', 'Curriculum Details')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ $curriculum->title }}</h1>
            <p class="text-muted">{{ $curriculum->edu_type == 'formal' ? 'Formal Education' : 'Non-Formal Education' }}</p>
        </div>
        <div>
            <a href="{{ route('admin.curricula.edit', $curriculum) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.curricula.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Main Content Container -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <ul class="nav nav-tabs card-header-tabs" id="curriculumTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="indonesian-tab" data-bs-toggle="tab" data-bs-target="#indonesian" type="button" role="tab" aria-controls="indonesian" aria-selected="true">
                        <i class="fas fa-language me-1"></i> Indonesian Content
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="english-tab" data-bs-toggle="tab" data-bs-target="#english" type="button" role="tab" aria-controls="english" aria-selected="false">
                        <i class="fas fa-globe me-1"></i> English Content
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="curriculumTabsContent">
                <!-- Indonesian Content Tab -->
                <div class="tab-pane fade show active" id="indonesian" role="tabpanel" aria-labelledby="indonesian-tab">
                    <div class="row g-4">
                        <!-- Basic Information -->
                        <div class="col-md-12">
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="text-muted small">Title</label>
                                                <p class="mb-1 fw-bold">{{ $curriculum->title }}</p>
                                            </div>
                                        </div>
                                        @if($curriculum->education_unit_id && $curriculum->educationUnit)
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="text-muted small">Education Unit</label>
                                                <p class="mb-1 fw-bold">{{ $curriculum->educationUnit->name }}</p>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($curriculum->edu_type == 'formal')
                        <!-- Formal Education Content -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-book me-2"></i>Curriculum Content</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->national_curriculum)
                                    <div class="mb-3">
                                        <label class="text-muted small">National Curriculum</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->national_curriculum)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->general_subjects)
                                    <div class="mb-3">
                                        <label class="text-muted small">General Subjects</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->general_subjects)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->religious_subjects)
                                    <div class="mb-3">
                                        <label class="text-muted small">Religious Subjects</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->religious_subjects)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->local_content)
                                    <div class="mb-0">
                                        <label class="text-muted small">Local Content</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->local_content)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>Teaching & Learning</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->supporting_activities)
                                    <div class="mb-3">
                                        <label class="text-muted small">Supporting Activities</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->supporting_activities)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->extracurricular)
                                    <div class="mb-3">
                                        <label class="text-muted small">Extracurricular Activities</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->extracurricular)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->special_program)
                                    <div class="mb-3">
                                        <label class="text-muted small">Special Program</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->special_program)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->learning_approach)
                                    <div class="mb-3">
                                        <label class="text-muted small">Learning Approach</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->learning_approach)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->assessment_system)
                                    <div class="mb-3">
                                        <label class="text-muted small">Assessment System</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->assessment_system)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->assessment_evaluation)
                                    <div class="mb-3">
                                        <label class="text-muted small">Assessment Evaluation</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->assessment_evaluation)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->teaching_methods)
                                    <div class="mb-3">
                                        <label class="text-muted small">Teaching Methods</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->teaching_methods)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->language_of_instruction)
                                    <div class="mb-3">
                                        <label class="text-muted small">Language of Instruction</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->language_of_instruction)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->graduation_certificates)
                                    <div class="mb-0">
                                        <label class="text-muted small">Graduation Certificates</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->graduation_certificates)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($curriculum->edu_type == 'non-formal')
                        <!-- Non-Formal Education Content -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-book me-2"></i>Program Content</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->educational_goals)
                                    <div class="mb-3">
                                        <label class="text-muted small">Educational Goals</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->educational_goals)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->fields_of_study)
                                    <div class="mb-3">
                                        <label class="text-muted small">Fields of Study</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->fields_of_study)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->class_levels)
                                    <div class="mb-3">
                                        <label class="text-muted small">Class Levels</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->class_levels)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->core_textbooks_studied)
                                    <div class="mb-0">
                                        <label class="text-muted small">Core Textbooks Studied</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->core_textbooks_studied)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>Educational Approach</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->study_schedule)
                                    <div class="mb-3">
                                        <label class="text-muted small">Study Schedule</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->study_schedule)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->teaching_methods)
                                    <div class="mb-3">
                                        <label class="text-muted small">Teaching Methods</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->teaching_methods)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->language_of_instruction)
                                    <div class="mb-3">
                                        <label class="text-muted small">Language of Instruction</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->language_of_instruction)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->graduation_certificate)
                                    <div class="mb-0">
                                        <label class="text-muted small">Graduation Certificate</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->graduation_certificate)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- English Content Tab -->
                <div class="tab-pane fade" id="english" role="tabpanel" aria-labelledby="english-tab">
                    <div class="row g-4">
                        <!-- Basic Information -->
                        <div class="col-md-12">
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="text-muted small">Title</label>
                                                <p class="mb-1 fw-bold">{{ $curriculum->title_en }}</p>
                                            </div>
                                        </div>
                                        @if($curriculum->education_unit_id && $curriculum->educationUnit)
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="text-muted small">Education Unit</label>
                                                <p class="mb-1 fw-bold">{{ $curriculum->educationUnit->name_en }}</p>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($curriculum->edu_type == 'formal')
                        <!-- Formal Education Content -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-book me-2"></i>Curriculum Content</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->national_curriculum_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">National Curriculum</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->national_curriculum_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->general_subjects_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">General Subjects</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->general_subjects_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->religious_subjects_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Religious Subjects</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->religious_subjects_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->local_content_en)
                                    <div class="mb-0">
                                        <label class="text-muted small">Local Content</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->local_content_en)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>Teaching & Learning</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->supporting_activities_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Supporting Activities</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->supporting_activities_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->extracurricular_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Extracurricular Activities</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->extracurricular_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->special_program_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Special Program</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->special_program_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->learning_approach_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Learning Approach</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->learning_approach_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->assessment_system_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Assessment System</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->assessment_system_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->assessment_evaluation_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Assessment Evaluation</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->assessment_evaluation_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->teaching_methods_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Teaching Methods</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->teaching_methods_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->language_of_instruction_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Language of Instruction</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->language_of_instruction_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->graduation_certificates_en)
                                    <div class="mb-0">
                                        <label class="text-muted small">Graduation Certificates</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->graduation_certificates_en)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($curriculum->edu_type == 'non-formal')
                        <!-- Non-Formal Education Content -->
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-book me-2"></i>Program Content</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->educational_goals_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Educational Goals</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->educational_goals_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->fields_of_study_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Fields of Study</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->fields_of_study_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->class_levels_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Class Levels</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->class_levels_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->core_textbooks_studied_en)
                                    <div class="mb-0">
                                        <label class="text-muted small">Core Textbooks Studied</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->core_textbooks_studied_en)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0"><i class="fas fa-chalkboard-teacher me-2"></i>Educational Approach</h5>
                                </div>
                                <div class="card-body">
                                    @if($curriculum->study_schedule_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Study Schedule</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->study_schedule_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->teaching_methods_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Teaching Methods</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->teaching_methods_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->language_of_instruction_en)
                                    <div class="mb-3">
                                        <label class="text-muted small">Language of Instruction</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->language_of_instruction_en)) !!}
                                        </div>
                                    </div>
                                    @endif

                                    @if($curriculum->graduation_certificate_en)
                                    <div class="mb-0">
                                        <label class="text-muted small">Graduation Certificate</label>
                                        <div class="p-2 bg-light rounded">
                                            {!! nl2br(e($curriculum->graduation_certificate_en)) !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#curriculumTabs button'))
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl)
            triggerEl.addEventListener('click', function (event) {
                event.preventDefault()
                tabTrigger.show()
            })
        })
    });
</script>
@endpush