// This script helps you create sample announcements and agenda items
document.addEventListener('DOMContentLoaded', function() {
    const createSamplesContainer = document.getElementById('create-samples-container');
    
    if (createSamplesContainer) {
        createSamplesContainer.innerHTML = `
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Create Sample Data</h5>
                </div>
                <div class="card-body">
                    <p>Click the buttons below to create sample data for testing:</p>
                    <div class="d-flex gap-2">
                        <button id="create-announcements" class="btn btn-success">Create Sample Announcements</button>
                        <button id="create-agenda" class="btn btn-info">Create Sample Agenda Items</button>
                    </div>
                    <div id="result-message" class="mt-3"></div>
                </div>
            </div>
        `;
        
        document.getElementById('create-announcements').addEventListener('click', function() {
            fetch('/create-sample-announcements')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('result-message').innerHTML = `
                        <div class="alert alert-success">
                            ${data}
                            <p>Refresh the page to see the changes.</p>
                        </div>
                    `;
                })
                .catch(error => {
                    document.getElementById('result-message').innerHTML = `
                        <div class="alert alert-danger">
                            Error: ${error.message}
                        </div>
                    `;
                });
        });
        
        document.getElementById('create-agenda').addEventListener('click', function() {
            fetch('/create-sample-agenda')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('result-message').innerHTML = `
                        <div class="alert alert-success">
                            ${data}
                            <p>Refresh the page to see the changes.</p>
                        </div>
                    `;
                })
                .catch(error => {
                    document.getElementById('result-message').innerHTML = `
                        <div class="alert alert-danger">
                            Error: ${error.message}
                        </div>
                    `;
                });
        });
    }
});
