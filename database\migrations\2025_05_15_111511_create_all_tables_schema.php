<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Migrasi ini berisi semua tabel database dan kolom terbaru.
     */
    public function up(): void
    {
        // Pastikan semua tabel dibuat dengan kondisi if (!Schema::hasTable())
        // untuk menghindari error jika tabel sudah ada
        // Tabel users
        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('username')->unique()->nullable();
                $table->string('email')->unique();
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->string('role')->default('user');
                $table->string('telegram_id')->nullable();
                $table->timestamp('last_login_at')->nullable();
                $table->rememberToken();
                $table->timestamps();
            });
        }

        // Tabel password_reset_tokens
        if (!Schema::hasTable('password_reset_tokens')) {
            Schema::create('password_reset_tokens', function (Blueprint $table) {
                $table->string('email')->primary();
                $table->string('token');
                $table->timestamp('created_at')->nullable();
            });
        }

        // Tabel sessions
        if (!Schema::hasTable('sessions')) {
            Schema::create('sessions', function (Blueprint $table) {
                $table->string('id')->primary();
                $table->foreignId('user_id')->nullable()->index();
                $table->string('ip_address', 45)->nullable();
                $table->text('user_agent')->nullable();
                $table->longText('payload');
                $table->integer('last_activity')->index();
            });
        }

        // Tabel personal_access_tokens
        if (!Schema::hasTable('personal_access_tokens')) {
            Schema::create('personal_access_tokens', function (Blueprint $table) {
                $table->id();
                $table->morphs('tokenable');
                $table->string('name');
                $table->string('token', 64)->unique();
                $table->text('abilities')->nullable();
                $table->timestamp('last_used_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
            });
        }

        // Tabel api_tokens
        if (!Schema::hasTable('api_tokens')) {
            Schema::create('api_tokens', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('token', 64)->unique();
                $table->text('abilities')->nullable();
                $table->timestamp('last_used_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
            });
        }

        // Tabel cache
        if (!Schema::hasTable('cache')) {
            Schema::create('cache', function (Blueprint $table) {
                $table->string('key')->primary();
                $table->mediumText('value');
                $table->integer('expiration');
            });
        }

        // Tabel cache_locks
        if (!Schema::hasTable('cache_locks')) {
            Schema::create('cache_locks', function (Blueprint $table) {
                $table->string('key')->primary();
                $table->string('owner');
                $table->integer('expiration');
            });
        }

        // Tabel jobs
        if (!Schema::hasTable('jobs')) {
            Schema::create('jobs', function (Blueprint $table) {
                $table->id();
                $table->string('queue')->index();
                $table->longText('payload');
                $table->unsignedTinyInteger('attempts');
                $table->unsignedInteger('reserved_at')->nullable();
                $table->unsignedInteger('available_at');
                $table->unsignedInteger('created_at');
            });
        }

        // Tabel job_batches
        if (!Schema::hasTable('job_batches')) {
            Schema::create('job_batches', function (Blueprint $table) {
                $table->string('id')->primary();
                $table->string('name');
                $table->integer('total_jobs');
                $table->integer('pending_jobs');
                $table->integer('failed_jobs');
                $table->text('failed_job_ids');
                $table->mediumText('options')->nullable();
                $table->integer('cancelled_at')->nullable();
                $table->integer('created_at');
                $table->integer('finished_at')->nullable();
            });
        }

        // Tabel failed_jobs
        if (!Schema::hasTable('failed_jobs')) {
            Schema::create('failed_jobs', function (Blueprint $table) {
                $table->id();
                $table->string('uuid')->unique();
                $table->text('connection');
                $table->text('queue');
                $table->longText('payload');
                $table->longText('exception');
                $table->timestamp('failed_at')->useCurrent();
            });
        }

        // Tabel profiles
        if (!Schema::hasTable('profiles')) {
            Schema::create('profiles', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->string('address')->nullable();
                $table->string('address_en')->nullable();
                $table->string('phone')->nullable();
                $table->string('email')->nullable();
                $table->string('website')->nullable();
                $table->string('facebook')->nullable();
                $table->string('twitter')->nullable();
                $table->string('instagram')->nullable();
                $table->string('youtube')->nullable();
                $table->string('tiktok')->nullable();
                $table->text('about')->nullable();
                $table->text('about_en')->nullable();
                $table->text('vision')->nullable();
                $table->text('vision_en')->nullable();
                $table->text('mission')->nullable();
                $table->text('mission_en')->nullable();
                $table->string('logo')->nullable();
                $table->string('favicon')->nullable();
                $table->string('image')->nullable();
                $table->timestamps();
            });
        }

        // Tabel settings
        if (!Schema::hasTable('settings')) {
            Schema::create('settings', function (Blueprint $table) {
                $table->id();
                $table->string('key')->unique();
                $table->text('value')->nullable();
                $table->string('group')->default('general');
                $table->timestamps();
            });
        }

        // Tabel news_categories
        if (!Schema::hasTable('news_categories')) {
            Schema::create('news_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->string('slug');
                $table->string('slug_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel news
        if (!Schema::hasTable('news')) {
            Schema::create('news', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->string('slug');
                $table->string('slug_en')->nullable();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->string('image')->nullable();
                $table->string('thumbnail')->nullable();
                $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();
                $table->foreignId('category_id')->nullable()->constrained('news_categories')->nullOnDelete();
                $table->boolean('is_published')->default(false);
                $table->timestamp('published_at')->nullable();
                $table->timestamps();
            });
        }

        // Tabel galleries
        if (!Schema::hasTable('galleries')) {
            Schema::create('galleries', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('image');
                $table->string('thumbnail')->nullable();
                $table->string('category')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel videos
        if (!Schema::hasTable('videos')) {
            Schema::create('videos', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('url');
                $table->string('thumbnail')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel facilities
        if (!Schema::hasTable('facilities')) {
            Schema::create('facilities', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('icon')->nullable();
                $table->string('image')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel programs
        if (!Schema::hasTable('programs')) {
            Schema::create('programs', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->text('description');
                $table->text('description_en')->nullable();
                $table->string('icon')->nullable();
                $table->string('image')->nullable();
                $table->boolean('is_featured')->default(false);
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel education_units
        if (!Schema::hasTable('education_units')) {
            Schema::create('education_units', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->string('edu_type')->nullable();
                $table->string('level')->nullable();
                $table->string('level_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->text('facilities')->nullable();
                $table->text('facilities_en')->nullable();
                $table->string('address')->nullable();
                $table->string('phone')->nullable();
                $table->string('email')->nullable();
                $table->string('website')->nullable();
                $table->string('principal_name')->nullable();
                $table->string('principal_name_en')->nullable();
                $table->text('principal_education')->nullable();
                $table->text('principal_education_en')->nullable();
                $table->text('principal_experience')->nullable();
                $table->text('principal_experience_en')->nullable();
                $table->text('principal_achievements')->nullable();
                $table->text('principal_achievements_en')->nullable();
                $table->string('principal_image')->nullable();
                $table->string('image')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel leaders
        if (!Schema::hasTable('leaders')) {
            Schema::create('leaders', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->string('position');
                $table->string('position_en')->nullable();
                $table->text('bio')->nullable();
                $table->text('bio_en')->nullable();
                $table->json('education_history')->nullable();
                $table->json('education_history_en')->nullable();
                $table->json('achievements')->nullable();
                $table->json('achievements_en')->nullable();
                $table->json('work_experience')->nullable();
                $table->json('work_experience_en')->nullable();
                $table->string('motto')->nullable();
                $table->string('motto_en')->nullable();
                $table->string('image')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel achievements
        if (!Schema::hasTable('achievements')) {
            Schema::create('achievements', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->date('achievement_date')->nullable();
                $table->string('award_by')->nullable();
                $table->string('award_by_en')->nullable();
                $table->string('image')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_featured')->default(false);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel partnerships
        if (!Schema::hasTable('partnerships')) {
            Schema::create('partnerships', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('website')->nullable();
                $table->string('logo')->nullable();
                $table->string('country_code')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel announcements
        if (!Schema::hasTable('announcements')) {
            Schema::create('announcements', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->string('image')->nullable();
                $table->string('file')->nullable();
                $table->date('start_date')->nullable();
                $table->date('end_date')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel agendas
        if (!Schema::hasTable('agendas')) {
            Schema::create('agendas', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->date('date');
                $table->time('time')->nullable();
                $table->string('location')->nullable();
                $table->string('location_en')->nullable();
                $table->string('organizer')->nullable();
                $table->string('organizer_en')->nullable();
                $table->string('image')->nullable();
                $table->boolean('is_featured')->default(false);
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel academic_calendars
        if (!Schema::hasTable('academic_calendars')) {
            Schema::create('academic_calendars', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->date('start_date');
                $table->date('end_date');
                $table->string('location')->nullable();
                $table->string('location_en')->nullable();
                $table->string('type')->nullable();
                $table->string('color')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel activity_schedules
        if (!Schema::hasTable('activity_schedules')) {
            Schema::create('activity_schedules', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('day');
                $table->time('start_time');
                $table->time('end_time');
                $table->string('location')->nullable();
                $table->string('location_en')->nullable();
                $table->string('activity_type')->nullable();
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel curricula
        if (!Schema::hasTable('curricula')) {
            Schema::create('curricula', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->string('edu_type')->default('formal'); // 'formal' or 'non-formal'
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();

                // Formal curriculum fields
                $table->text('national_curriculum')->nullable();
                $table->text('national_curriculum_en')->nullable();
                $table->text('general_subjects')->nullable();
                $table->text('general_subjects_en')->nullable();
                $table->text('religious_subjects')->nullable();
                $table->text('religious_subjects_en')->nullable();
                $table->text('local_content')->nullable();
                $table->text('local_content_en')->nullable();
                $table->text('supporting_activities')->nullable();
                $table->text('supporting_activities_en')->nullable();
                $table->text('extracurricular')->nullable();
                $table->text('extracurricular_en')->nullable();
                $table->text('special_program')->nullable();
                $table->text('special_program_en')->nullable();
                $table->text('learning_approach')->nullable();
                $table->text('learning_approach_en')->nullable();
                $table->text('assessment_system')->nullable();
                $table->text('assessment_system_en')->nullable();
                $table->text('assessment_evaluation')->nullable();
                $table->text('assessment_evaluation_en')->nullable();
                $table->text('graduation_certificates')->nullable();
                $table->text('graduation_certificates_en')->nullable();

                // Non-formal curriculum fields
                $table->text('educational_goals')->nullable();
                $table->text('educational_goals_en')->nullable();
                $table->text('core_textbooks_studied')->nullable();
                $table->text('core_textbooks_studied_en')->nullable();
                $table->text('fields_of_study')->nullable();
                $table->text('fields_of_study_en')->nullable();
                $table->text('class_levels')->nullable();
                $table->text('class_levels_en')->nullable();
                $table->text('study_schedule')->nullable();
                $table->text('study_schedule_en')->nullable();
                $table->text('teaching_methods')->nullable();
                $table->text('teaching_methods_en')->nullable();
                $table->text('language_of_instruction')->nullable();
                $table->text('language_of_instruction_en')->nullable();

                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel menu_items
        if (!Schema::hasTable('menu_items')) {
            Schema::create('menu_items', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->string('url')->nullable();
                $table->string('route_name')->nullable();
                $table->string('target')->default('_self');
                $table->foreignId('parent_id')->nullable()->constrained('menu_items')->nullOnDelete();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel contacts
        if (!Schema::hasTable('contacts')) {
            Schema::create('contacts', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email');
                $table->string('phone')->nullable();
                $table->string('subject');
                $table->text('message');
                $table->text('reply_message')->nullable();
                $table->unsignedBigInteger('replied_by')->nullable();
                $table->timestamp('replied_at')->nullable();
                $table->boolean('is_read')->default(false);
                $table->timestamps();

                $table->foreign('replied_by')->references('id')->on('users')->onDelete('set null');
            });
        }

        // Tabel registrations
        if (!Schema::hasTable('registrations')) {
            Schema::create('registrations', function (Blueprint $table) {
                $table->id();
                $table->string('nik', 16); // NIK (16 digit)
                $table->string('full_name');
                $table->string('gender');
                $table->string('place_of_birth');
                $table->date('date_of_birth');
                $table->string('parent_name');
                $table->string('parent_occupation')->nullable();
                $table->string('father_name')->nullable();
                $table->string('father_occupation')->nullable();
                $table->string('mother_name')->nullable();
                $table->string('mother_occupation')->nullable();
                $table->string('address');
                $table->string('phone');
                $table->string('email')->nullable();
                $table->string('nisn', 10)->nullable(); // NISN (10 digit)
                $table->string('last_education');
                $table->string('previous_school')->nullable();
                $table->integer('graduation_year')->nullable();
                $table->text('notes')->nullable();
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();
                $table->text('reason')->nullable();
                $table->text('hobby')->nullable();
                $table->text('ambition')->nullable();
                $table->string('status')->default('pending'); // pending, approved, rejected
                $table->string('registration_number')->nullable();
                $table->timestamp('approved_at')->nullable();
                $table->timestamp('rejected_at')->nullable();
                $table->timestamps();
            });
        }

        // Tabel registration_schedules
        if (!Schema::hasTable('registration_schedules')) {
            Schema::create('registration_schedules', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->date('start_date');
                $table->date('end_date');
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel registration_requirements
        if (!Schema::hasTable('registration_requirements')) {
            Schema::create('registration_requirements', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel registration_fees
        if (!Schema::hasTable('registration_fees')) {
            Schema::create('registration_fees', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->decimal('amount', 15, 2)->nullable();
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel registration_infos
        if (!Schema::hasTable('registration_infos')) {
            Schema::create('registration_infos', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->foreignId('education_unit_id')->nullable()->constrained('education_units')->nullOnDelete();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel directors
        if (!Schema::hasTable('directors')) {
            Schema::create('directors', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->string('position');
                $table->string('position_en')->nullable();
                $table->text('bio')->nullable();
                $table->text('bio_en')->nullable();
                $table->string('image')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel pages
        if (!Schema::hasTable('pages')) {
            Schema::create('pages', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->string('slug')->unique();
                $table->string('slug_en')->nullable()->unique();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->string('image')->nullable();
                $table->string('meta_title')->nullable();
                $table->string('meta_description')->nullable();
                $table->string('meta_keywords')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel insights
        if (!Schema::hasTable('insights')) {
            Schema::create('insights', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('content');
                $table->text('content_en')->nullable();
                $table->string('image')->nullable();
                $table->date('published_at')->nullable();
                $table->boolean('is_featured')->default(false);
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel swipers
        if (!Schema::hasTable('swipers')) {
            Schema::create('swipers', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('image');
                $table->string('url')->nullable();
                $table->string('button_text')->nullable();
                $table->string('button_text_en')->nullable();
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel timelines
        if (!Schema::hasTable('timelines')) {
            Schema::create('timelines', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('content')->nullable();
                $table->text('content_en')->nullable();
                $table->string('image')->nullable();
                $table->integer('year');
                $table->integer('order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel webhooks
        if (!Schema::hasTable('webhooks')) {
            Schema::create('webhooks', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('url');
                $table->string('event');
                $table->string('secret')->nullable();
                $table->string('api_key', 64)->nullable();
                $table->boolean('is_active')->default(true);
                $table->json('headers')->nullable();
                $table->json('payload_template')->nullable();
                $table->integer('retry_count')->default(0);
                $table->timestamp('last_triggered_at')->nullable();
                $table->timestamp('last_failed_at')->nullable();
                $table->text('last_error')->nullable();
                $table->timestamps();
            });
        }

        // Tabel artwork_categories / lit_type
        if (!Schema::hasTable('lit_type')) {
            Schema::create('lit_type', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('name_en')->nullable();
                $table->string('slug')->unique();
                $table->string('slug_en')->nullable()->unique();
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // Tabel literature
        if (!Schema::hasTable('literature')) {
            Schema::create('literature', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en')->nullable();
                $table->text('content')->nullable();
                $table->text('content_en')->nullable();
                $table->string('author')->nullable();
                $table->string('author_en')->nullable();
                $table->integer('year')->nullable();
                $table->string('type')->nullable();
                $table->string('type_en')->nullable();
                $table->string('image')->nullable();
                $table->string('file')->nullable();
                $table->foreignId('category_id')->nullable();
                $table->boolean('is_active')->default(true);
                $table->integer('view_count')->default(0);
                $table->timestamps();
            });
        }

        // Tabel paints
        if (!Schema::hasTable('paints')) {
            Schema::create('paints', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->string('title_en');
                $table->text('description')->nullable();
                $table->text('description_en')->nullable();
                $table->string('image');
                $table->string('artist')->nullable();
                $table->string('artist_en')->nullable();
                $table->integer('year')->nullable();
                $table->string('medium')->nullable();
                $table->string('medium_en')->nullable();
                $table->string('dimensions')->nullable();
                $table->boolean('is_active')->default(true);
                $table->integer('view_count')->default(0);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * Catatan: Metode down() ini tidak diimplementasikan untuk alasan keamanan
     * karena dapat menghapus semua data dalam database.
     * Jika Anda perlu mengembalikan migrasi ini, sebaiknya gunakan backup database.
     */
    public function down(): void
    {
        // Tidak diimplementasikan untuk alasan keamanan
        // Jika benar-benar diperlukan, hapus komentar kode di bawah ini
        /*
        // Hapus tabel dalam urutan terbalik untuk menghindari kendala foreign key
        Schema::dropIfExists('paints');
        Schema::dropIfExists('literature');
        Schema::dropIfExists('lit_type');
        Schema::dropIfExists('webhooks');
        Schema::dropIfExists('timelines');
        Schema::dropIfExists('swipers');
        Schema::dropIfExists('insights');
        Schema::dropIfExists('pages');
        Schema::dropIfExists('directors');
        Schema::dropIfExists('registration_infos');
        Schema::dropIfExists('registration_fees');
        Schema::dropIfExists('registration_requirements');
        Schema::dropIfExists('registration_schedules');
        Schema::dropIfExists('registrations');
        Schema::dropIfExists('contacts');
        Schema::dropIfExists('menu_items');
        Schema::dropIfExists('curricula');
        Schema::dropIfExists('activity_schedules');
        Schema::dropIfExists('academic_calendars');
        Schema::dropIfExists('agendas');
        Schema::dropIfExists('announcements');
        Schema::dropIfExists('partnerships');
        Schema::dropIfExists('achievements');
        Schema::dropIfExists('leaders');
        Schema::dropIfExists('education_units');
        Schema::dropIfExists('programs');
        Schema::dropIfExists('facilities');
        Schema::dropIfExists('videos');
        Schema::dropIfExists('galleries');
        Schema::dropIfExists('news');
        Schema::dropIfExists('news_categories');
        Schema::dropIfExists('settings');
        Schema::dropIfExists('profiles');
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('cache_locks');
        Schema::dropIfExists('cache');
        Schema::dropIfExists('api_tokens');
        Schema::dropIfExists('personal_access_tokens');
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('users');
        */
    }
};
