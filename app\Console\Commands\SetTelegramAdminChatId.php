<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class SetTelegramAdminChatId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:set-admin-chat-id {chat_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set the Telegram admin personal chat ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chatId = $this->argument('chat_id');
        
        // Validate chat ID
        if (empty($chatId)) {
            $this->error('Chat ID cannot be empty.');
            return 1;
        }
        
        // Check if it's a group chat ID (starts with -)
        if (strpos($chatId, '-') === 0) {
            if (!$this->confirm('This appears to be a group chat ID (starts with -). Admin chat ID should be a personal chat ID. Continue anyway?', false)) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }
        
        // Update the setting
        Setting::updateOrCreate(
            ['key' => 'telegram_admin_chat_id'],
            ['value' => $chatId, 'group' => 'telegram']
        );
        
        $this->info('Admin personal chat ID set to: ' . $chatId);
        
        // Show current configuration
        $adminPersonalChatId = Setting::getValue('telegram_admin_chat_id');
        $groupChatId = Setting::getValue('telegram_chat_id');
        $topicId = Setting::getValue('telegram_group_topic_id');
        
        $this->info('Current Telegram Configuration:');
        $this->info('Admin Personal Chat ID: ' . $adminPersonalChatId);
        $this->info('Group Chat ID: ' . ($groupChatId ?: 'Not configured'));
        $this->info('Group Topic ID: ' . ($topicId ?: 'Not configured'));
        
        return 0;
    }
}
