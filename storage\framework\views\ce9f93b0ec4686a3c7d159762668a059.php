<?php if (isset($component)) { $__componentOriginalaa758e6a82983efcbf593f765e026bd9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa758e6a82983efcbf593f765e026bd9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::message'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
# <?php echo e(__('emails.islamic_greeting')); ?>


<?php echo e(__('emails.contact_thank_you')); ?>


<?php echo e(__('emails.contact_received')); ?>


<div style="background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3 style="color: #0056b3; margin-top: 0;"><?php echo e(__('emails.contact_details')); ?>:</h3>
    <table style="width: 100%; border-collapse: collapse; border: none; margin-bottom: 15px;">
        <tr>
            <td style="padding: 8px; width: 30%; border-bottom: 1px solid #dee2e6;"><strong><?php echo e(__('emails.contact_subject')); ?>:</strong></td>
            <td style="padding: 8px; border-bottom: 1px solid #dee2e6;"><?php echo e($subject); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px; width: 30%;"><strong><?php echo e(__('emails.contact_date')); ?>:</strong></td>
            <td style="padding: 8px;"><?php echo e($date); ?></td>
        </tr>
    </table>
</div>

<p><?php echo e(__('emails.contact_response_time')); ?></p>

<div style="background-color: #f0f7ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3 style="color: #0056b3; margin-top: 0;"><?php echo e(__('emails.contact_further_info')); ?>:</h3>
    <p style="margin: 5px 0;"><strong>Alamat:</strong> <?php echo e($address); ?></p>
    <p style="margin: 5px 0;"><strong>Telepon:</strong> <?php echo e($phone); ?></p>
    <p style="margin: 5px 0;"><strong>Email:</strong> <a href="mailto:<?php echo e($email); ?>" style="color: #0056b3;"><?php echo e($email); ?></a></p>
</div>

<div style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6c757d; margin: 15px 0; border-radius: 4px;">
    <strong><?php echo e(__('emails.auto_email_notice')); ?></strong>
</div>

<p><?php echo e(__('emails.islamic_closing')); ?></p>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaa758e6a82983efcbf593f765e026bd9)): ?>
<?php $attributes = $__attributesOriginalaa758e6a82983efcbf593f765e026bd9; ?>
<?php unset($__attributesOriginalaa758e6a82983efcbf593f765e026bd9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaa758e6a82983efcbf593f765e026bd9)): ?>
<?php $component = $__componentOriginalaa758e6a82983efcbf593f765e026bd9; ?>
<?php unset($__componentOriginalaa758e6a82983efcbf593f765e026bd9); ?>
<?php endif; ?>
<?php /**PATH /home/<USER>/laravel/resources/views/emails/contact-auto-reply.blade.php ENDPATH**/ ?>