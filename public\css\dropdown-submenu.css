/* Dropdown submenu styles */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
    border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover > .dropdown-menu {
    display: block;
}

.dropdown-submenu > a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #ccc;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover > a:after {
    border-left-color: #fff;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left > .dropdown-menu {
    left: -100%;
    margin-left: 10px;
    border-radius: 6px 0 6px 6px;
}

/* Mobile view adjustments */
@media (max-width: 991.98px) {
    .dropdown-submenu > .dropdown-menu {
        left: 0;
        margin-left: 15px;
        border-radius: 0;
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
        display: none;
    }

    .dropdown-submenu > a:after {
        transform: rotate(90deg);
        margin-top: 8px;
    }

    .dropdown-submenu.show > .dropdown-menu {
        display: block;
    }

    .dropdown-item {
        white-space: normal;
    }

    /* Indent submenu items */
    .dropdown-menu .dropdown-menu .dropdown-item {
        padding-left: 2rem;
    }

    /* Indent sub-submenu items */
    .dropdown-menu .dropdown-menu .dropdown-menu .dropdown-item {
        padding-left: 3rem;
    }
}
