@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en)

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('announcements') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($announcement->title, 30) : \Illuminate\Support\Str::limit($announcement->title_en, 30) }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Announcement Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 main-content">
                    <div class="card shadow-sm mb-4" data-aos="fade-up">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="announcement-date">
                                    <i class="far fa-calendar-alt me-1"></i>
                                    {{ $announcement->start_date->format('d M Y') }} - {{ $announcement->end_date->format('d M Y') }}
                                </div>
                            </div>

                            @if($announcement->image)
                                <div class="text-center mb-4">
                                    <img src="{{ asset('storage/' . $announcement->image) }}" alt="{{ app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en }}" class="img-fluid rounded">
                                </div>
                            @endif

                            <div class="announcement-content">
                                @php
                                    $content = app()->getLocale() == 'id' ? $announcement->content : $announcement->content_en;
                                    // Proses HTML entity dengan benar
                                    $content = html_entity_decode($content);
                                @endphp
                                {!! $content !!}
                            </div>

                            @if($announcement->file)
                            <div class="announcement-file mt-4">
                                <div class="card bg-light">
                                    <div class="card-body d-flex align-items-center">
                                        <i class="fas fa-file-pdf text-danger fa-2x me-3"></i>
                                        <div>
                                            <h5 class="mb-1">{{ app()->getLocale() == 'id' ? 'Lampiran Dokumen' : 'Document Attachment' }}</h5>
                                            <a href="{{ asset('storage/' . $announcement->file) }}" target="_blank" class="btn btn-sm btn-primary">
                                                <i class="fas fa-download me-1"></i> {{ app()->getLocale() == 'id' ? 'Unduh PDF' : 'Download PDF' }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <div class="mt-4">
                                <a href="{{ route('announcements') }}" class="btn btn-outline-success">
                                    <i class="fas fa-arrow-left me-2"></i>{{ app()->getLocale() == 'id' ? 'Kembali ke Daftar Pengumuman' : 'Back to Announcements' }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                    <!-- Related Announcements -->
                    <div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Pengumuman Terkait' : 'Related Announcements' }}</h5>
                        </div>
                        <div class="card-body">
                            @forelse($relatedAnnouncements as $related)
                                <div class="related-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                    <div class="small text-danger mb-1">
                                        <i class="far fa-calendar-alt me-1"></i>
                                        {{ $related->start_date->format('d M Y') }} - {{ $related->end_date->format('d M Y') }}
                                    </div>
                                    <h6 class="mb-0">
                                        <a href="{{ route('announcements.show', $related->id) }}" class="text-decoration-none">
                                            {{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($related->title, 70) : \Illuminate\Support\Str::limit($related->title_en, 70) }}
                                        </a>
                                    </h6>
                                </div>
                            @empty
                                <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Tidak ada pengumuman terkait.' : 'No related announcements.' }}</p>
                            @endforelse
                        </div>
                    </div>

                    <!-- Latest Agenda -->
                    @include('public.partials.latest-agenda')

                    <!-- Latest News -->
                    @include('public.partials.latest-news')

                    <!-- CTA -->
                    <div class="card border-0 bg-success text-white shadow-sm" data-aos="fade-up">
                        <div class="card-body p-4 text-center">
                            <h5 class="mb-3">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</h5>
                            <p>{{ app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family.' }}</p>
                            <a href="{{ route('registration') }}" class="btn btn-light">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@push('scripts')
    <script src="{{ asset('js/sticky-sidebar.js') }}"></script>
@endpush
@endsection
