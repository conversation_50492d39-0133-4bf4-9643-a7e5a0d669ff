<?php

namespace App\Http\Controllers\Api;

use App\Models\Program;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class ProgramController extends BaseApiController
{
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        parent::__construct($webhookService);
        $this->model = new Program();
        $this->modelName = 'program';
        
        $this->storeRules = [
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'required|string',
            'description_en' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
            'is_featured' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ];
        
        $this->updateRules = [
            'name' => 'sometimes|required|string|max:255',
            'name_en' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'description_en' => 'sometimes|required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
            'is_featured' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Display a listing of the programs.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $programs = Program::active()->get();
        
        return response()->json([
            'success' => true,
            'data' => $programs,
        ]);
    }

    /**
     * Display the specified program.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $program = Program::where('is_active', true)
            ->findOrFail($id);
        
        return response()->json([
            'success' => true,
            'data' => $program,
        ]);
    }

    /**
     * Store a newly created program in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), $this->storeRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Program::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = $data['is_active'] ?? true;
        $data['is_featured'] = $data['is_featured'] ?? false;

        // Handle image upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('programs', $fileName, 'public');
            $data['image'] = $filePath;
        }

        $program = Program::create($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('program.created', $program->toArray());

        return response()->json([
            'success' => true,
            'message' => 'Program created successfully',
            'data' => $program,
        ], 201);
    }

    /**
     * Update the specified program in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $program = Program::findOrFail($id);

        $validator = Validator::make($request->all(), $this->updateRules);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->all();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($program->image) {
                Storage::disk('public')->delete($program->image);
            }

            // Upload new image
            $file = $request->file('image');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('programs', $fileName, 'public');
            $data['image'] = $filePath;
        }

        $program->update($data);

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('program.updated', $program->toArray());

        return response()->json([
            'success' => true,
            'message' => 'Program updated successfully',
            'data' => $program,
        ]);
    }

    /**
     * Remove the specified program from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $program = Program::findOrFail($id);
        
        // Delete image if exists
        if ($program->image) {
            Storage::disk('public')->delete($program->image);
        }
        
        $program->delete();

        // Dispatch webhook event
        $this->webhookService->dispatchEvent('program.deleted', ['id' => $id]);

        return response()->json([
            'success' => true,
            'message' => 'Program deleted successfully',
        ]);
    }
}
