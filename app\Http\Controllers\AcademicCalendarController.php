<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AcademicCalendar;

class AcademicCalendarController extends Controller
{
    /**
     * Display a listing of the academic calendars.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get all active academic calendars
        $calendars = AcademicCalendar::active()->get();
        
        // Group calendars by month for better organization
        $calendarsByMonth = $calendars->groupBy(function($calendar) {
            return $calendar->start_date->format('Y-m');
        });
        
        // Get unique calendar types for filtering
        $calendarTypes = $calendars->pluck('type')->unique()->filter()->values();
        
        return view('public.calendars.index', compact('calendars', 'calendarsByMonth', 'calendarTypes'));
    }

    /**
     * Display the specified academic calendar.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $calendar = AcademicCalendar::where('is_active', true)
            ->findOrFail($id);
            
        // Get related calendars (same type or same month)
        $relatedCalendars = AcademicCalendar::where('is_active', true)
            ->where('id', '!=', $calendar->id)
            ->where(function($query) use ($calendar) {
                $query->where('type', $calendar->type)
                    ->orWhereBetween('start_date', [
                        $calendar->start_date->startOfMonth(),
                        $calendar->start_date->endOfMonth()
                    ]);
            })
            ->orderBy('start_date', 'asc')
            ->take(3)
            ->get();
            
        return view('public.calendars.show', compact('calendar', 'relatedCalendars'));
    }
}
