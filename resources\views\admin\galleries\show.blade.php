@extends('admin.layouts.app')

@section('title', 'View Gallery Item')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Gallery Item</h1>
        <div>
            <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.galleries.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Gallery Item Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID:</div>
                        <div class="col-md-9">{{ $gallery->id }}</div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Category:</div>
                        <div class="col-md-9">
                            <span class="badge bg-primary">{{ $gallery->category }}</span>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At:</div>
                        <div class="col-md-9">{{ $gallery->created_at->format('d M Y H:i:s') }}</div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Updated At:</div>
                        <div class="col-md-9">{{ $gallery->updated_at->format('d M Y H:i:s') }}</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Indonesian Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Title:</div>
                                <div class="col-md-9">{{ $gallery->title }}</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3 fw-bold">Description:</div>
                                <div class="col-md-9">
                                    @if($gallery->description)
                                        {!! nl2br(e($gallery->description)) !!}
                                    @else
                                        <span class="text-muted">No description available</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">English Content</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 fw-bold">Title:</div>
                                <div class="col-md-9">{{ $gallery->title_en }}</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-3 fw-bold">Description:</div>
                                <div class="col-md-9">
                                    @if($gallery->description_en)
                                        {!! nl2br(e($gallery->description_en)) !!}
                                    @else
                                        <span class="text-muted">No description available</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body">
                    @if($gallery->image)
                        <img src="{{ asset('storage/' . $gallery->image) }}" alt="{{ $gallery->title }}" class="img-fluid rounded">
                    @else
                        <div class="text-center py-5 bg-light rounded">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="mt-3 text-muted">No image available</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('gallery') }}" class="btn btn-info w-100 mb-2" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i> View on Website
                    </a>
                    
                    <a href="{{ route('admin.galleries.edit', $gallery) }}" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-edit me-1"></i> Edit Gallery Item
                    </a>
                    
                    <form action="{{ route('admin.galleries.destroy', $gallery) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this gallery item?')">
                            <i class="fas fa-trash me-1"></i> Delete Gallery Item
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
