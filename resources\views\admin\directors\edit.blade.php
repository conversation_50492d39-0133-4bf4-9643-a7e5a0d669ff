@extends('admin.layouts.app')

@section('title', 'Edit Director')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Director</h1>
        <a href="{{ route('admin.directors-insight.index', ['tab' => 'director']) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Directors
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.directors.update', $director) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $director->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <input type="hidden" id="name_en" name="name_en" value="{{ old('name_en', $director->name_en ?? $director->name) }}">
                        <small class="text-muted">Name will be displayed the same in both Indonesian and English.</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="position" class="form-label">Position</label>
                        <input type="text" class="form-control @error('position') is-invalid @enderror" id="position" name="position" value="{{ old('position', $director->position) }}" required>
                        @error('position')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="position_en" class="form-label">Position (English)</label>
                        <input type="text" class="form-control @error('position_en') is-invalid @enderror" id="position_en" name="position_en" value="{{ old('position_en', $director->position_en) }}">
                        @error('position_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="bio" class="form-label">Bio <small class="text-muted">(max 100 words)</small></label>
                        <textarea class="form-control @error('bio') is-invalid @enderror" id="bio" name="bio" rows="5">{{ old('bio', $director->bio) }}</textarea>
                        <small class="form-text text-muted">Please keep your bio concise, maximum 100 words.</small>
                        @error('bio')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="bio_en" class="form-label">Bio (English) <small class="text-muted">(max 100 words)</small></label>
                        <textarea class="form-control @error('bio_en') is-invalid @enderror" id="bio_en" name="bio_en" rows="5">{{ old('bio_en', $director->bio_en) }}</textarea>
                        <small class="form-text text-muted">Please keep your bio concise, maximum 100 words.</small>
                        @error('bio_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="image" class="form-label">Image</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image">
                        <small class="form-text text-muted">Recommended size: 300x300 pixels. Leave empty to keep current image.</small>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="order" class="form-label">Display Order</label>
                        <input type="number" class="form-control @error('order') is-invalid @enderror" id="order" name="order" value="{{ old('order', $director->order) }}" min="0">
                        @error('order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="mt-3">
                            @if($director->image)
                                <img id="image-preview" src="{{ asset('storage/' . $director->image) }}" alt="{{ $director->name }}" class="img-thumbnail" style="max-width: 300px; max-height: 300px;">
                            @else
                                <img id="image-preview" src="#" alt="Preview" class="img-thumbnail d-none" style="max-width: 300px; max-height: 300px;">
                            @endif
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Director
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Image preview
    document.getElementById('image').addEventListener('change', function(e) {
        const preview = document.getElementById('image-preview');
        const file = e.target.files[0];

        if (file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.classList.remove('d-none');
            }

            reader.readAsDataURL(file);
        } else {
            // If no new file is selected, keep the current image if it exists
            @if($director->image)
                preview.src = "{{ asset('storage/' . $director->image) }}";
                preview.classList.remove('d-none');
            @else
                preview.src = '#';
                preview.classList.add('d-none');
            @endif
        }
    });

    // Sync name to English version
    document.getElementById('name').addEventListener('input', function() {
        document.getElementById('name_en').value = this.value;
    });

    // Rich text editors are now handled by Summernote
    // Add summernote class to all rich text fields
    const textareaIds = ['bio', 'bio_en'];

    textareaIds.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.classList.add('summernote');
        }
    });

    // Initialize word counters after Summernote is loaded
    setTimeout(function() {
        // Trigger initial word count update
        $('#bio, #bio_en').each(function() {
            var text = $(this).summernote('code');
            var plainText = $('<div>').html(text).text();
            var words = plainText.trim().split(/\s+/);
            var wordCount = plainText.trim() === '' ? 0 : words.length;

            // Find or create the counter element
            var $counter = $(this).siblings('.word-counter');
            if ($counter.length === 0) {
                $counter = $('<div class="word-counter mt-1 text-muted small"></div>');
                $(this).after($counter);
            }

            // Update counter text and style
            $counter.text('Word count: ' + wordCount + ' / 100');

            // Add warning style if approaching limit
            if (wordCount > 90) {
                $counter.removeClass('text-muted').addClass('text-warning');
            } else if (wordCount >= 100) {
                $counter.removeClass('text-muted text-warning').addClass('text-danger');
            } else {
                $counter.removeClass('text-warning text-danger').addClass('text-muted');
            }
        });
    }, 1000);
</script>
@endpush
