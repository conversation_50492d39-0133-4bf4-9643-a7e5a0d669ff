<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Services\ImageService;

class ProgramController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the programs.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $programs = \App\Models\Program::all();
        return view('admin.programs.index', compact('programs'));
    }

    /**
     * Show the form for creating a new program.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.programs.create');
    }

    /**
     * Store a newly created program in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'required|string',
            'description_en' => 'required|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'is_featured' => 'boolean',
        ]);

        $data = $request->all();

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'programs',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        // Set is_featured to false if not provided
        $data['is_featured'] = $request->has('is_featured') ? true : false;

        \App\Models\Program::create($data);

        return redirect()->route('admin.programs.index')
            ->with('success', 'Program created successfully.');
    }

    /**
     * Display the specified program.
     *
     * @param  \App\Models\Program  $program
     * @return \Illuminate\View\View
     */
    public function show(\App\Models\Program $program)
    {
        return view('admin.programs.show', compact('program'));
    }

    /**
     * Show the form for editing the specified program.
     *
     * @param  \App\Models\Program  $program
     * @return \Illuminate\View\View
     */
    public function edit(\App\Models\Program $program)
    {
        return view('admin.programs.edit', compact('program'));
    }

    /**
     * Update the specified program in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Program  $program
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, \App\Models\Program $program)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'required|string',
            'description_en' => 'required|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'is_featured' => 'boolean',
        ]);

        $data = $request->all();

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($program->image) {
                $this->imageService->deleteImage($program->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'programs',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        // Set is_featured to false if not provided
        $data['is_featured'] = $request->has('is_featured') ? true : false;

        $program->update($data);

        return redirect()->route('admin.programs.index')
            ->with('success', 'Program updated successfully.');
    }

    /**
     * Remove the specified program from storage.
     *
     * @param  \App\Models\Program  $program
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(\App\Models\Program $program)
    {
        // Delete associated image
        if ($program->image) {
            $this->imageService->deleteImage($program->image);
        }

        $program->delete();

        return redirect()->route('admin.programs.index')
            ->with('success', 'Program deleted successfully.');
    }
}
