/* Custom CSS for Hero Swiper */

/* Make hero section take full viewport height */
.hero-section {
    height: 80vh;
    min-height: 500px;
    max-height: 800px;
    padding: 0;
    /* Changed from overflow: hidden to visible to prevent navbar cutoff */
    overflow: visible;
    position: relative;
}

/* Make swiper container fill the hero section */
.hero-section .swiper {
    width: 100%;
    height: 100%;
    /* Ensure content doesn't overlap with navbar */
    position: relative;
    z-index: 1;
}

/* Style for swiper slides */
.hero-section .swiper-slide {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden; /* Keep overflow hidden for slides */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Style for the image inside swiper slides */
.hero-section .swiper-slide img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    z-index: 0;
}

/* Overlay for text readability */
.hero-section .swiper-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

/* Position the content */
.hero-section .hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1200px;
    padding: 0 20px;
    text-align: center;
    color: #fff;
}

/* Style for formatted swiper description */
.hero-section .swiper-description {
    margin: 1rem auto;
    max-width: 800px;
    line-height: 1.6;
    font-size: 1.1rem;
}

.hero-section .swiper-description {
    white-space: pre-line; /* Preserves line breaks in plain text */
}

/* Style for swiper navigation buttons */
.hero-section .swiper-button-next,
.hero-section .swiper-button-prev {
    color: #fff;
    background: rgba(0, 0, 0, 0.3);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.hero-section .swiper-button-next:hover,
.hero-section .swiper-button-prev:hover {
    background: rgba(0, 0, 0, 0.6);
}

.hero-section .swiper-button-next:after,
.hero-section .swiper-button-prev:after {
    font-size: 18px;
}

/* Style for swiper pagination */
.hero-section .swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.7);
    opacity: 0.7;
}

.hero-section .swiper-pagination-bullet-active {
    background: #fff;
    opacity: 1;
}

/* Fade effect styles */
.hero-section .swiper-slide {
    opacity: 0 !important;
    transition: opacity 0.3s ease;
}

.hero-section .swiper-slide-active {
    opacity: 1 !important;
}

/* Fix for blank slides during transition */
.hero-section .swiper-slide-duplicate-active {
    opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .hero-section {
        height: 70vh;
    }

    .hero-content h1 {
        font-size: 36px;
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        height: 60vh;
        min-height: 400px;
    }

    .hero-content h1 {
        font-size: 28px;
    }

    .hero-content p {
        font-size: 16px;
    }
}
