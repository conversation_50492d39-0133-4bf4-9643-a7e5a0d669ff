<?php $__env->startSection('title', 'User Management'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid px-4">
        <h1 class="mt-4">User Management</h1>
        <ol class="breadcrumb mb-4">
            <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Settings</li>
            <li class="breadcrumb-item active">Users</li>
        </ol>

        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-users me-1"></i>
                    User Management
                </div>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-plus me-1"></i> Add New User
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($user->name); ?></td>
                                <td><?php echo e($user->username ?? 'N/A'); ?></td>
                                <td><?php echo e($user->email); ?></td>
                                <td>
                                    <span class="badge bg-primary"><?php echo e(ucfirst($user->role ?? 'Admin')); ?></span>
                                </td>
                                <td><?php echo e($user->created_at->format('d M Y')); ?></td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editUserModal<?php echo e($user->id); ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal<?php echo e($user->id); ?>" title="Reset Password">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    <?php if(auth()->id() != $user->id): ?>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteUserModal<?php echo e($user->id); ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button type="button" class="btn btn-sm btn-danger" disabled title="You cannot delete your own account">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php if(empty($users) || count($users) === 0): ?>
                            <tr>
                                <td colspan="5" class="text-center">No users found.</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="<?php echo e(route('admin.settings.users.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3 alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            A random password will be generated and sent to the user's email address.
                        </div>
                        <div class="mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="admin">Admin</option>
                                <option value="operator">Operator</option>
                                <option value="editor">Editor</option>
                            </select>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Add User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modals -->
    <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="modal fade" id="editUserModal<?php echo e($user->id); ?>" tabindex="-1" aria-labelledby="editUserModalLabel<?php echo e($user->id); ?>" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="<?php echo e(route('admin.settings.users.update', $user->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="editUserModalLabel<?php echo e($user->id); ?>">Edit User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_name<?php echo e($user->id); ?>" class="form-label">Name</label>
                            <input type="text" class="form-control" id="edit_name<?php echo e($user->id); ?>" name="name" value="<?php echo e($user->name); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_username<?php echo e($user->id); ?>" class="form-label">Username</label>
                            <input type="text" class="form-control" id="edit_username<?php echo e($user->id); ?>" name="username" value="<?php echo e($user->username); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_email<?php echo e($user->id); ?>" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email<?php echo e($user->id); ?>" name="email" value="<?php echo e($user->email); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_role<?php echo e($user->id); ?>" class="form-label">Role</label>
                            <select class="form-select" id="edit_role<?php echo e($user->id); ?>" name="role">
                                <option value="admin" <?php echo e(($user->role ?? 'admin') == 'admin' ? 'selected' : ''); ?>>Admin</option>
                                <option value="operator" <?php echo e(($user->role ?? 'admin') == 'operator' ? 'selected' : ''); ?>>Operator</option>
                                <option value="editor" <?php echo e(($user->role ?? 'admin') == 'editor' ? 'selected' : ''); ?>>Editor</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="edit_telegram_id<?php echo e($user->id); ?>" class="form-label">Telegram ID</label>
                            <input type="text" class="form-control" id="edit_telegram_id<?php echo e($user->id); ?>" name="telegram_id" value="<?php echo e($user->telegram_id); ?>">
                            <div class="form-text">ID Telegram untuk integrasi notifikasi. Biarkan kosong jika tidak digunakan.</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div class="modal fade" id="deleteUserModal<?php echo e($user->id); ?>" tabindex="-1" aria-labelledby="deleteUserModalLabel<?php echo e($user->id); ?>" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="<?php echo e(route('admin.settings.users.destroy', $user->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteUserModalLabel<?php echo e($user->id); ?>">Delete User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete the user <strong><?php echo e($user->name); ?></strong>?</p>
                        <p class="text-danger">This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="changePasswordModal<?php echo e($user->id); ?>" tabindex="-1" aria-labelledby="changePasswordModalLabel<?php echo e($user->id); ?>" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="<?php echo e(route('admin.settings.users.change-password', $user->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="changePasswordModalLabel<?php echo e($user->id); ?>">Reset Password for <?php echo e($user->name); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            A new random password will be generated and sent to the user's email address (<?php echo e($user->email); ?>).
                        </div>
                        <p>Are you sure you want to reset the password for this user?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Reset Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/settings/users/index.blade.php ENDPATH**/ ?>