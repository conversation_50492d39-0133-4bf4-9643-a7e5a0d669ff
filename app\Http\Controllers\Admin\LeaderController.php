<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Leader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Services\ImageService;

class LeaderController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $leaders = Leader::orderBy('order', 'asc')->get();
        return view('admin.leaders.index', compact('leaders'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.leaders.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'position' => 'required|string|max:255',
            'position_en' => 'nullable|string|max:255',
            'bio' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'bio_en' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio_en field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio (English) must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'education_history' => 'nullable|array',
            'education_history_en' => 'nullable|array',
            'achievements' => 'nullable|array',
            'achievements_en' => 'nullable|array',
            'work_experience' => 'nullable|array',
            'work_experience_en' => 'nullable|array',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'social_facebook' => 'nullable|string|max:255',
            'social_twitter' => 'nullable|string|max:255',
            'social_instagram' => 'nullable|string|max:255',
            'social_linkedin' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->except('image');

        // Process JSON fields and ensure shared fields have the same values
        foreach (['education_history', 'education_history_en', 'achievements', 'achievements_en', 'work_experience', 'work_experience_en'] as $field) {
            if (isset($data[$field]) && is_array($data[$field])) {
                // Special handling for fields that should be the same in both languages
                if ($field === 'education_history_en' && isset($data['education_history'])) {
                    foreach ($data[$field] as $key => $item) {
                        if (isset($data['education_history'][$key])) {
                            // Copy year and location from Indonesian to English
                            $data[$field][$key]['year'] = $data['education_history'][$key]['year'] ?? '';
                            $data[$field][$key]['location'] = $data['education_history'][$key]['location'] ?? '';
                        }
                    }
                } else if ($field === 'achievements_en' && isset($data['achievements'])) {
                    foreach ($data[$field] as $key => $item) {
                        if (isset($data['achievements'][$key])) {
                            // Copy year from Indonesian to English
                            $data[$field][$key]['year'] = $data['achievements'][$key]['year'] ?? '';
                        }
                    }
                } else if ($field === 'work_experience_en' && isset($data['work_experience'])) {
                    foreach ($data[$field] as $key => $item) {
                        if (isset($data['work_experience'][$key])) {
                            // Copy period and location from Indonesian to English
                            $data[$field][$key]['period'] = $data['work_experience'][$key]['period'] ?? '';
                            $data[$field][$key]['location'] = $data['work_experience'][$key]['location'] ?? '';
                        }
                    }
                }

                $data[$field] = json_encode($data[$field]);
            }
        }

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = Leader::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'leaders',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        Leader::create($data);

        return redirect()->route('admin.leaders.index')->with('success', 'Leader created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $leader = Leader::findOrFail($id);
        return view('admin.leaders.show', compact('leader'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $leader = Leader::findOrFail($id);
        return view('admin.leaders.edit', compact('leader'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $leader = Leader::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'position' => 'required|string|max:255',
            'position_en' => 'nullable|string|max:255',
            'bio' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'bio_en' => ['nullable', 'string', function ($attribute, $value, $fail) {
                // Count words in the bio_en field (strip HTML tags first)
                $wordCount = str_word_count(strip_tags($value));
                if ($wordCount > 100) {
                    $fail('The bio (English) must not exceed 100 words. Current count: ' . $wordCount . ' words.');
                }
            }],
            'education_history' => 'nullable|array',
            'education_history_en' => 'nullable|array',
            'achievements' => 'nullable|array',
            'achievements_en' => 'nullable|array',
            'work_experience' => 'nullable|array',
            'work_experience_en' => 'nullable|array',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'social_facebook' => 'nullable|string|max:255',
            'social_twitter' => 'nullable|string|max:255',
            'social_instagram' => 'nullable|string|max:255',
            'social_linkedin' => 'nullable|string|max:255',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->except(['image', '_token', '_method']);

        // Process JSON fields and ensure shared fields have the same values
        foreach (['education_history', 'education_history_en', 'achievements', 'achievements_en', 'work_experience', 'work_experience_en'] as $field) {
            if (isset($data[$field]) && is_array($data[$field])) {
                // Special handling for fields that should be the same in both languages
                if ($field === 'education_history_en' && isset($data['education_history'])) {
                    foreach ($data[$field] as $key => $item) {
                        if (isset($data['education_history'][$key])) {
                            // Copy year and location from Indonesian to English
                            $data[$field][$key]['year'] = $data['education_history'][$key]['year'] ?? '';
                            $data[$field][$key]['location'] = $data['education_history'][$key]['location'] ?? '';
                        }
                    }
                } else if ($field === 'achievements_en' && isset($data['achievements'])) {
                    foreach ($data[$field] as $key => $item) {
                        if (isset($data['achievements'][$key])) {
                            // Copy year from Indonesian to English
                            $data[$field][$key]['year'] = $data['achievements'][$key]['year'] ?? '';
                        }
                    }
                } else if ($field === 'work_experience_en' && isset($data['work_experience'])) {
                    foreach ($data[$field] as $key => $item) {
                        if (isset($data['work_experience'][$key])) {
                            // Copy period and location from Indonesian to English
                            $data[$field][$key]['period'] = $data['work_experience'][$key]['period'] ?? '';
                            $data[$field][$key]['location'] = $data['work_experience'][$key]['location'] ?? '';
                        }
                    }
                }

                $data[$field] = json_encode($data[$field]);
            }
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($leader->image) {
                $this->imageService->deleteImage($leader->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'leaders',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        $leader->update($data);

        return redirect()->route('admin.leaders.index')->with('success', 'Leader updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $leader = Leader::findOrFail($id);

        // Delete image if exists
        if ($leader->image) {
            $this->imageService->deleteImage($leader->image);
        }

        $leader->delete();

        return redirect()->route('admin.leaders.index')->with('success', 'Leader deleted successfully.');
    }
}
