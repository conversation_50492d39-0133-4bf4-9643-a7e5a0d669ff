<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Events\AnnouncementCreated;
use App\Models\Announcement;
use App\Models\User;

class TestAnnouncementNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test-announcement';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending an announcement notification to Telegram';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating a test announcement...');

        // Find an admin user
        $user = User::where('role', 'admin')->first();

        if (!$user) {
            $this->error('No admin user found.');
            return 1;
        }

        // Create a test announcement
        $announcement = new Announcement();
        $announcement->title = 'Test Announcement';
        $announcement->content = 'This is a test announcement created at ' . now()->format('Y-m-d H:i:s');
        $announcement->start_date = now();
        $announcement->end_date = now()->addDays(7);
        $announcement->is_active = true;
        $announcement->save();

        $this->info('Test announcement created with ID: ' . $announcement->id);

        // Dispatch the event
        event(new AnnouncementCreated($announcement));

        $this->info('AnnouncementCreated event dispatched.');
        $this->info('Check your Telegram group to see if the notification was sent to the correct topic.');

        return 0;
    }
}
