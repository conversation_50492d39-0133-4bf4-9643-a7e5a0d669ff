<?php $__env->startSection('title', 'Leader Details'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Text formatting styles */
    .formatted-content p {
        margin-bottom: 0.5rem;
    }

    .formatted-content ul, .formatted-content ol {
        padding-left: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .formatted-content table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .formatted-content table th, .formatted-content table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .formatted-content table th {
        background-color: #f8f9fa;
    }

    .formatted-content img {
        max-width: 100%;
        height: auto;
        margin-bottom: 0.5rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Leader Details</h1>
        <div>
            <a href="<?php echo e(route('admin.leaders.edit', $leader)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.leaders.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <?php if($leader->image): ?>
                        <img src="<?php echo e(asset('storage/' . $leader->image)); ?>" alt="<?php echo e($leader->name); ?>" class="img-fluid rounded mb-3" style="max-height: 300px;">
                    <?php else: ?>
                        <div class="bg-light p-5 mb-3 text-center">
                            <i class="fas fa-user fa-5x text-secondary"></i>
                        </div>
                    <?php endif; ?>

                    <h4><?php echo e($leader->name); ?></h4>
                    <p class="text-muted"><?php echo e($leader->position); ?></p>

                    <div class="d-flex justify-content-center mt-3">
                        <?php if($leader->social_facebook): ?>
                            <a href="<?php echo e($leader->social_facebook); ?>" target="_blank" class="btn btn-outline-primary mx-1">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        <?php endif; ?>

                        <?php if($leader->social_twitter): ?>
                            <a href="<?php echo e($leader->social_twitter); ?>" target="_blank" class="btn btn-outline-dark mx-1">
                                <i class="fab fa-x-twitter"></i>
                            </a>
                        <?php endif; ?>

                        <?php if($leader->social_instagram): ?>
                            <a href="<?php echo e($leader->social_instagram); ?>" target="_blank" class="btn btn-outline-danger mx-1">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>

                        <?php if($leader->social_linkedin): ?>
                            <a href="<?php echo e($leader->social_linkedin); ?>" target="_blank" class="btn btn-outline-info mx-1">
                                <i class="fab fa-telegram"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Leader Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Status</div>
                        <div class="col-md-9">
                            <?php if($leader->is_active): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Display Order</div>
                        <div class="col-md-9"><?php echo e($leader->order); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Email</div>
                        <div class="col-md-9">
                            <?php if($leader->email): ?>
                                <a href="mailto:<?php echo e($leader->email); ?>"><?php echo e($leader->email); ?></a>
                            <?php else: ?>
                                <span class="text-muted">Not provided</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Phone</div>
                        <div class="col-md-9">
                            <?php if($leader->phone): ?>
                                <a href="tel:<?php echo e($leader->phone); ?>"><?php echo e($leader->phone); ?></a>
                            <?php else: ?>
                                <span class="text-muted">Not provided</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Created At</div>
                        <div class="col-md-9"><?php echo e($leader->created_at->format('d M Y, H:i')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Last Updated</div>
                        <div class="col-md-9"><?php echo e($leader->updated_at->format('d M Y, H:i')); ?></div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="bioTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="bio-id-tab" data-bs-toggle="tab" data-bs-target="#bio-id" type="button" role="tab" aria-controls="bio-id" aria-selected="true">Bio (Indonesian)</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bio-en-tab" data-bs-toggle="tab" data-bs-target="#bio-en" type="button" role="tab" aria-controls="bio-en" aria-selected="false">Bio (English)</button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="bioTabsContent">
                        <div class="tab-pane fade show active" id="bio-id" role="tabpanel" aria-labelledby="bio-id-tab">
                            <?php if($leader->bio): ?>
                                <div class="formatted-content"><?php echo $leader->bio; ?></div>
                            <?php else: ?>
                                <p class="text-muted">No biography provided in Indonesian.</p>
                            <?php endif; ?>
                        </div>
                        <div class="tab-pane fade" id="bio-en" role="tabpanel" aria-labelledby="bio-en-tab">
                            <?php if($leader->bio_en): ?>
                                <div class="formatted-content"><?php echo $leader->bio_en; ?></div>
                            <?php else: ?>
                                <p class="text-muted">No biography provided in English.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Education History -->
            <?php if(isset($leader->education_history) && $leader->education_history): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="educationTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="education-id-tab" data-bs-toggle="tab" data-bs-target="#education-id" type="button" role="tab" aria-controls="education-id" aria-selected="true">Education History (Indonesian)</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="education-en-tab" data-bs-toggle="tab" data-bs-target="#education-en" type="button" role="tab" aria-controls="education-en" aria-selected="false">Education History (English)</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="educationTabsContent">
                            <div class="tab-pane fade show active" id="education-id" role="tabpanel" aria-labelledby="education-id-tab">
                                <?php
                                    $educationHistory = json_decode($leader->education_history, true) ?? [];
                                ?>

                                <?php if(count($educationHistory) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Institution</th>
                                                    <th>Degree</th>
                                                    <th>Year</th>
                                                    <th>Location</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $educationHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $education): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($education['institution'] ?? '-'); ?></td>
                                                        <td><?php echo e($education['degree'] ?? '-'); ?></td>
                                                        <td><?php echo e($education['year'] ?? '-'); ?></td>
                                                        <td><?php echo e($education['location'] ?? '-'); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No education history provided in Indonesian.</p>
                                <?php endif; ?>
                            </div>
                            <div class="tab-pane fade" id="education-en" role="tabpanel" aria-labelledby="education-en-tab">
                                <?php
                                    $educationHistoryEn = json_decode($leader->education_history_en, true) ?? [];
                                    $educationHistory = json_decode($leader->education_history, true) ?? [];

                                    // If English version is empty but Indonesian version exists, use Indonesian data
                                    if (count($educationHistoryEn) == 0 && count($educationHistory) > 0) {
                                        $educationHistoryEn = $educationHistory;
                                    } else {
                                        // For each item in English version, fill in missing fields from Indonesian version
                                        foreach ($educationHistoryEn as $key => $item) {
                                            if (isset($educationHistory[$key])) {
                                                // For fields that should be the same in both languages
                                                if (!isset($item['year']) || empty($item['year'])) {
                                                    $educationHistoryEn[$key]['year'] = $educationHistory[$key]['year'] ?? '-';
                                                }
                                                if (!isset($item['location']) || empty($item['location'])) {
                                                    $educationHistoryEn[$key]['location'] = $educationHistory[$key]['location'] ?? '-';
                                                }
                                            }
                                        }
                                    }
                                ?>

                                <?php if(count($educationHistoryEn) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Institution</th>
                                                    <th>Degree</th>
                                                    <th>Year</th>
                                                    <th>Location</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $educationHistoryEn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $education): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($education['institution'] ?? '-'); ?></td>
                                                        <td><?php echo e($education['degree'] ?? '-'); ?></td>
                                                        <td><?php echo e($education['year'] ?? '-'); ?></td>
                                                        <td><?php echo e($education['location'] ?? '-'); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No education history provided in English.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Achievements -->
            <?php if(isset($leader->achievements) && $leader->achievements): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="achievementsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="achievements-id-tab" data-bs-toggle="tab" data-bs-target="#achievements-id" type="button" role="tab" aria-controls="achievements-id" aria-selected="true">Achievements (Indonesian)</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="achievements-en-tab" data-bs-toggle="tab" data-bs-target="#achievements-en" type="button" role="tab" aria-controls="achievements-en" aria-selected="false">Achievements (English)</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="achievementsTabsContent">
                            <div class="tab-pane fade show active" id="achievements-id" role="tabpanel" aria-labelledby="achievements-id-tab">
                                <?php
                                    $achievements = json_decode($leader->achievements, true) ?? [];
                                ?>

                                <?php if(count($achievements) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Title</th>
                                                    <th>Description</th>
                                                    <th>Year</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($achievement['title'] ?? '-'); ?></td>
                                                        <td><?php echo $achievement['description'] ?? '-'; ?></td>
                                                        <td><?php echo e($achievement['year'] ?? '-'); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No achievements provided in Indonesian.</p>
                                <?php endif; ?>
                            </div>
                            <div class="tab-pane fade" id="achievements-en" role="tabpanel" aria-labelledby="achievements-en-tab">
                                <?php
                                    $achievementsEn = json_decode($leader->achievements_en, true) ?? [];
                                    $achievements = json_decode($leader->achievements, true) ?? [];

                                    // If English version is empty but Indonesian version exists, use Indonesian data
                                    if (count($achievementsEn) == 0 && count($achievements) > 0) {
                                        $achievementsEn = $achievements;
                                    } else {
                                        // For each item in English version, fill in missing fields from Indonesian version
                                        foreach ($achievementsEn as $key => $item) {
                                            if (isset($achievements[$key])) {
                                                // For fields that should be the same in both languages
                                                if (!isset($item['year']) || empty($item['year'])) {
                                                    $achievementsEn[$key]['year'] = $achievements[$key]['year'] ?? '-';
                                                }
                                            }
                                        }
                                    }
                                ?>

                                <?php if(count($achievementsEn) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Title</th>
                                                    <th>Description</th>
                                                    <th>Year</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $achievementsEn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($achievement['title'] ?? '-'); ?></td>
                                                        <td><?php echo $achievement['description'] ?? '-'; ?></td>
                                                        <td><?php echo e($achievement['year'] ?? '-'); ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No achievements provided in English.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Work Experience -->
            <?php if(isset($leader->work_experience) && $leader->work_experience): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="workExperienceTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="work-experience-id-tab" data-bs-toggle="tab" data-bs-target="#work-experience-id" type="button" role="tab" aria-controls="work-experience-id" aria-selected="true">Work Experience (Indonesian)</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="work-experience-en-tab" data-bs-toggle="tab" data-bs-target="#work-experience-en" type="button" role="tab" aria-controls="work-experience-en" aria-selected="false">Work Experience (English)</button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="workExperienceTabsContent">
                            <div class="tab-pane fade show active" id="work-experience-id" role="tabpanel" aria-labelledby="work-experience-id-tab">
                                <?php
                                    $workExperience = json_decode($leader->work_experience, true) ?? [];
                                ?>

                                <?php if(count($workExperience) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Company/Organization</th>
                                                    <th>Position</th>
                                                    <th>Period</th>
                                                    <th>Location</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $workExperience; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $experience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($experience['company'] ?? '-'); ?></td>
                                                        <td><?php echo e($experience['position'] ?? '-'); ?></td>
                                                        <td><?php echo e($experience['period'] ?? '-'); ?></td>
                                                        <td><?php echo e($experience['location'] ?? '-'); ?></td>
                                                        <td><?php echo $experience['description'] ?? '-'; ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No work experience provided in Indonesian.</p>
                                <?php endif; ?>
                            </div>
                            <div class="tab-pane fade" id="work-experience-en" role="tabpanel" aria-labelledby="work-experience-en-tab">
                                <?php
                                    $workExperienceEn = json_decode($leader->work_experience_en, true) ?? [];
                                    $workExperience = json_decode($leader->work_experience, true) ?? [];

                                    // If English version is empty but Indonesian version exists, use Indonesian data
                                    if (count($workExperienceEn) == 0 && count($workExperience) > 0) {
                                        $workExperienceEn = $workExperience;
                                    } else {
                                        // For each item in English version, fill in missing fields from Indonesian version
                                        foreach ($workExperienceEn as $key => $item) {
                                            if (isset($workExperience[$key])) {
                                                // For fields that should be the same in both languages
                                                if (!isset($item['period']) || empty($item['period'])) {
                                                    $workExperienceEn[$key]['period'] = $workExperience[$key]['period'] ?? '-';
                                                }
                                                if (!isset($item['location']) || empty($item['location'])) {
                                                    $workExperienceEn[$key]['location'] = $workExperience[$key]['location'] ?? '-';
                                                }
                                            }
                                        }
                                    }
                                ?>

                                <?php if(count($workExperienceEn) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Company/Organization</th>
                                                    <th>Position</th>
                                                    <th>Period</th>
                                                    <th>Location</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $workExperienceEn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $experience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($experience['company'] ?? '-'); ?></td>
                                                        <td><?php echo e($experience['position'] ?? '-'); ?></td>
                                                        <td><?php echo e($experience['period'] ?? '-'); ?></td>
                                                        <td><?php echo e($experience['location'] ?? '-'); ?></td>
                                                        <td><?php echo $experience['description'] ?? '-'; ?></td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted">No work experience provided in English.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/leaders/show.blade.php ENDPATH**/ ?>