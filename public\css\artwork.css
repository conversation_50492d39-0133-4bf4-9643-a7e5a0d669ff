/* Artwork CSS for <PERSON>uru<PERSON> 4 Website */

/* General Artwork Styles */
.page-header {
    background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('../images/header-bg.jpg') center/cover no-repeat;
    padding: 80px 0;
    margin-bottom: 0;
}

.page-title {
    color: white;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.breadcrumb-item, .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
}

.breadcrumb-item.active {
    color: white;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.6);
}

.section-padding {
    padding: 70px 0;
}

.section-title h2 {
    font-weight: 600;
    color: #333;
    position: relative;
    margin-bottom: 15px;
}

.divider {
    width: 60px;
    height: 3px;
    background-color: #198754;
    margin-bottom: 20px;
}

/* Artwork Cards */
.artwork-card, .literature-card {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.artwork-card:hover, .literature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.artwork-image, .literature-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.artwork-image img, .literature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.artwork-card:hover .artwork-image img, .literature-card:hover .literature-image img {
    transform: scale(1.1);
}

.artwork-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(25, 135, 84, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.artwork-card:hover .artwork-overlay {
    opacity: 1;
}

.artwork-link {
    color: white;
    font-size: 24px;
}

.artwork-info, .literature-info {
    padding: 1.25rem;
}

.artist, .author, .year, .type {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Literature Specific */
.literature-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
}

.literature-excerpt {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.5;
}

/* Paint Detail */
.paint-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.paint-image-container img {
    width: 100%;
    transition: transform 0.5s ease;
}

.image-zoom {
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background-color: rgba(25, 135, 84, 0.8);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-zoom:hover {
    background-color: rgba(25, 135, 84, 1);
    color: white;
}

.icon-box {
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #198754;
}

/* Literature Detail */
.literature-featured-image img {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
}

.literature-meta {
    font-size: 0.95rem;
    color: #6c757d;
}

.literature-content {
    line-height: 1.8;
}

.literature-content img {
    max-width: 100%;
    height: auto;
}

.widget-title {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
}

.widget-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: #198754;
}

.author-avatar {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.related-item:hover h6 {
    color: #198754;
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    display: block;
    margin: 0 auto;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 30px;
    cursor: pointer;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .section-padding {
        padding: 50px 0;
    }
    
    .artwork-image, .literature-image {
        height: 220px;
    }
}

@media (max-width: 767.98px) {
    .page-header {
        padding: 60px 0;
    }
    
    .artwork-image, .literature-image {
        height: 200px;
    }
}

@media (max-width: 575.98px) {
    .page-header {
        padding: 40px 0;
    }
    
    .page-title {
        font-size: 1.75rem;
    }
    
    .artwork-image, .literature-image {
        height: 180px;
    }
}
