<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\News;
use App\Models\NewsCategory;
use App\Models\User;

class TestNewsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test category if none exists
        $category = NewsCategory::first();
        if (!$category) {
            $category = NewsCategory::create([
                'name' => 'Test Category',
                'slug' => 'test-category',
                'is_active' => true,
            ]);
        }

        // Get the first user or create one if none exists
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        // Create a test news article
        News::create([
            'title' => 'Test News',
            'slug' => 'test-news',
            'content' => 'Test content',
            'user_id' => $user->id,
            'category_id' => $category->id,
            'is_published' => true,
            'published_at' => now(),
        ]);

        $this->command->info('Test news article created successfully.');
    }
}
