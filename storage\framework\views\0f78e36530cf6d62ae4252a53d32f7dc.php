<?php $__env->startSection('title', 'View Announcement'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Announcement</h1>
        <div>
            <a href="<?php echo e(route('admin.announcements.edit', $announcement)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?php echo e(route('admin.announcements.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Image</h5>
                </div>
                <div class="card-body text-center">
                    <?php if($announcement->image): ?>
                        <img src="<?php echo e(asset('storage/' . $announcement->image)); ?>" alt="<?php echo e($announcement->title); ?>" class="img-fluid rounded">
                    <?php else: ?>
                        <div class="alert alert-info">No image available</div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">File Attachment</h5>
                </div>
                <div class="card-body text-center">
                    <?php if($announcement->file): ?>
                        <a href="<?php echo e(asset('storage/' . $announcement->file)); ?>" target="_blank" class="btn btn-primary">
                            <i class="fas fa-file-pdf me-2"></i> View PDF File
                        </a>
                        <p class="mt-2 text-muted small"><?php echo e(basename($announcement->file)); ?></p>
                    <?php else: ?>
                        <div class="alert alert-info">No file attached</div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Details</h5>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th>ID:</th>
                            <td><?php echo e($announcement->id); ?></td>
                        </tr>
                        <tr>
                            <th>Start Date:</th>
                            <td><?php echo e($announcement->start_date->format('d M Y')); ?></td>
                        </tr>
                        <tr>
                            <th>End Date:</th>
                            <td><?php echo e($announcement->end_date->format('d M Y')); ?></td>
                        </tr>
                        <tr>
                            <th>Order:</th>
                            <td><?php echo e($announcement->order); ?></td>
                        </tr>
                        <tr>
                            <th>Featured:</th>
                            <td>
                                <?php if($announcement->is_featured): ?>
                                    <span class="badge bg-warning">Yes</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">No</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <?php if($announcement->is_active): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Created:</th>
                            <td><?php echo e($announcement->created_at->format('d M Y H:i')); ?></td>
                        </tr>
                        <tr>
                            <th>Updated:</th>
                            <td><?php echo e($announcement->updated_at->format('d M Y H:i')); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Content</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Indonesian</h6>
                            <h5 class="mt-3"><?php echo e($announcement->title); ?></h5>
                            <div class="p-3 bg-light rounded">
                                <?php echo $announcement->content; ?>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>English</h6>
                            <h5 class="mt-3"><?php echo e($announcement->title_en); ?></h5>
                            <div class="p-3 bg-light rounded">
                                <?php echo $announcement->content_en; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/announcements/show.blade.php ENDPATH**/ ?>