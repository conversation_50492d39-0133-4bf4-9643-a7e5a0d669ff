@extends('admin.layouts.app')

@section('title', 'Dashboard')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Dashboard</h1>
    </div>

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-primary-light">
                <div class="card-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="card-title">Total News</div>
                <div class="card-value">{{ $newsCount }}</div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-success-light">
                <div class="card-icon">
                    <i class="fas fa-images"></i>
                </div>
                <div class="card-title">Total Gallery Items</div>
                <div class="card-value">{{ $galleryCount }}</div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-warning-light">
                <div class="card-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="card-title">Pending Registrations</div>
                <div class="card-value">{{ $pendingRegistrations }}</div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-danger-light">
                <div class="card-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="card-title">Unread Messages</div>
                <div class="card-value">{{ $unreadMessages }}</div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Latest Registrations</h5>
                    <a href="{{ route('admin.registrations.index') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($latestRegistrations as $registration)
                                    <tr>
                                        <td>{{ $registration->full_name }}</td>
                                        <td>{{ $registration->created_at ? $registration->created_at->format('d M Y') : 'N/A' }}</td>
                                        <td>
                                            @if($registration->status == 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($registration->status == 'approved')
                                                <span class="badge bg-success">Approved</span>
                                            @elseif($registration->status == 'rejected')
                                                <span class="badge bg-danger">Rejected</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.registrations.show', $registration) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center">No registrations found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Latest Messages</h5>
                    <a href="{{ route('admin.contacts.index') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Subject</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($latestMessages as $message)
                                    <tr class="{{ $message->is_read ? '' : 'table-light' }}">
                                        <td>{{ $message->name }}</td>
                                        <td>{{ \Illuminate\Support\Str::limit($message->subject, 30) }}</td>
                                        <td>{{ $message->created_at->format('d M Y') }}</td>
                                        <td>
                                            <a href="{{ route('admin.contacts.show', $message) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center">No messages found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
