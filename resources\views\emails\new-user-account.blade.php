<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('emails.new_user_subject') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header img {
            max-width: 150px;
            height: auto;
        }
        h1 {
            color: #198754;
            font-size: 24px;
            margin-top: 0;
        }
        .details-box {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .details-box h3 {
            color: #0056b3;
            margin-top: 0;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        .details-table td {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
        }
        .details-table tr:last-child td {
            border-bottom: none;
        }
        .warning-box {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
            border-radius: 4px;
        }
        .notice-box {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #6c757d;
            margin: 15px 0;
            border-radius: 4px;
        }
        .button {
            display: inline-block;
            background-color: #198754;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin: 15px 0;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ config('app.url') }}/storage/website/LogoNUHA4_1746320576.png" alt="{{ config('app.name') }} Logo">
            <h1>{{ config('app.name') }}</h1>
        </div>

        <h2>{{ __('emails.islamic_greeting') }}</h2>

        <p>{{ __('emails.new_user_intro') }}</p>

        <div class="details-box">
            <h3>{{ __('emails.new_user_details') }}</h3>
            <table class="details-table">
                <tr>
                    <td width="40%"><strong>{{ __('emails.new_user_username') }}:</strong></td>
                    <td>{{ $username }}</td>
                </tr>
                <tr>
                    <td><strong>{{ __('emails.new_user_email') }}:</strong></td>
                    <td>{{ $email }}</td>
                </tr>
                <tr>
                    <td><strong>{{ __('emails.new_user_password') }}:</strong></td>
                    <td style="font-weight: bold; color: #0056b3;">{{ $password }}</td>
                </tr>
            </table>
        </div>

        <div class="warning-box">
            <strong>{{ __('emails.new_user_password_change') }}</strong>
        </div>

        <div style="text-align: center;">
            <a href="{{ url('/login') }}" class="button">{{ __('emails.new_user_login') }}</a>
        </div>

        <div class="notice-box">
            <strong>{{ __('emails.auto_email_notice') }}</strong>
        </div>

        <p>{{ __('emails.islamic_closing') }}</p>

        <div class="footer">
            &copy; {{ date('Y') }} {{ config('app.name') }}
        </div>
    </div>
</body>
</html>
