@if($item->is_active)
    <li class="nav-item {{ $item->hasActiveChildren() ? 'dropdown' : '' }}">
        @if($item->hasActiveChildren())
            <a class="nav-link dropdown-toggle" href="{{ $item->url }}" id="navbarDropdown{{ $item->id }}" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                {{ app()->getLocale() == 'en' && $item->title_en ? $item->title_en : $item->title }}
            </a>
            <ul class="dropdown-menu" aria-labelledby="navbarDropdown{{ $item->id }}">
                @foreach($item->activeChildren as $child)
                    @include('components.menu-item', ['item' => $child])
                @endforeach
            </ul>
        @else
            <a class="nav-link" href="{{ $item->url }}" {{ $item->target != '_self' ? 'target='.$item->target : '' }}>
                {{ app()->getLocale() == 'en' && $item->title_en ? $item->title_en : $item->title }}
            </a>
        @endif
    </li>
@endif
