<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Jadwal Kegiatan' : 'Activity Schedules'); ?>

<?php $__env->startSection('meta_description', app()->getLocale() == 'id' ? 'Jadwal kegiatan di ' . \App\Helpers\SettingHelper::getInstitutionName() : 'Activity schedules at ' . \App\Helpers\SettingHelper::getInstitutionNameEn()); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Jadwal Kegiatan' : 'Activity Schedules'); ?></h1>
                    <p><?php echo e(app()->getLocale() == 'id' ? 'Jadwal kegiatan di ' . \App\Helpers\SettingHelper::getInstitutionName() : 'Activity schedules at ' . \App\Helpers\SettingHelper::getInstitutionNameEn()); ?></p>
                </div>
            </div>
        </div>
    </section>

    <?php $__env->startPush('styles'); ?>
    <style>
        /* New Daily Schedule Item Styles */
        .daily-schedule-item {
            position: relative;
            padding-left: 10px;
            margin-bottom: 15px;
            border-radius: 0;
            background-color: #fff;
            transition: all 0.3s ease;
            overflow: hidden;
            border-bottom: 1px solid #f0f0f0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .daily-schedule-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .daily-schedule-item::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 5px;
            background-color: #198754;
        }

        .daily-schedule-content {
            display: flex;
            padding: 15px 15px 10px 15px;
        }

        .daily-schedule-time {
            flex: 0 0 110px;
            padding: 8px 5px;
            margin-right: 15px;
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
            font-weight: 600;
            border-radius: 5px;
            text-align: center;
            align-self: flex-start;
            font-size: 14px;
        }

        .daily-schedule-info {
            flex: 1;
            min-width: 0; /* Prevents flex item from overflowing */
        }

        .daily-schedule-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            color: #333;
            line-height: 1.2;
        }

        .daily-schedule-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px;
            line-height: 1.5;
        }

        .daily-schedule-description p {
            margin-bottom: 0;
        }

        .daily-schedule-badges {
            display: flex;
            gap: 8px;
            margin-top: 5px;
            margin-bottom: 12px;
            padding: 0 15px 0 15px;
            flex-wrap: wrap;
        }

        .daily-location-badge, .daily-category-badge, .daily-instructor-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .daily-location-badge {
            background-color: #f8f9fa;
            color: #dc3545;
        }

        .daily-category-badge {
            background-color: #f8f9fa;
            color: #0d6efd;
        }

        .daily-instructor-badge {
            background-color: #f8f9fa;
            color: #198754;
        }

        /* Ensure the daily schedule list has proper spacing */
        .daily-schedules-list {
            display: flex;
            flex-direction: column;
            gap: 0;
            padding-bottom: 15px;
        }

        .daily-location-badge i, .daily-category-badge i, .daily-instructor-badge i {
            margin-right: 5px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .daily-schedule-item {
                margin-bottom: 15px;
            }

            .daily-schedule-item:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            }

            .daily-schedule-content {
                flex-direction: row;
                align-items: flex-start;
                padding: 12px 12px 5px 12px;
            }

            .daily-schedule-time {
                flex: 0 0 90px;
                margin-right: 10px;
                padding: 6px 4px;
                font-size: 13px;
            }

            .daily-schedule-title {
                font-size: 16px;
                margin-bottom: 4px;
            }

            .daily-schedule-description {
                font-size: 13px;
            }

            .daily-schedule-badges {
                padding: 0 12px 12px 12px;
                margin-top: 5px;
                margin-bottom: 5px;
            }

            .daily-location-badge, .daily-category-badge, .daily-instructor-badge {
                padding: 2px 6px;
                font-size: 11px;
            }
        }

        /* Schedule Card Styles */
        .schedule-card {
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            border-left: 5px solid #198754;
        }

        .schedule-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .schedule-time {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .time-badge {
            display: inline-block;
            padding: 5px 10px;
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .category-badge {
            display: inline-block;
            padding: 3px 8px;
            background-color: #f8f9fa;
            color: #6c757d;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .schedule-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #333;
        }

        .schedule-details {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 14px;
            color: #6c757d;
        }

        .schedule-location, .schedule-instructor {
            display: flex;
            align-items: center;
        }

        .schedule-location i, .schedule-instructor i {
            margin-right: 5px;
            font-size: 16px;
        }

        .schedule-description {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.6;
        }

        /* Tab Styles */
        .schedule-tabs .nav-pills {
            background-color: #f8f9fa;
            border-radius: 50px;
            padding: 5px;
        }

        .schedule-tabs .nav-link {
            border-radius: 50px;
            padding: 10px 20px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .schedule-tabs .nav-link.active {
            background-color: #198754;
            color: white;
        }

        .schedule-tabs .nav-link:hover:not(.active) {
            background-color: #e9ecef;
        }

        /* Divider Styles */
        .day-divider, .week-divider, .month-divider {
            position: relative;
            text-align: center;
            margin: 40px 0 30px;
        }

        .day-divider:before, .week-divider:before, .month-divider:before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #dee2e6;
            z-index: 1;
        }

        .day-divider span, .week-divider span, .month-divider span {
            position: relative;
            z-index: 2;
            background-color: #fff;
            padding: 0 20px;
            font-size: 18px;
            font-weight: 600;
            color: #198754;
        }

        /* Filter Buttons */
        .filter-buttons {
            margin-bottom: 30px;
        }

        .filter-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 14px;
            font-weight: 500;
        }

        .filter-buttons .btn.active {
            background-color: #198754;
            color: white;
        }

        /* Animation for schedule cards */
        .schedule-item, .daily-schedule-item {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .schedule-item.active, .daily-schedule-item.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .schedule-tabs .nav-pills {
                flex-direction: column;
                border-radius: 10px;
            }

            .schedule-tabs .nav-link {
                border-radius: 0;
                margin-bottom: 5px;
            }

            .schedule-tabs .nav-item:first-child .nav-link {
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
            }

            .schedule-tabs .nav-item:last-child .nav-link {
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
            }

            .day-divider span, .week-divider span, .month-divider span {
                font-size: 16px;
            }
        }
    </style>
    <?php $__env->stopPush(); ?>

    <!-- Schedules Section -->
    <section class="section-padding">
        <div class="container">
            <!-- Tabs Navigation -->
            <div class="schedule-tabs mb-5">
                <ul class="nav nav-pills nav-fill mb-4" id="scheduleTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo e($activeTab == 'daily' ? 'active' : ''); ?>" id="daily-tab" data-bs-toggle="pill" data-bs-target="#daily" type="button" role="tab" aria-controls="daily" aria-selected="<?php echo e($activeTab == 'daily' ? 'true' : 'false'); ?>">
                            <i class="fas fa-calendar-day me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Harian' : 'Daily'); ?>

                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo e($activeTab == 'weekly' ? 'active' : ''); ?>" id="weekly-tab" data-bs-toggle="pill" data-bs-target="#weekly" type="button" role="tab" aria-controls="weekly" aria-selected="<?php echo e($activeTab == 'weekly' ? 'true' : 'false'); ?>">
                            <i class="fas fa-calendar-week me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Mingguan' : 'Weekly'); ?>

                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo e($activeTab == 'monthly' ? 'active' : ''); ?>" id="monthly-tab" data-bs-toggle="pill" data-bs-target="#monthly" type="button" role="tab" aria-controls="monthly" aria-selected="<?php echo e($activeTab == 'monthly' ? 'true' : 'false'); ?>">
                            <i class="fas fa-calendar-alt me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Bulanan' : 'Monthly'); ?>

                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo e($activeTab == 'yearly' ? 'active' : ''); ?>" id="yearly-tab" data-bs-toggle="pill" data-bs-target="#yearly" type="button" role="tab" aria-controls="yearly" aria-selected="<?php echo e($activeTab == 'yearly' ? 'true' : 'false'); ?>">
                            <i class="fas fa-calendar me-2"></i><?php echo e(app()->getLocale() == 'id' ? 'Tahunan' : 'Annual'); ?>

                        </button>
                    </li>
                </ul>
            </div>

            <!-- Tab Content -->
            <div class="tab-content" id="scheduleTabsContent">
                <!-- Daily Schedules Tab -->
                <div class="tab-pane fade <?php echo e($activeTab == 'daily' ? 'show active' : ''); ?>" id="daily" role="tabpanel" aria-labelledby="daily-tab">
                    <div class="schedule-container">
                        <?php if($dailySchedules->isEmpty()): ?>
                            <div class="text-center">
                                <div class="alert alert-info">
                                    <?php echo e(app()->getLocale() == 'id' ? 'Belum ada jadwal harian yang tersedia.' : 'No daily schedules available yet.'); ?>

                                </div>
                            </div>
                        <?php else: ?>

                            <div class="daily-schedules-list">
                                <?php $__currentLoopData = $dailySchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="daily-schedule-item schedule-item fade-in" data-category="<?php echo e($schedule->category ?? 'other'); ?>">
                                        <div class="daily-schedule-content">
                                            <div class="daily-schedule-time">
                                                <?php echo e(\Carbon\Carbon::parse($schedule->start_time)->format('H:i')); ?> - <?php echo e(\Carbon\Carbon::parse($schedule->end_time)->format('H:i')); ?>

                                            </div>
                                            <div class="daily-schedule-info">
                                                <h4 class="daily-schedule-title"><?php echo e(app()->getLocale() == 'id' ? $schedule->title : $schedule->title_en); ?></h4>
                                                <div class="daily-schedule-description">
                                                    <?php echo app()->getLocale() == 'id' ? $schedule->description : $schedule->description_en; ?>

                                                </div>
                                            </div>
                                        </div>

                                        <div class="daily-schedule-badges">
                                            <?php if($schedule->location || $schedule->location_en): ?>
                                                <span class="daily-location-badge">
                                                    <i class="fas fa-map-marker-alt"></i>
                                                    <?php echo e(app()->getLocale() == 'id' ? $schedule->location : $schedule->location_en); ?>

                                                </span>
                                            <?php endif; ?>

                                            <?php if($schedule->category): ?>
                                                <span class="daily-category-badge">
                                                    <i class="fas fa-tag"></i>
                                                    <?php echo e($schedule->category); ?>

                                                </span>
                                            <?php endif; ?>

                                            <?php if($schedule->instructor || $schedule->instructor_en): ?>
                                                <span class="daily-instructor-badge">
                                                    <i class="fas fa-user"></i>
                                                    <?php echo e(app()->getLocale() == 'id' ? $schedule->instructor : $schedule->instructor_en); ?>

                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Weekly Schedules Tab -->
                <div class="tab-pane fade <?php echo e($activeTab == 'weekly' ? 'show active' : ''); ?>" id="weekly" role="tabpanel" aria-labelledby="weekly-tab">
                    <div class="schedule-container">
                        <?php if($weeklySchedules->isEmpty()): ?>
                            <div class="text-center">
                                <div class="alert alert-info">
                                    <?php echo e(app()->getLocale() == 'id' ? 'Belum ada jadwal mingguan yang tersedia.' : 'No weekly schedules available yet.'); ?>

                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Group schedules by day of week starting from Saturday -->
                            <?php
                                $dayOrder = ['Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
                                $schedulesByDay = $weeklySchedules->groupBy('day_of_week')->sortBy(function($items, $day) use ($dayOrder) {
                                    return array_search($day, $dayOrder);
                                });

                                $dayTranslations = [
                                    'Monday' => app()->getLocale() == 'id' ? 'Senin' : 'Monday',
                                    'Tuesday' => app()->getLocale() == 'id' ? 'Selasa' : 'Tuesday',
                                    'Wednesday' => app()->getLocale() == 'id' ? 'Rabu' : 'Wednesday',
                                    'Thursday' => app()->getLocale() == 'id' ? 'Kamis' : 'Thursday',
                                    'Friday' => app()->getLocale() == 'id' ? 'Jumat' : 'Friday',
                                    'Saturday' => app()->getLocale() == 'id' ? 'Sabtu' : 'Saturday',
                                    'Sunday' => app()->getLocale() == 'id' ? 'Minggu' : 'Sunday',
                                ];
                            ?>

                            <?php $__currentLoopData = $schedulesByDay; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day => $daySchedules): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="day-divider">
                                    <span><?php echo e($dayTranslations[$day] ?? $day); ?></span>
                                </div>
                                <div class="row justify-content-center">
                                    <?php $__currentLoopData = $daySchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-lg-6 mb-4 schedule-item fade-in" data-category="<?php echo e($schedule->category ?? 'other'); ?>">
                                            <div class="schedule-card">
                                                <div class="card-body p-4">
                                                    <div class="schedule-time">
                                                        <span class="time-badge">
                                                            <?php echo e(\Carbon\Carbon::parse($schedule->start_time)->format('H:i')); ?> - <?php echo e(\Carbon\Carbon::parse($schedule->end_time)->format('H:i')); ?>

                                                        </span>
                                                        <?php if($schedule->category): ?>
                                                            <span class="category-badge"><?php echo e($schedule->category); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <h4 class="schedule-title"><?php echo e(app()->getLocale() == 'id' ? $schedule->title : $schedule->title_en); ?></h4>

                                                    <div class="schedule-details">
                                                        <?php if($schedule->location || $schedule->location_en): ?>
                                                            <div class="schedule-location">
                                                                <i class="fas fa-map-marker-alt"></i>
                                                                <span><?php echo e(app()->getLocale() == 'id' ? $schedule->location : $schedule->location_en); ?></span>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if($schedule->instructor || $schedule->instructor_en): ?>
                                                            <div class="schedule-instructor">
                                                                <i class="fas fa-user"></i>
                                                                <span><?php echo e(app()->getLocale() == 'id' ? $schedule->instructor : $schedule->instructor_en); ?></span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="schedule-description mt-3">
                                                        <p><?php echo app()->getLocale() == 'id' ? $schedule->description : $schedule->description_en; ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Monthly Schedules Tab -->
                <div class="tab-pane fade <?php echo e($activeTab == 'monthly' ? 'show active' : ''); ?>" id="monthly" role="tabpanel" aria-labelledby="monthly-tab">
                    <div class="schedule-container">
                        <?php if($monthlySchedules->isEmpty()): ?>
                            <div class="text-center">
                                <div class="alert alert-info">
                                    <?php echo e(app()->getLocale() == 'id' ? 'Belum ada jadwal bulanan yang tersedia.' : 'No monthly schedules available yet.'); ?>

                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Group schedules by week number -->
                            <?php
                                $schedulesByWeek = $monthlySchedules->groupBy('week_number')->sortBy(function($items, $week) {
                                    return $week;
                                });

                                $weekTranslations = [
                                    1 => app()->getLocale() == 'id' ? 'Minggu Pertama' : 'First Week',
                                    2 => app()->getLocale() == 'id' ? 'Minggu Kedua' : 'Second Week',
                                    3 => app()->getLocale() == 'id' ? 'Minggu Ketiga' : 'Third Week',
                                    4 => app()->getLocale() == 'id' ? 'Minggu Keempat' : 'Fourth Week',
                                ];
                            ?>

                            <?php $__currentLoopData = $schedulesByWeek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $week => $weekSchedules): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="week-divider">
                                    <span><?php echo e($weekTranslations[$week] ?? 'Week ' . $week); ?></span>
                                </div>
                                <div class="row justify-content-center">
                                    <?php $__currentLoopData = $weekSchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-lg-6 mb-4 schedule-item fade-in" data-category="<?php echo e($schedule->category ?? 'other'); ?>">
                                            <div class="schedule-card">
                                                <div class="card-body p-4">
                                                    <div class="schedule-time">
                                                        <span class="time-badge">
                                                            <?php echo e(\Carbon\Carbon::parse($schedule->start_time)->format('H:i')); ?> - <?php echo e(\Carbon\Carbon::parse($schedule->end_time)->format('H:i')); ?>

                                                        </span>
                                                        <?php if($schedule->category): ?>
                                                            <span class="category-badge"><?php echo e($schedule->category); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <h4 class="schedule-title"><?php echo e(app()->getLocale() == 'id' ? $schedule->title : $schedule->title_en); ?></h4>

                                                    <div class="schedule-details">
                                                        <?php if($schedule->location || $schedule->location_en): ?>
                                                            <div class="schedule-location">
                                                                <i class="fas fa-map-marker-alt"></i>
                                                                <span><?php echo e(app()->getLocale() == 'id' ? $schedule->location : $schedule->location_en); ?></span>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if($schedule->instructor || $schedule->instructor_en): ?>
                                                            <div class="schedule-instructor">
                                                                <i class="fas fa-user"></i>
                                                                <span><?php echo e(app()->getLocale() == 'id' ? $schedule->instructor : $schedule->instructor_en); ?></span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="schedule-description mt-3">
                                                        <p><?php echo app()->getLocale() == 'id' ? $schedule->description : $schedule->description_en; ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Yearly Schedules Tab -->
                <div class="tab-pane fade <?php echo e($activeTab == 'yearly' ? 'show active' : ''); ?>" id="yearly" role="tabpanel" aria-labelledby="yearly-tab">
                    <div class="schedule-container">
                        <?php if($yearlySchedules->isEmpty()): ?>
                            <div class="text-center">
                                <div class="alert alert-info">
                                    <?php echo e(app()->getLocale() == 'id' ? 'Belum ada jadwal tahunan yang tersedia.' : 'No yearly schedules available yet.'); ?>

                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Group schedules by month number -->
                            <?php
                                $schedulesByMonth = $yearlySchedules->groupBy('month_number')->sortBy(function($items, $month) {
                                    return $month;
                                });

                                $monthTranslations = [
                                    1 => app()->getLocale() == 'id' ? 'Januari' : 'January',
                                    2 => app()->getLocale() == 'id' ? 'Februari' : 'February',
                                    3 => app()->getLocale() == 'id' ? 'Maret' : 'March',
                                    4 => app()->getLocale() == 'id' ? 'April' : 'April',
                                    5 => app()->getLocale() == 'id' ? 'Mei' : 'May',
                                    6 => app()->getLocale() == 'id' ? 'Juni' : 'June',
                                    7 => app()->getLocale() == 'id' ? 'Juli' : 'July',
                                    8 => app()->getLocale() == 'id' ? 'Agustus' : 'August',
                                    9 => app()->getLocale() == 'id' ? 'September' : 'September',
                                    10 => app()->getLocale() == 'id' ? 'Oktober' : 'October',
                                    11 => app()->getLocale() == 'id' ? 'November' : 'November',
                                    12 => app()->getLocale() == 'id' ? 'Desember' : 'December',
                                ];
                            ?>

                            <?php $__currentLoopData = $schedulesByMonth; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month => $monthSchedules): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="month-divider">
                                    <span><?php echo e($monthTranslations[$month] ?? 'Month ' . $month); ?></span>
                                </div>
                                <div class="row justify-content-center">
                                    <?php $__currentLoopData = $monthSchedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-lg-6 mb-4 schedule-item fade-in" data-category="<?php echo e($schedule->category ?? 'other'); ?>">
                                            <div class="schedule-card">
                                                <div class="card-body p-4">
                                                    <div class="schedule-time">
                                                        <span class="time-badge">
                                                            <?php echo e(\Carbon\Carbon::parse($schedule->start_time)->format('H:i')); ?> - <?php echo e(\Carbon\Carbon::parse($schedule->end_time)->format('H:i')); ?>

                                                        </span>
                                                        <?php if($schedule->category): ?>
                                                            <span class="category-badge"><?php echo e($schedule->category); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <h4 class="schedule-title"><?php echo e(app()->getLocale() == 'id' ? $schedule->title : $schedule->title_en); ?></h4>

                                                    <div class="schedule-details">
                                                        <?php if($schedule->location || $schedule->location_en): ?>
                                                            <div class="schedule-location">
                                                                <i class="fas fa-map-marker-alt"></i>
                                                                <span><?php echo e(app()->getLocale() == 'id' ? $schedule->location : $schedule->location_en); ?></span>
                                                            </div>
                                                        <?php endif; ?>

                                                        <?php if($schedule->instructor || $schedule->instructor_en): ?>
                                                            <div class="schedule-instructor">
                                                                <i class="fas fa-user"></i>
                                                                <span><?php echo e(app()->getLocale() == 'id' ? $schedule->instructor : $schedule->instructor_en); ?></span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="schedule-description mt-3">
                                                        <p><?php echo app()->getLocale() == 'id' ? $schedule->description : $schedule->description_en; ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations for schedule cards
        const scheduleItems = document.querySelectorAll('.schedule-item');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        scheduleItems.forEach(item => {
            observer.observe(item);
        });

        // Category filtering
        const filterButtons = document.querySelectorAll('.filter-buttons .btn');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Get filter value
                const filterValue = this.getAttribute('data-filter');

                // Filter schedule items
                scheduleItems.forEach(item => {
                    const category = item.getAttribute('data-category').toLowerCase();

                    if (filterValue === 'all' || category === filterValue) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // Tab change animations
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="pill"]');

        tabLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Get the target tab content
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                // Add a slight delay to allow the Bootstrap tab transition to complete
                setTimeout(() => {
                    // Trigger animations for elements inside the active tab
                    const animElements = targetPane.querySelectorAll('.schedule-item');
                    animElements.forEach((element, index) => {
                        // Reset animation state
                        element.classList.remove('active');

                        // Add staggered animation delay
                        setTimeout(() => {
                            element.classList.add('active');
                        }, index * 100);
                    });
                }, 150);
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/schedules/index.blade.php ENDPATH**/ ?>