// Custom JavaScript for Admin Panel

document.addEventListener('DOMContentLoaded', function() {
    const wrapper = document.getElementById('wrapper');
    const sidebarWrapper = document.getElementById('sidebar-wrapper');
    const pageContent = document.getElementById('page-content-wrapper');

    // Toggle sidebar with different behavior for mobile and desktop
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();

            // Check if we're on mobile or desktop
            if (window.innerWidth < 768) {
                // Mobile: toggle the sidebar visibility
                wrapper.classList.toggle('toggled');
            } else {
                // Desktop: toggle between mini and full sidebar
                wrapper.classList.toggle('mini-sidebar');
            }
        });
    }

    // Close mobile sidebar when clicking outside
    document.addEventListener('click', function(e) {
        // Only apply this on mobile
        if (window.innerWidth < 768) {
            // Check if sidebar is open and click is outside sidebar and not on toggle button
            if (wrapper.classList.contains('toggled') &&
                !sidebarWrapper.contains(e.target) &&
                e.target !== sidebarToggle &&
                !sidebarToggle.contains(e.target)) {
                wrapper.classList.remove('toggled');
            }
        }
    });

    // Handle window resize to ensure correct sidebar state
    window.addEventListener('resize', function() {
        if (window.innerWidth < 768) {
            // On mobile, remove mini-sidebar class and use toggled for visibility
            if (wrapper.classList.contains('mini-sidebar')) {
                wrapper.classList.remove('mini-sidebar');
            }
        } else {
            // On desktop, ensure toggled class is not affecting the sidebar
            if (wrapper.classList.contains('toggled')) {
                wrapper.classList.remove('toggled');
            }
        }
    });

    // Toggle submenu
    const submenuToggles = document.querySelectorAll('.has-submenu');
    if (submenuToggles.length > 0) {
        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                // Always toggle when clicking anywhere on the menu item
                e.preventDefault();
                this.classList.toggle('open');

                const submenu = this.nextElementSibling;
                if (submenu && submenu.classList.contains('sidebar-submenu')) {
                    submenu.classList.toggle('show');
                }
            });
        });
    }

    // Auto-open submenu if a child is active
    const activeSubmenuItems = document.querySelectorAll('.sidebar-submenu .submenu-item.active');
    if (activeSubmenuItems.length > 0) {
        activeSubmenuItems.forEach(item => {
            const submenu = item.closest('.sidebar-submenu');
            if (submenu) {
                submenu.classList.add('show');
                const parentMenuItem = submenu.previousElementSibling;
                if (parentMenuItem && parentMenuItem.classList.contains('has-submenu')) {
                    parentMenuItem.classList.add('open');
                }
            }
        });
    }

    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    if (alerts.length > 0) {
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize dropdowns explicitly
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function(dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Confirm delete
    const deleteButtons = document.querySelectorAll('.btn-delete');
    if (deleteButtons.length > 0) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
        });
    }

    // Image preview for file inputs
    const imageInputs = document.querySelectorAll('.image-input');
    if (imageInputs.length > 0) {
        imageInputs.forEach(input => {
            input.addEventListener('change', function() {
                const previewId = this.dataset.preview;
                const preview = document.getElementById(previewId);

                if (preview && this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }

                    reader.readAsDataURL(this.files[0]);
                }
            });
        });
    }

    // Toggle password visibility
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    if (togglePasswordButtons.length > 0) {
        togglePasswordButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.dataset.target;
                const target = document.getElementById(targetId);

                if (target) {
                    const type = target.getAttribute('type') === 'password' ? 'text' : 'password';
                    target.setAttribute('type', type);

                    // Toggle icon
                    this.querySelector('i').classList.toggle('fa-eye');
                    this.querySelector('i').classList.toggle('fa-eye-slash');
                }
            });
        });
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    if (forms.length > 0) {
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                form.classList.add('was-validated');
            }, false);
        });
    }
});
