<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Partnership;
use App\Services\CountryService;
use App\Services\ImageService;
use Illuminate\Http\Request;

class PartnershipController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * The country service instance.
     *
     * @var \App\Services\CountryService
     */
    protected $countryService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @param  \App\Services\CountryService  $countryService
     * @return void
     */
    public function __construct(ImageService $imageService, CountryService $countryService)
    {
        $this->imageService = $imageService;
        $this->countryService = $countryService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $partnerships = Partnership::orderBy('name', 'asc')->get();
        return view('admin.partnerships.index', compact('partnerships'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $countries = $this->countryService->getCountries();
        return view('admin.partnerships.create', compact('countries'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'website' => 'nullable|url|max:255',
            'partnership_since' => 'nullable|date',
            'country_code' => 'nullable|string|size:2',
            'logo' => 'nullable|mimes:png,svg',
        ]);

        $data = $request->except('logo');

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle logo upload with compression
        if ($request->hasFile('logo')) {
            $logoPath = $this->imageService->compressAndSave(
                $request->file('logo'),
                'partnerships',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['logo'] = $logoPath;
        }

        Partnership::create($data);

        return redirect()->route('admin.partnerships.index')
            ->with('success', 'Partnership created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Partnership  $partnership
     * @return \Illuminate\View\View
     */
    public function show(Partnership $partnership)
    {
        return view('admin.partnerships.show', compact('partnership'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Partnership  $partnership
     * @return \Illuminate\View\View
     */
    public function edit(Partnership $partnership)
    {
        $countries = $this->countryService->getCountries();
        return view('admin.partnerships.edit', compact('partnership', 'countries'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Partnership  $partnership
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Partnership $partnership)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'website' => 'nullable|url|max:255',
            'partnership_since' => 'nullable|date',
            'country_code' => 'nullable|string|size:2',
            'logo' => 'nullable|mimes:png,svg',
        ]);

        $data = $request->except('logo');

        // Set is_active to true by default
        $data['is_active'] = true;

        // Handle logo upload with compression
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($partnership->logo) {
                $this->imageService->deleteImage($partnership->logo);
            }

            $logoPath = $this->imageService->compressAndSave(
                $request->file('logo'),
                'partnerships',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['logo'] = $logoPath;
        }

        $partnership->update($data);

        return redirect()->route('admin.partnerships.index')
            ->with('success', 'Partnership updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Partnership  $partnership
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Partnership $partnership)
    {
        // Delete logo if exists
        if ($partnership->logo) {
            $this->imageService->deleteImage($partnership->logo);
        }

        $partnership->delete();

        return redirect()->route('admin.partnerships.index')
            ->with('success', 'Partnership deleted successfully.');
    }
}
