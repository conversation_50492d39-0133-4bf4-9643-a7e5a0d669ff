<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paints', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('title_en');
            $table->text('description')->nullable();
            $table->text('description_en')->nullable();
            $table->string('image');
            $table->string('artist')->nullable();
            $table->string('artist_en')->nullable();
            $table->integer('year')->nullable();
            $table->string('medium')->nullable();
            $table->string('medium_en')->nullable();
            $table->string('dimensions')->nullable();
            $table->foreignId('category_id')->nullable()->constrained('artwork_categories')->onDelete('set null');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paints');
    }
};
