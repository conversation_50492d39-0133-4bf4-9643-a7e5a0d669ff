<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WebsiteIdentityController extends Controller
{
    /**
     * The webhook service instance.
     *
     * @var \App\Services\WebhookService
     */
    protected $webhookService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        $this->webhookService = $webhookService;
    }

    /**
     * Display the website identity information.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show()
    {
        // Get all settings
        $settings = Setting::all()->groupBy('group');

        // Format the response
        $response = [
            'general' => $this->formatSettingsGroup($settings['general'] ?? []),
            'contact' => $this->formatSettingsGroup($settings['contact'] ?? []),
            'social' => $this->formatSettingsGroup($settings['social'] ?? []),
            'telegram' => $this->formatSettingsGroup($settings['telegram'] ?? []),
        ];

        return response()->json([
            'success' => true,
            'data' => $response,
        ]);
    }

    /**
     * Update the website identity.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'string|max:255',
            'site_name_en' => 'nullable|string|max:255',
            'institution_name' => 'nullable|string|max:255',
            'institution_name_en' => 'nullable|string|max:255',
            'site_description' => 'nullable|string',
            'site_description_en' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'domain' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'address_en' => 'nullable|string',
            'map_url' => 'nullable|string',
            'facebook' => 'nullable|string|max:255',
            'twitter' => 'nullable|string|max:255',
            'instagram' => 'nullable|string|max:255',
            'youtube' => 'nullable|string|max:255',
            'whatsapp' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'meta_description_en' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'meta_keywords_en' => 'nullable|string',
            'google_analytics' => 'nullable|string|max:255',
            'telegram_id' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get all form data
        $data = $request->all();
        $updatedSettings = [];

        // Update or create settings for all fields
        foreach ($data as $key => $value) {
            // Determine the group based on the key
            $group = $this->determineSettingGroup($key);

            $setting = Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'group' => $group
                ]
            );

            $updatedSettings[$key] = $value;
        }

        // Webhook event for website.identity.updated has been removed

        return response()->json([
            'success' => true,
            'message' => 'Website identity updated successfully',
            'data' => $updatedSettings,
        ]);
    }

    /**
     * Format a settings group for the response.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $settings
     * @return array
     */
    protected function formatSettingsGroup($settings)
    {
        $result = [];

        foreach ($settings as $setting) {
            $result[$setting->key] = $setting->value;
        }

        return $result;
    }

    /**
     * Determine the setting group based on the key.
     *
     * @param  string  $key
     * @return string
     */
    protected function determineSettingGroup($key)
    {
        if (in_array($key, ['address', 'phone', 'email', 'map_url'])) {
            return 'contact';
        }

        if (in_array($key, ['facebook', 'twitter', 'instagram', 'youtube', 'whatsapp'])) {
            return 'social';
        }

        if (in_array($key, ['telegram_id'])) {
            return 'telegram';
        }

        return 'general';
    }
}
