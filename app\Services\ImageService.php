<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Exceptions\DecoderException;

class ImageService
{
    /**
     * The disk to use for storage.
     *
     * @var string
     */
    protected $disk = 'public';

    /**
     * Allowed image extensions.
     *
     * @var array
     */
    protected $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];

    /**
     * Compress and save an image
     *
     * @param UploadedFile $image
     * @param string $path
     * @param int $quality
     * @param int|null $maxWidth
     * @param int|null $maxHeight
     * @return string|null
     */
    public function compressAndSave(UploadedFile $image, string $path, int $quality = 80, ?int $maxWidth = 1920, ?int $maxHeight = 1080): ?string
    {
        try {
            // Create a unique filename
            $filename = pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = strtolower($image->getClientOriginalExtension());

            // Validate extension
            if (!in_array($extension, $this->allowedExtensions)) {
                Log::warning("Invalid image extension: {$extension}");
                return null;
            }

            // Generate a unique filename with timestamp
            $storedFilename = $this->sanitizeFilename($filename) . '_' . time() . '.' . $extension;
            $fullPath = $path . '/' . $storedFilename;

            // Make sure the directory exists
            $directory = storage_path('app/public/' . $path);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
                Log::info("Created directory: {$directory}");
            }

            // Special handling for SVG files - store them directly without processing
            if ($extension === 'svg') {
                Storage::disk($this->disk)->put($fullPath, file_get_contents($image->getRealPath()));

                // Also copy to public directory to ensure it's accessible
                $publicPath = public_path('storage/' . $fullPath);
                $publicDir = dirname($publicPath);

                if (!file_exists($publicDir)) {
                    mkdir($publicDir, 0755, true);
                    Log::info("Created public directory: {$publicDir}");
                }

                // Copy the file to public path
                $storagePath = storage_path('app/public/' . $fullPath);
                if (file_exists($storagePath)) {
                    copy($storagePath, $publicPath);
                    Log::info("Copied file from {$storagePath} to {$publicPath}");
                }

                return $fullPath;
            }

            // For PNG files, use Intervention Image but preserve quality and transparency
            if ($extension === 'png') {
                return $this->processPngImage($image, $fullPath, $maxWidth, $maxHeight);
            }

            // For WebP files
            if ($extension === 'webp') {
                return $this->processWebpImage($image, $fullPath, $quality, $maxWidth, $maxHeight);
            }

            // For JPEG and other formats
            return $this->processJpegImage($image, $fullPath, $quality, $maxWidth, $maxHeight);
        } catch (DecoderException $e) {
            Log::error("Failed to decode image: " . $e->getMessage());
            return null;
        } catch (\Exception $e) {
            Log::error("Failed to process image: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Process a PNG image
     *
     * @param UploadedFile $image
     * @param string $fullPath
     * @param int|null $maxWidth
     * @param int|null $maxHeight
     * @return string
     */
    protected function processPngImage(UploadedFile $image, string $fullPath, ?int $maxWidth, ?int $maxHeight): string
    {
        // Create image manager with GD driver
        $manager = new ImageManager(new Driver());

        // Load the image
        $img = $manager->read($image->getRealPath());

        // Resize the image if it exceeds max dimensions while maintaining aspect ratio
        $width = $img->width();
        $height = $img->height();

        if ($maxWidth && $maxHeight && ($width > $maxWidth || $height > $maxHeight)) {
            $img = $img->scale(width: $maxWidth, height: $maxHeight);
        }

        // Save as PNG to preserve transparency
        $encodedImage = $img->toPng();

        // Save the image
        Storage::disk($this->disk)->put($fullPath, $encodedImage);

        return $fullPath;
    }

    /**
     * Process a WebP image
     *
     * @param UploadedFile $image
     * @param string $fullPath
     * @param int $quality
     * @param int|null $maxWidth
     * @param int|null $maxHeight
     * @return string
     */
    protected function processWebpImage(UploadedFile $image, string $fullPath, int $quality, ?int $maxWidth, ?int $maxHeight): string
    {
        $manager = new ImageManager(new Driver());
        $img = $manager->read($image->getRealPath());

        // Resize if needed
        $width = $img->width();
        $height = $img->height();

        if ($maxWidth && $maxHeight && ($width > $maxWidth || $height > $maxHeight)) {
            $img = $img->scale(width: $maxWidth, height: $maxHeight);
        }

        // Save as WebP
        $encodedImage = $img->toWebp($quality);

        // Save the image
        Storage::disk($this->disk)->put($fullPath, $encodedImage);

        // Also copy to public directory to ensure it's accessible
        $publicPath = public_path('storage/' . $fullPath);
        $publicDir = dirname($publicPath);

        if (!file_exists($publicDir)) {
            mkdir($publicDir, 0755, true);
            Log::info("Created public directory: {$publicDir}");
        }

        // Copy the file to public path
        $storagePath = storage_path('app/public/' . $fullPath);
        if (file_exists($storagePath)) {
            copy($storagePath, $publicPath);
            Log::info("Copied file from {$storagePath} to {$publicPath}");
        }

        return $fullPath;
    }

    /**
     * Process a JPEG image
     *
     * @param UploadedFile $image
     * @param string $fullPath
     * @param int $quality
     * @param int|null $maxWidth
     * @param int|null $maxHeight
     * @return string
     */
    protected function processJpegImage(UploadedFile $image, string $fullPath, int $quality, ?int $maxWidth, ?int $maxHeight): string
    {
        $manager = new ImageManager(new Driver());
        $img = $manager->read($image->getRealPath());

        // Resize if needed
        $width = $img->width();
        $height = $img->height();

        if ($maxWidth && $maxHeight && ($width > $maxWidth || $height > $maxHeight)) {
            $img = $img->scale(width: $maxWidth, height: $maxHeight);
        }

        // Convert to JPEG for other formats
        $encodedImage = $img->toJpeg($quality);

        // Save the compressed image
        Storage::disk($this->disk)->put($fullPath, $encodedImage);

        // Also copy to public directory to ensure it's accessible
        $publicPath = public_path('storage/' . $fullPath);
        $publicDir = dirname($publicPath);

        if (!file_exists($publicDir)) {
            mkdir($publicDir, 0755, true);
            Log::info("Created public directory: {$publicDir}");
        }

        // Copy the file to public path
        $storagePath = storage_path('app/public/' . $fullPath);
        if (file_exists($storagePath)) {
            copy($storagePath, $publicPath);
            Log::info("Copied file from {$storagePath} to {$publicPath}");
        }

        return $fullPath;
    }

    /**
     * Delete an image from storage
     *
     * @param string|null $path
     * @return bool
     */
    public function deleteImage(?string $path): bool
    {
        if (!$path) {
            Log::info("No image path provided for deletion");
            return false;
        }

        try {
            // Check if file exists in storage
            if (!Storage::disk($this->disk)->exists($path)) {
                Log::warning("Image not found in storage: {$path}");
                return false;
            }

            // Delete from storage disk
            $result = Storage::disk($this->disk)->delete($path);

            if ($result) {
                Log::info("Successfully deleted image from storage: {$path}");
            } else {
                Log::warning("Failed to delete image from storage: {$path}");
            }

            // Also delete from public directory if it exists
            $publicPath = public_path('storage/' . $path);
            if (file_exists($publicPath)) {
                if (unlink($publicPath)) {
                    Log::info("Successfully deleted image from public directory: {$publicPath}");
                } else {
                    Log::warning("Failed to delete image from public directory: {$publicPath}");
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error("Failed to delete image {$path}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Save a favicon image
     *
     * @param UploadedFile $image
     * @param string $path
     * @return string|null
     */
    public function saveFavicon(UploadedFile $image, string $path): ?string
    {
        try {
            // Create a unique filename
            $filename = pathinfo($image->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = strtolower($image->getClientOriginalExtension());
            $storedFilename = $this->sanitizeFilename($filename) . '_' . time() . '.' . $extension;
            $fullPath = $path . '/' . $storedFilename;

            // Create image manager
            $manager = new ImageManager(new Driver());

            // Load and resize the image
            $img = $manager->read($image->getRealPath());
            $img = $img->resize(32, 32);

            // Always save as PNG to preserve quality and transparency
            $encodedImage = $img->toPng();

            // Save the compressed image
            Storage::disk($this->disk)->put($fullPath, $encodedImage);

            // Also copy to public directory to ensure it's accessible
            $publicPath = public_path('storage/' . $fullPath);
            $publicDir = dirname($publicPath);

            if (!file_exists($publicDir)) {
                mkdir($publicDir, 0755, true);
                Log::info("Created public directory: {$publicDir}");
            }

            // Copy the file to public path
            $storagePath = storage_path('app/public/' . $fullPath);
            if (file_exists($storagePath)) {
                copy($storagePath, $publicPath);
                Log::info("Copied file from {$storagePath} to {$publicPath}");
            }

            return $fullPath;
        } catch (\Exception $e) {
            Log::error("Failed to save favicon: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a thumbnail from an existing image
     *
     * @param string $imagePath
     * @param string $thumbnailPath
     * @param int $width
     * @param int $height
     * @param int $quality
     * @return string|null
     */
    public function createThumbnail(string $imagePath, string $thumbnailPath, int $width = 300, int $height = 300, int $quality = 80): ?string
    {
        try {
            if (!Storage::disk($this->disk)->exists($imagePath)) {
                return null;
            }

            $imageContent = Storage::disk($this->disk)->get($imagePath);
            $extension = pathinfo($imagePath, PATHINFO_EXTENSION);

            // Generate thumbnail filename
            $filename = pathinfo($imagePath, PATHINFO_FILENAME);
            $thumbnailFilename = $this->sanitizeFilename($filename) . '_thumb_' . time() . '.' . $extension;
            $fullThumbnailPath = $thumbnailPath . '/' . $thumbnailFilename;

            // Create image manager
            $manager = new ImageManager(new Driver());
            $img = $manager->read($imageContent);

            // Resize to thumbnail dimensions
            $img = $img->resize($width, $height);

            // Save based on extension
            if ($extension === 'png') {
                $encodedImage = $img->toPng();
            } elseif ($extension === 'webp') {
                $encodedImage = $img->toWebp($quality);
            } else {
                $encodedImage = $img->toJpeg($quality);
            }

            // Save the thumbnail
            Storage::disk($this->disk)->put($fullThumbnailPath, $encodedImage);

            // Also copy to public directory to ensure it's accessible
            $publicPath = public_path('storage/' . $fullThumbnailPath);
            $publicDir = dirname($publicPath);

            if (!file_exists($publicDir)) {
                mkdir($publicDir, 0755, true);
                Log::info("Created public directory: {$publicDir}");
            }

            // Copy the file to public path
            $storagePath = storage_path('app/public/' . $fullThumbnailPath);
            if (file_exists($storagePath)) {
                copy($storagePath, $publicPath);
                Log::info("Copied file from {$storagePath} to {$publicPath}");
            }

            return $fullThumbnailPath;
        } catch (\Exception $e) {
            Log::error("Failed to create thumbnail: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the full URL for an image
     *
     * @param string|null $path
     * @return string|null
     */
    public function getImageUrl(?string $path): ?string
    {
        if (!$path) {
            return null;
        }

        try {
            return Storage::disk($this->disk)->url($path);
        } catch (\Exception $e) {
            Log::error("Failed to get image URL for {$path}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Sanitize a filename to remove special characters
     *
     * @param string $filename
     * @return string
     */
    protected function sanitizeFilename(string $filename): string
    {
        // Remove any character that is not a letter, number, dash, or underscore
        $filename = preg_replace('/[^\w\-]/', '', $filename);

        // Ensure the filename is not empty
        if (empty($filename)) {
            $filename = 'image';
        }

        return $filename;
    }
}

