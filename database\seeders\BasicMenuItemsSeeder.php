<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BasicMenuItemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing menu items
        DB::table('menu_items')->truncate();

        // Create a few basic menu items
        DB::table('menu_items')->insert([
            [
                'title' => 'Beranda',
                'title_en' => 'Home',
                'route_name' => 'home',
                'url' => null,
                'target' => '_self',
                'parent_id' => null,
                'order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Profil',
                'title_en' => 'Profile',
                'route_name' => null,
                'url' => '#',
                'target' => '_self',
                'parent_id' => null,
                'order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Kontak',
                'title_en' => 'Contact',
                'route_name' => 'contact',
                'url' => null,
                'target' => '_self',
                'parent_id' => null,
                'order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
