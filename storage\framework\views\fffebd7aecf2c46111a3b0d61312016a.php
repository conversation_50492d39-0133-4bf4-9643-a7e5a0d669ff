<?php $__env->startSection('title', 'Curriculum'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Curriculum</h1>
        <a href="<?php echo e(route('admin.curricula.create')); ?>" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link <?php echo e($activeTab == 'formal' ? 'active' : ''); ?>" href="<?php echo e(route('admin.curricula.index', ['tab' => 'formal'])); ?>">
                        Formal
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo e($activeTab == 'non-formal' ? 'active' : ''); ?>" href="<?php echo e(route('admin.curricula.index', ['tab' => 'non-formal'])); ?>">
                        Non-Formal
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <?php if($activeTab == 'formal'): ?>
                                <th>Education Unit</th>
                            <?php endif; ?>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $curricula; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $curriculum): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($curriculum->title); ?></td>
                                <?php if($activeTab == 'formal'): ?>
                                    <td><?php echo e($curriculum->educationUnit->name ?? 'N/A'); ?></td>
                                <?php endif; ?>
                                <td><?php echo e($curriculum->order); ?></td>
                                <td>
                                    <?php if($curriculum->is_active): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('admin.curricula.show', $curriculum)); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="<?php echo e(route('admin.curricula.edit', $curriculum)); ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.curricula.destroy', $curriculum)); ?>" method="POST" class="d-inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this item?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="<?php echo e($activeTab == 'formal' ? 6 : 5); ?>" class="text-center">No curriculum items found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/curricula/index.blade.php ENDPATH**/ ?>