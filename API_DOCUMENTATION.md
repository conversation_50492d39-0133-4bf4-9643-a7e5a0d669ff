# Nurul Hayah API Documentation

This document provides information about the API endpoints and webhook system for Nurul Hayah website.

## Authentication

The API uses Laravel Sanctum for authentication. To authenticate, you need to obtain an API token by logging in.

### Login

```
POST /api/v1/login
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "your-password"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>",
            "role": "admin",
            ...
        },
        "token": "your-api-token"
    }
}
```

### Using the Token

Include the token in the Authorization header for all authenticated requests:

```
Authorization: Bearer your-api-token
```

## API Endpoints

### Public Endpoints

These endpoints are accessible without authentication:

#### News

- `GET /api/v1/news` - Get a list of published news
- `GET /api/v1/news/{id}` - Get a specific news article
- `GET /api/v1/news/categories` - Get a list of news categories

#### Galleries

- `GET /api/v1/galleries` - Get a list of galleries
- `GET /api/v1/galleries/{id}` - Get a specific gallery

#### Facilities

- `GET /api/v1/facilities` - Get a list of facilities
- `GET /api/v1/facilities/{id}` - Get a specific facility

#### Programs

- `GET /api/v1/programs` - Get a list of programs
- `GET /api/v1/programs/{id}` - Get a specific program

#### Announcements

- `GET /api/v1/announcements` - Get a list of announcements
- `GET /api/v1/announcements/{id}` - Get a specific announcement

#### Agendas

- `GET /api/v1/agendas` - Get a list of agendas
- `GET /api/v1/agendas/{id}` - Get a specific agenda

#### Academic Calendars

- `GET /api/v1/academic-calendars` - Get a list of academic calendars
- `GET /api/v1/academic-calendars/{id}` - Get a specific academic calendar

#### Activity Schedules

- `GET /api/v1/activity-schedules` - Get a list of activity schedules
- `GET /api/v1/activity-schedules/{id}` - Get a specific activity schedule

#### Achievements

- `GET /api/v1/achievements` - Get a list of achievements
- `GET /api/v1/achievements/{id}` - Get a specific achievement

#### Leaders

- `GET /api/v1/leaders` - Get a list of leaders
- `GET /api/v1/leaders/{id}` - Get a specific leader

#### Education Units

- `GET /api/v1/education-units` - Get a list of education units
- `GET /api/v1/education-units/{id}` - Get a specific education unit

#### Partnerships

- `GET /api/v1/partnerships` - Get a list of partnerships
- `GET /api/v1/partnerships/{id}` - Get a specific partnership

#### Website Identity

- `GET /api/v1/website-identity` - Get website identity information
- `PUT /api/v1/website-identity` - Update website identity information

**GET Response Example:**
```json
{
    "success": true,
    "data": {
        "general": {
            "site_name": "Nurul Hayah 4",
            "site_description": "Website resmi Nurul Hayah 4",
            "logo": "website/logo.png",
            "favicon": "website/favicon.ico"
        },
        "contact": {
            "address": "Jl. Pesantren No. 123, Kota Jakarta, Indonesia",
            "phone": "+62 123 4567 890",
            "email": "<EMAIL>",
            "map_url": "https://www.google.com/maps/embed?..."
        },
        "social": {
            "facebook": "https://facebook.com/NUHAtv",
            "instagram": "https://instagram.com/NUHAtv",
            "twitter": "#",
            "youtube": "https://youtube.com/NUHAtv",
            "whatsapp": "+628818866831"
        },
        "telegram": {
            "telegram_id": "123456789"
        }
    }
}
```

**PUT Request Example:**
```json
{
    "telegram_id": "123456789"
}
```

#### Swipers

- `GET /api/v1/swipers` - Get a list of swipers

#### Menus

- `GET /api/v1/menus` - Get a list of menus

#### Pages

- `GET /api/v1/pages` - Get a list of pages
- `GET /api/v1/pages/{slug}` - Get a specific page

#### Videos

- `GET /api/v1/videos` - Get a list of videos
- `GET /api/v1/videos/{id}` - Get a specific video

#### Contact Form

- `POST /api/v1/contacts` - Submit a contact form

#### Registration Form

- `POST /api/v1/registrations` - Submit a registration form

#### Webhook Management

- `GET /api/v1/webhooks` - Get a list of webhooks
- `POST /api/v1/webhooks` - Create a new webhook
- `GET /api/v1/webhooks/{id}` - Get a specific webhook
- `PUT /api/v1/webhooks/{id}` - Update a webhook
- `DELETE /api/v1/webhooks/{id}` - Delete a webhook
- `POST /api/v1/webhooks/{id}/test` - Test a webhook

### Protected Endpoints

These endpoints require authentication:

#### User Profile

- `GET /api/v1/user` - Get the authenticated user's profile
- `POST /api/v1/logout` - Logout (revoke token)

### Admin Endpoints

These endpoints require admin role:

#### Users Management

- `GET /api/v1/users` - Get a list of users
- `POST /api/v1/users` - Create a new user
- `GET /api/v1/users/{id}` - Get a specific user
- `PUT /api/v1/users/{id}` - Update a user
- `DELETE /api/v1/users/{id}` - Delete a user

#### Director's Insight

- `GET /api/v1/directors-insights` - Get a list of director's insights
- `POST /api/v1/directors-insights` - Create a new director's insight
- `GET /api/v1/directors-insights/{id}` - Get a specific director's insight
- `PUT /api/v1/directors-insights/{id}` - Update a director's insight
- `DELETE /api/v1/directors-insights/{id}` - Delete a director's insight

### Operator Endpoints

These endpoints require operator role:

#### Content Management

- Various CRUD operations for news, galleries, facilities, programs, etc.

#### Registration Management

- `GET /api/v1/registrations` - Get a list of registrations
- `GET /api/v1/registrations/{id}` - Get a specific registration
- `PUT /api/v1/registrations/{id}` - Update a registration
- `DELETE /api/v1/registrations/{id}` - Delete a registration
- `POST /api/v1/registrations/{id}/approve` - Approve a registration
- `POST /api/v1/registrations/{id}/reject` - Reject a registration
- `GET /api/v1/registrations/export` - Export registrations to Excel

#### Contact Management

- `GET /api/v1/contacts` - Get a list of contacts
- `GET /api/v1/contacts/{id}` - Get a specific contact
- `PUT /api/v1/contacts/{id}` - Update a contact
- `DELETE /api/v1/contacts/{id}` - Delete a contact
- `POST /api/v1/contacts/{id}/mark-as-read` - Mark a contact as read
- `POST /api/v1/contacts/{id}/reply` - Reply to a contact

## Webhook System

The webhook system allows external applications to receive notifications when certain events occur in the Nurul Hayah system.

### Available Events

- `news.created` - When a news article is created
- `news.updated` - When a news article is updated
- `news.deleted` - When a news article is deleted
- `registration.created` - When a registration is submitted
- `registration.approved` - When a registration is approved
- `registration.rejected` - When a registration is rejected
- `contact.created` - When a contact form is submitted
- `contact.read` - When a contact is marked as read
- `contact.replied` - When a contact is replied to
- `announcement.created` - When an announcement is created
- `announcement.updated` - When an announcement is updated
- `announcement.deleted` - When an announcement is deleted
- `website.identity.updated` - When website identity information is updated (including Telegram ID)

### Creating a Webhook

To create a webhook, send a POST request to `/api/v1/webhooks` with the following data:

```json
{
    "name": "My Webhook",
    "url": "https://example.com/webhook",
    "event": "news.created",
    "secret": "your-secret-key",
    "headers": {
        "X-Custom-Header": "Custom Value"
    },
    "payload_template": {
        "custom_field": "custom_value"
    }
}
```

### Webhook Payload

When an event occurs, the webhook will send a POST request to the specified URL with the following payload:

```json
{
    "event": "news.created",
    "triggered_at": "2025-05-08T12:34:56+00:00",
    "data": {
        // Event-specific data
    }
}
```

**Example for `website.identity.updated` event:**
```json
{
    "event": "website.identity.updated",
    "triggered_at": "2025-05-08T12:34:56+00:00",
    "data": {
        "telegram_id": "123456789"
    }
}
```

### Webhook Security

If a secret is provided when creating the webhook, the request will include a signature in the `X-Webhook-Signature` header. The signature is generated using HMAC SHA-256 with the secret as the key and the JSON payload as the message.

To verify the signature:

```php
$signature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'];
$payload = file_get_contents('php://input');
$calculatedSignature = hash_hmac('sha256', $payload, 'your-secret-key');

if (hash_equals($signature, $calculatedSignature)) {
    // Signature is valid
} else {
    // Signature is invalid
}
```
