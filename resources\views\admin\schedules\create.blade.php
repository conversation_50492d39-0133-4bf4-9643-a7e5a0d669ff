@extends('admin.layouts.app')

@section('title', 'Create Activity Schedule')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Create Activity Schedule</h1>
        <a href="{{ route('admin.schedules.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.schedules.store') }}" method="POST">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="activity_type" class="form-label">Activity Type <span class="text-danger">*</span></label>
                        <select class="form-select @error('activity_type') is-invalid @enderror" id="activity_type" name="activity_type" required>
                            <option value="">-- Select Activity Type --</option>
                            <option value="daily" {{ old('activity_type') == 'daily' ? 'selected' : '' }}>Daily</option>
                            <option value="weekly" {{ old('activity_type') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                            <option value="monthly" {{ old('activity_type') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                            <option value="yearly" {{ old('activity_type') == 'yearly' ? 'selected' : '' }}>Yearly</option>
                        </select>
                        @error('activity_type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="title" class="form-label">Title (Indonesian) <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="title_en" class="form-label">Title (English) <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title_en') is-invalid @enderror" id="title_en" name="title_en" value="{{ old('title_en') }}" required>
                        @error('title_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="description" class="form-label">Description (Indonesian)</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="description_en" class="form-label">Description (English)</label>
                        <textarea class="form-control @error('description_en') is-invalid @enderror" id="description_en" name="description_en" rows="3">{{ old('description_en') }}</textarea>
                        @error('description_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>



                <!-- Weekly options -->
                <div class="row mb-3" id="weekly-options" style="display: none;">
                    <div class="col-md-12">
                        <label for="day_of_week" class="form-label">Day of Week <span class="text-danger">*</span></label>
                        <select class="form-select @error('day_of_week') is-invalid @enderror" id="day_of_week" name="day_of_week">
                            <option value="">-- Select Day --</option>
                            <option value="Saturday" {{ old('day_of_week') == 'Saturday' ? 'selected' : '' }}>Saturday</option>
                            <option value="Sunday" {{ old('day_of_week') == 'Sunday' ? 'selected' : '' }}>Sunday</option>
                            <option value="Monday" {{ old('day_of_week') == 'Monday' ? 'selected' : '' }}>Monday</option>
                            <option value="Tuesday" {{ old('day_of_week') == 'Tuesday' ? 'selected' : '' }}>Tuesday</option>
                            <option value="Wednesday" {{ old('day_of_week') == 'Wednesday' ? 'selected' : '' }}>Wednesday</option>
                            <option value="Thursday" {{ old('day_of_week') == 'Thursday' ? 'selected' : '' }}>Thursday</option>
                            <option value="Friday" {{ old('day_of_week') == 'Friday' ? 'selected' : '' }}>Friday</option>
                        </select>
                        @error('day_of_week')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Monthly options -->
                <div class="row mb-3" id="monthly-options" style="display: none;">
                    <div class="col-md-12">
                        <label for="week_number" class="form-label">Week Number <span class="text-danger">*</span></label>
                        <select class="form-select @error('week_number') is-invalid @enderror" id="week_number" name="week_number">
                            <option value="">-- Select Week --</option>
                            <option value="1" {{ old('week_number') == '1' ? 'selected' : '' }}>Week 1</option>
                            <option value="2" {{ old('week_number') == '2' ? 'selected' : '' }}>Week 2</option>
                            <option value="3" {{ old('week_number') == '3' ? 'selected' : '' }}>Week 3</option>
                            <option value="4" {{ old('week_number') == '4' ? 'selected' : '' }}>Week 4</option>
                        </select>
                        @error('week_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Yearly options -->
                <div class="row mb-3" id="yearly-options" style="display: none;">
                    <div class="col-md-12">
                        <label for="month_number" class="form-label">Month <span class="text-danger">*</span></label>
                        <select class="form-select @error('month_number') is-invalid @enderror" id="month_number" name="month_number">
                            <option value="">-- Select Month --</option>
                            <option value="1" {{ old('month_number') == '1' ? 'selected' : '' }}>January</option>
                            <option value="2" {{ old('month_number') == '2' ? 'selected' : '' }}>February</option>
                            <option value="3" {{ old('month_number') == '3' ? 'selected' : '' }}>March</option>
                            <option value="4" {{ old('month_number') == '4' ? 'selected' : '' }}>April</option>
                            <option value="5" {{ old('month_number') == '5' ? 'selected' : '' }}>May</option>
                            <option value="6" {{ old('month_number') == '6' ? 'selected' : '' }}>June</option>
                            <option value="7" {{ old('month_number') == '7' ? 'selected' : '' }}>July</option>
                            <option value="8" {{ old('month_number') == '8' ? 'selected' : '' }}>August</option>
                            <option value="9" {{ old('month_number') == '9' ? 'selected' : '' }}>September</option>
                            <option value="10" {{ old('month_number') == '10' ? 'selected' : '' }}>October</option>
                            <option value="11" {{ old('month_number') == '11' ? 'selected' : '' }}>November</option>
                            <option value="12" {{ old('month_number') == '12' ? 'selected' : '' }}>December</option>
                        </select>
                        @error('month_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="start_time" class="form-label">Start Time <span class="text-danger">*</span></label>
                        <input type="time" class="form-control @error('start_time') is-invalid @enderror" id="start_time" name="start_time" value="{{ old('start_time') }}" required>
                        @error('start_time')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="end_time" class="form-label">End Time <span class="text-danger">*</span></label>
                        <input type="time" class="form-control @error('end_time') is-invalid @enderror" id="end_time" name="end_time" value="{{ old('end_time') }}" required>
                        @error('end_time')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="location" class="form-label">Location (Indonesian)</label>
                        <input type="text" class="form-control @error('location') is-invalid @enderror" id="location" name="location" value="{{ old('location') }}">
                        @error('location')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="location_en" class="form-label">Location (English)</label>
                        <input type="text" class="form-control @error('location_en') is-invalid @enderror" id="location_en" name="location_en" value="{{ old('location_en') }}">
                        @error('location_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="instructor" class="form-label">Instructor (Indonesian)</label>
                        <input type="text" class="form-control @error('instructor') is-invalid @enderror" id="instructor" name="instructor" value="{{ old('instructor') }}">
                        @error('instructor')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="instructor_en" class="form-label">Instructor (English)</label>
                        <input type="text" class="form-control @error('instructor_en') is-invalid @enderror" id="instructor_en" name="instructor_en" value="{{ old('instructor_en') }}">
                        @error('instructor_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                            <option value="">-- Select Category --</option>
                            <option value="academic" {{ old('category') == 'academic' ? 'selected' : '' }}>Academic</option>
                            <option value="extracurricular" {{ old('category') == 'extracurricular' ? 'selected' : '' }}>Extracurricular</option>
                            <option value="religious" {{ old('category') == 'religious' ? 'selected' : '' }}>Religious</option>
                            <option value="sports" {{ old('category') == 'sports' ? 'selected' : '' }}>Sports</option>
                            <option value="arts" {{ old('category') == 'arts' ? 'selected' : '' }}>Arts</option>
                            <option value="other" {{ old('category') == 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('category')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="order" class="form-label">Display Order</label>
                        <input type="number" class="form-control @error('order') is-invalid @enderror" id="order" name="order" value="{{ old('order') }}">
                        @error('order')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>



                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const activityTypeSelect = document.getElementById('activity_type');
        const weeklyOptions = document.getElementById('weekly-options');
        const monthlyOptions = document.getElementById('monthly-options');
        const yearlyOptions = document.getElementById('yearly-options');
        const dayOfWeekSelect = document.getElementById('day_of_week');
        const weekNumberSelect = document.getElementById('week_number');
        const monthNumberSelect = document.getElementById('month_number');

        // Initial state based on old input
        updateFormFields();

        // Update form fields when activity type changes
        activityTypeSelect.addEventListener('change', updateFormFields);

        function updateFormFields() {
            const activityType = activityTypeSelect.value;

            // Hide all option divs first
            weeklyOptions.style.display = 'none';
            monthlyOptions.style.display = 'none';
            yearlyOptions.style.display = 'none';

            // Reset required attributes
            dayOfWeekSelect.removeAttribute('required');
            weekNumberSelect.removeAttribute('required');
            monthNumberSelect.removeAttribute('required');

            // Show relevant options based on activity type
            if (activityType === 'weekly') {
                weeklyOptions.style.display = 'block';
                dayOfWeekSelect.setAttribute('required', 'required');
            } else if (activityType === 'monthly') {
                monthlyOptions.style.display = 'block';
                weekNumberSelect.setAttribute('required', 'required');
                // Clear day_of_week for monthly activities
                dayOfWeekSelect.value = '';
            } else if (activityType === 'yearly') {
                yearlyOptions.style.display = 'block';
                monthNumberSelect.setAttribute('required', 'required');
                // Clear day_of_week for yearly activities
                dayOfWeekSelect.value = '';
            } else if (activityType === 'daily') {
                // For daily activities, we'll use a default value in the controller
                dayOfWeekSelect.value = '';
            }
        }
    });
</script>
@endpush
