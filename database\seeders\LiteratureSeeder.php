<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Literature;
use App\Models\LitType;
use Illuminate\Support\Str;

class LiteratureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, check if we already have literature types
        $existingTypes = LitType::pluck('slug')->toArray();

        if (empty($existingTypes)) {
            // Create literature types if they don't exist
            $types = [
                [
                    'name' => 'Puisi',
                    'name_en' => 'Poetry',
                    'slug' => 'puisi',
                    'slug_en' => 'poetry',
                    'description' => 'Karya sastra yang menggunakan kata-kata indah dan kaya makna',
                    'description_en' => 'Literary work using beautiful and meaningful words',
                ],
                [
                    'name' => 'Cerpen',
                    'name_en' => 'Short Story',
                    'slug' => 'cerpen',
                    'slug_en' => 'short-story',
                    'description' => 'Cerita pendek yang dapat dibaca dalam sekali duduk',
                    'description_en' => 'Short fiction that can be read in one sitting',
                ],
                [
                    'name' => 'Esai',
                    'name_en' => 'Essay',
                    'slug' => 'esai',
                    'slug_en' => 'essay',
                    'description' => 'Tulisan yang mengungkapkan opini atau pandangan penulis',
                    'description_en' => 'Writing that expresses the author\'s opinion or perspective',
                ],
                [
                    'name' => 'Artikel',
                    'name_en' => 'Article',
                    'slug' => 'artikel',
                    'slug_en' => 'article',
                    'description' => 'Tulisan faktual tentang suatu topik',
                    'description_en' => 'Factual writing about a specific topic',
                ],
            ];

            foreach ($types as $typeData) {
                LitType::create([
                    'name' => $typeData['name'],
                    'name_en' => $typeData['name_en'],
                    'slug' => $typeData['slug'],
                    'slug_en' => $typeData['slug_en'],
                    'description' => $typeData['description'],
                    'description_en' => $typeData['description_en'],
                    'is_active' => true,
                ]);
            }
        }

        // Create 10 placeholder literature entries
        $literatureData = [
            // Poetry
            [
                'title' => 'Hujan di Pagi Hari',
                'title_en' => 'Morning Rain',
                'content' => '<p>Hujan turun di pagi hari<br>Membasahi bumi yang kering<br>Menghapus debu dan lelah<br>Membawa kesejukan dan harapan baru</p>',
                'content_en' => '<p>Rain falls in the morning<br>Wetting the dry earth<br>Erasing dust and fatigue<br>Bringing coolness and new hope</p>',
                'author' => 'Ahmad Tohari',
                'author_en' => 'Ahmad Tohari',
                'year' => 2020,
                'type' => 'puisi',
                'type_en' => 'poetry',
            ],
            [
                'title' => 'Senja dan Kenangan',
                'title_en' => 'Dusk and Memories',
                'content' => '<p>Senja menyapa dengan lembut<br>Membawa kenangan masa lalu<br>Tentang cinta yang pernah ada<br>Dan waktu yang terus berlalu</p>',
                'content_en' => '<p>Dusk greets gently<br>Bringing memories of the past<br>About love that once existed<br>And time that keeps passing</p>',
                'author' => 'Sapardi Djoko Damono',
                'author_en' => 'Sapardi Djoko Damono',
                'year' => 2019,
                'type' => 'puisi',
                'type_en' => 'poetry',
            ],

            // Short Stories
            [
                'title' => 'Pertemuan di Stasiun',
                'title_en' => 'Meeting at the Station',
                'content' => '<p>Stasiun itu selalu ramai di pagi hari. Orang-orang bergegas mengejar kereta, takut terlambat sampai di tempat tujuan. Di antara kerumunan itu, dua pasang mata bertemu. Sebuah pertemuan yang akan mengubah hidup mereka selamanya.</p><p>Andi tidak pernah menyangka bahwa hari itu akan menjadi hari yang spesial. Dia hanya menjalankan rutinitas seperti biasa, berangkat ke kantor dengan kereta pagi. Namun takdir berkata lain ketika dia tidak sengaja menabrak seorang wanita yang sedang membawa tumpukan buku.</p>',
                'content_en' => '<p>The station is always crowded in the morning. People rush to catch the train, afraid of being late to their destination. Among the crowd, two pairs of eyes meet. A meeting that will change their lives forever.</p><p>Andi never thought that day would be special. He was just carrying out his routine as usual, going to the office by morning train. But fate had other plans when he accidentally bumped into a woman carrying a stack of books.</p>',
                'author' => 'Dee Lestari',
                'author_en' => 'Dee Lestari',
                'year' => 2021,
                'type' => 'cerpen',
                'type_en' => 'short-story',
            ],
            [
                'title' => 'Rumah Tua di Ujung Desa',
                'title_en' => 'Old House at the Edge of the Village',
                'content' => '<p>Rumah tua itu berdiri kokoh di ujung desa. Sudah bertahun-tahun tidak ada yang berani mendekatinya. Cerita-cerita seram tentang rumah itu tersebar luas di kalangan penduduk desa. Namun, seorang anak kecil bernama Tono memiliki keberanian untuk mengungkap misteri di balik rumah tua tersebut.</p><p>Suatu sore, dengan berbekal senter kecil dan rasa penasaran yang besar, Tono melangkahkan kakinya menuju rumah tua itu. Jantungnya berdebar kencang, tapi tekadnya sudah bulat.</p>',
                'content_en' => '<p>The old house stood firmly at the edge of the village. For years, no one dared to approach it. Scary stories about the house were widespread among the villagers. However, a little boy named Tono had the courage to uncover the mystery behind the old house.</p><p>One afternoon, armed with a small flashlight and great curiosity, Tono stepped toward the old house. His heart was pounding, but his determination was unwavering.</p>',
                'author' => 'Eka Kurniawan',
                'author_en' => 'Eka Kurniawan',
                'year' => 2018,
                'type' => 'cerpen',
                'type_en' => 'short-story',
            ],

            // Essays
            [
                'title' => 'Pendidikan di Era Digital',
                'title_en' => 'Education in the Digital Era',
                'content' => '<p>Era digital telah mengubah banyak aspek kehidupan manusia, termasuk pendidikan. Metode pembelajaran tradisional mulai ditinggalkan dan digantikan dengan pendekatan yang lebih modern dan interaktif. Hal ini membawa tantangan sekaligus peluang bagi dunia pendidikan.</p><p>Teknologi digital memungkinkan akses terhadap informasi dan pengetahuan menjadi lebih mudah dan cepat. Siswa tidak lagi terbatas pada buku teks sebagai sumber belajar. Mereka dapat mengakses berbagai sumber dari internet, mengikuti kursus online, dan berinteraksi dengan siswa lain dari berbagai belahan dunia.</p>',
                'content_en' => '<p>The digital era has changed many aspects of human life, including education. Traditional learning methods are being abandoned and replaced with more modern and interactive approaches. This brings both challenges and opportunities for the world of education.</p><p>Digital technology allows access to information and knowledge to be easier and faster. Students are no longer limited to textbooks as learning resources. They can access various sources from the internet, take online courses, and interact with other students from various parts of the world.</p>',
                'author' => 'Anies Baswedan',
                'author_en' => 'Anies Baswedan',
                'year' => 2022,
                'type' => 'esai',
                'type_en' => 'essay',
            ],
            [
                'title' => 'Pentingnya Melestarikan Budaya Lokal',
                'title_en' => 'The Importance of Preserving Local Culture',
                'content' => '<p>Di tengah arus globalisasi yang semakin kuat, melestarikan budaya lokal menjadi tantangan tersendiri. Banyak generasi muda yang lebih tertarik dengan budaya asing dan melupakan kekayaan budaya sendiri. Padahal, budaya lokal merupakan identitas dan jati diri suatu bangsa.</p><p>Indonesia sebagai negara yang kaya akan keberagaman budaya, memiliki tanggung jawab besar untuk menjaga dan melestarikan warisan budaya tersebut. Setiap daerah memiliki keunikan dan kekhasan budaya yang patut dibanggakan dan diperkenalkan ke dunia internasional.</p>',
                'content_en' => '<p>Amid the increasingly strong current of globalization, preserving local culture becomes a challenge in itself. Many young people are more interested in foreign cultures and forget their own cultural wealth. In fact, local culture is the identity and character of a nation.</p><p>Indonesia, as a country rich in cultural diversity, has a great responsibility to maintain and preserve this cultural heritage. Each region has unique and distinctive cultures that deserve to be proud of and introduced to the international world.</p>',
                'author' => 'Mochtar Lubis',
                'author_en' => 'Mochtar Lubis',
                'year' => 2017,
                'type' => 'esai',
                'type_en' => 'essay',
            ],

            // Articles
            [
                'title' => 'Manfaat Olahraga Teratur bagi Kesehatan',
                'title_en' => 'Benefits of Regular Exercise for Health',
                'content' => '<p>Olahraga teratur memiliki banyak manfaat bagi kesehatan tubuh dan pikiran. Beberapa penelitian menunjukkan bahwa aktivitas fisik yang dilakukan secara rutin dapat mengurangi risiko penyakit jantung, stroke, diabetes, dan beberapa jenis kanker.</p><p>Selain itu, olahraga juga dapat meningkatkan mood dan mengurangi stres. Hal ini karena saat berolahraga, tubuh melepaskan endorfin, hormon yang membuat kita merasa bahagia dan rileks. Olahraga juga dapat meningkatkan kualitas tidur dan energi sepanjang hari.</p>',
                'content_en' => '<p>Regular exercise has many benefits for the health of the body and mind. Several studies show that physical activity performed regularly can reduce the risk of heart disease, stroke, diabetes, and certain types of cancer.</p><p>In addition, exercise can also improve mood and reduce stress. This is because during exercise, the body releases endorphins, hormones that make us feel happy and relaxed. Exercise can also improve sleep quality and energy throughout the day.</p>',
                'author' => 'Dr. Tirta Mandira Hudhi',
                'author_en' => 'Dr. Tirta Mandira Hudhi',
                'year' => 2023,
                'type' => 'artikel',
                'type_en' => 'article',
            ],
            [
                'title' => 'Dampak Media Sosial pada Kehidupan Sosial',
                'title_en' => 'Impact of Social Media on Social Life',
                'content' => '<p>Media sosial telah menjadi bagian tak terpisahkan dari kehidupan modern. Hampir setiap orang memiliki setidaknya satu akun media sosial yang digunakan untuk berkomunikasi, berbagi informasi, dan mengekspresikan diri. Namun, di balik kemudahan dan kesenangan yang ditawarkan, media sosial juga membawa dampak negatif pada kehidupan sosial.</p><p>Salah satu dampak negatif media sosial adalah menurunnya interaksi sosial secara langsung. Orang-orang lebih suka berkomunikasi melalui pesan teks daripada bertemu dan berbicara langsung. Hal ini dapat mengurangi kemampuan komunikasi interpersonal dan empati.</p>',
                'content_en' => '<p>Social media has become an inseparable part of modern life. Almost everyone has at least one social media account used to communicate, share information, and express themselves. However, behind the convenience and pleasure offered, social media also brings negative impacts on social life.</p><p>One of the negative impacts of social media is the decline in direct social interaction. People prefer to communicate via text messages rather than meet and talk directly. This can reduce interpersonal communication skills and empathy.</p>',
                'author' => 'Najwa Shihab',
                'author_en' => 'Najwa Shihab',
                'year' => 2022,
                'type' => 'artikel',
                'type_en' => 'article',
            ],
            [
                'title' => 'Peran Teknologi dalam Pendidikan Modern',
                'title_en' => 'The Role of Technology in Modern Education',
                'content' => '<p>Teknologi telah mengubah cara kita belajar dan mengajar. Dari penggunaan komputer dan internet hingga aplikasi pembelajaran dan kelas virtual, teknologi menawarkan berbagai alat yang dapat meningkatkan pengalaman pendidikan.</p><p>Salah satu manfaat utama teknologi dalam pendidikan adalah akses ke sumber daya yang lebih luas. Siswa dapat mengakses e-book, video pembelajaran, dan berbagai materi online yang mungkin tidak tersedia di perpustakaan sekolah mereka. Hal ini membuka pintu untuk eksplorasi dan pembelajaran mandiri.</p>',
                'content_en' => '<p>Technology has changed the way we learn and teach. From the use of computers and the internet to learning applications and virtual classrooms, technology offers various tools that can enhance the educational experience.</p><p>One of the main benefits of technology in education is access to wider resources. Students can access e-books, learning videos, and various online materials that may not be available in their school library. This opens the door for exploration and independent learning.</p>',
                'author' => 'Nadiem Makarim',
                'author_en' => 'Nadiem Makarim',
                'year' => 2021,
                'type' => 'artikel',
                'type_en' => 'article',
            ],
            [
                'title' => 'Pentingnya Literasi Digital di Era Informasi',
                'title_en' => 'The Importance of Digital Literacy in the Information Era',
                'content' => '<p>Di era informasi seperti sekarang, literasi digital menjadi keterampilan yang sangat penting. Literasi digital tidak hanya tentang kemampuan menggunakan teknologi, tetapi juga kemampuan untuk memahami, mengevaluasi, dan menggunakan informasi dari sumber digital secara efektif.</p><p>Dengan banjirnya informasi di internet, penting bagi kita untuk dapat membedakan antara informasi yang benar dan yang salah. Hoaks dan misinformasi tersebar luas di media sosial dan platform online lainnya. Tanpa literasi digital yang baik, kita bisa dengan mudah tertipu dan menyebarkan informasi yang tidak akurat.</p>',
                'content_en' => '<p>In the information era like now, digital literacy becomes a very important skill. Digital literacy is not just about the ability to use technology, but also the ability to understand, evaluate, and use information from digital sources effectively.</p><p>With the flood of information on the internet, it is important for us to be able to distinguish between true and false information. Hoaxes and misinformation are widespread on social media and other online platforms. Without good digital literacy, we can easily be deceived and spread inaccurate information.</p>',
                'author' => 'Onno W. Purbo',
                'author_en' => 'Onno W. Purbo',
                'year' => 2023,
                'type' => 'artikel',
                'type_en' => 'article',
            ],
        ];

        // Check if we already have literature entries
        $existingLiterature = Literature::count();

        if ($existingLiterature < 10) {
            foreach ($literatureData as $data) {
                // Check if this title already exists
                $exists = Literature::where('title', $data['title'])->exists();

                if (!$exists) {
                    Literature::create([
                        'title' => $data['title'],
                        'title_en' => $data['title_en'],
                        'content' => $data['content'],
                        'content_en' => $data['content_en'],
                        'author' => $data['author'],
                        'author_en' => $data['author_en'],
                        'year' => $data['year'],
                        'type' => $data['type'],
                        'type_en' => $data['type_en'],
                        'is_active' => true,
                    ]);
                }
            }
        }
    }
}
