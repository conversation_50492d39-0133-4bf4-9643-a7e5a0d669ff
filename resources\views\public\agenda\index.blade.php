@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Agenda' : 'Agenda')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Agenda' : 'Agenda' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Agenda' : 'Agenda' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Agenda List -->
    <section class="section-padding">
        <div class="container">
            <div class="row justify-content-center">
                @forelse($agendaItems as $agenda)
                    <div class="col-lg-6 mb-4 fade-in">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="agenda-date">
                                        <div class="day">{{ $agenda->date->format('d') }}</div>
                                        <div class="month">{{ $agenda->date->format('M') }}</div>
                                    </div>
                                    <div class="agenda-content">
                                        <h4 class="card-title">{{ app()->getLocale() == 'id' ? $agenda->title : $agenda->title_en }}</h4>
                                        <div class="agenda-info mb-3">
                                            <p><i class="far fa-clock me-1"></i> {{ $agenda->time->format('H:i') }}</p>
                                            <p><i class="fas fa-map-marker-alt me-1"></i> {{ app()->getLocale() == 'id' ? $agenda->location : $agenda->location_en }}</p>
                                            @if($agenda->organizer)
                                                <p><i class="fas fa-user me-1"></i> {{ app()->getLocale() == 'id' ? $agenda->organizer : $agenda->organizer_en }}</p>
                                            @endif
                                        </div>
                                        <p class="card-text">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($agenda->description), 150) : \Illuminate\Support\Str::limit(strip_tags($agenda->description_en), 150) }}</p>
                                        <a href="{{ route('agenda.show', $agenda->id) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Detail Agenda' : 'Agenda Details' }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada agenda yang tersedia.' : 'No agenda available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $agendaItems->links() }}
            </div>
        </div>
    </section>
@endsection
