<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the telegram_subscribers table if it exists
        if (Schema::hasTable('telegram_subscribers')) {
            Schema::dropIfExists('telegram_subscribers');
        }

        // Remove Telegram-related settings if the settings table exists
        if (Schema::hasTable('settings')) {
            DB::table('settings')->where('group', 'telegram')->delete();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible
    }
};
