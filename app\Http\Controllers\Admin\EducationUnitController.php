<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EducationUnit;
use App\Services\ImageService;
use Illuminate\Http\Request;

class EducationUnitController extends Controller
{
    /**
     * The image service instance.
     *
     * @var \App\Services\ImageService
     */
    protected $imageService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\ImageService  $imageService
     * @return void
     */
    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $activeTab = $request->query('tab', 'formal');

        if ($activeTab == 'formal') {
            $educationUnits = EducationUnit::formal()->orderBy('order', 'asc')->get();
        } else {
            $educationUnits = EducationUnit::nonFormal()->orderBy('order', 'asc')->get();
        }

        return view('admin.education_units.index', compact('educationUnits', 'activeTab'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.education_units.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'edu_type' => 'required|string|in:formal,non-formal',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'level' => 'nullable|string|max:255',
            'level_en' => 'nullable|string|max:255',
            'facilities' => 'nullable|string',
            'facilities_en' => 'nullable|string',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|string|max:255',
            'principal_name' => 'required|string|max:255',
            'principal_name_en' => 'nullable|string|max:255',
            'principal_education' => 'nullable|string',
            'principal_education_en' => 'nullable|string',
            'principal_experience' => 'nullable|string',
            'principal_experience_en' => 'nullable|string',
            'principal_achievements' => 'nullable|string',
            'principal_achievements_en' => 'nullable|string',
            'principal_image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'order' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->except(['image', 'principal_image']);

        // Set default order if not provided
        if (!isset($data['order'])) {
            $maxOrder = EducationUnit::max('order');
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;



        // Handle main image upload with compression
        if ($request->hasFile('image')) {
            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'education_units',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        // Handle principal image upload with compression
        if ($request->hasFile('principal_image')) {
            $principalImagePath = $this->imageService->compressAndSave(
                $request->file('principal_image'),
                'principals',
                config('image.quality'),
                400, // Width for 4:5 aspect ratio
                500  // Height for 4:5 aspect ratio
            );
            $data['principal_image'] = $principalImagePath;
        }

        EducationUnit::create($data);

        $eduType = $data['edu_type'] ?? 'formal';
        return redirect()->route('admin.education-units.index', ['tab' => $eduType])
            ->with('success', 'Education unit created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EducationUnit  $educationUnit
     * @return \Illuminate\View\View
     */
    public function show(EducationUnit $educationUnit)
    {
        return view('admin.education_units.show', compact('educationUnit'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\EducationUnit  $educationUnit
     * @return \Illuminate\View\View
     */
    public function edit(EducationUnit $educationUnit)
    {
        return view('admin.education_units.edit', compact('educationUnit'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EducationUnit  $educationUnit
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, EducationUnit $educationUnit)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'edu_type' => 'required|string|in:formal,non-formal',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'level' => 'nullable|string|max:255',
            'level_en' => 'nullable|string|max:255',
            'facilities' => 'nullable|string',
            'facilities_en' => 'nullable|string',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|string|max:255',
            'principal_name' => 'required|string|max:255',
            'principal_name_en' => 'nullable|string|max:255',
            'principal_education' => 'nullable|string',
            'principal_education_en' => 'nullable|string',
            'principal_experience' => 'nullable|string',
            'principal_experience_en' => 'nullable|string',
            'principal_achievements' => 'nullable|string',
            'principal_achievements_en' => 'nullable|string',
            'principal_image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
            'order' => 'nullable|integer',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif',
        ]);

        $data = $request->except(['image', 'principal_image']);

        // Set is_active to true by default
        $data['is_active'] = true;



        // Handle main image upload with compression
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($educationUnit->image) {
                $this->imageService->deleteImage($educationUnit->image);
            }

            $imagePath = $this->imageService->compressAndSave(
                $request->file('image'),
                'education_units',
                config('image.quality'),
                config('image.max_width'),
                config('image.max_height')
            );
            $data['image'] = $imagePath;
        }

        // Handle principal image upload with compression
        if ($request->hasFile('principal_image')) {
            // Delete old principal image if exists
            if ($educationUnit->principal_image) {
                $this->imageService->deleteImage($educationUnit->principal_image);
            }

            $principalImagePath = $this->imageService->compressAndSave(
                $request->file('principal_image'),
                'principals',
                config('image.quality'),
                400, // Width for 4:5 aspect ratio
                500  // Height for 4:5 aspect ratio
            );
            $data['principal_image'] = $principalImagePath;
        }

        $educationUnit->update($data);

        $eduType = $data['edu_type'] ?? 'formal';
        return redirect()->route('admin.education-units.index', ['tab' => $eduType])
            ->with('success', 'Education unit updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EducationUnit  $educationUnit
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(EducationUnit $educationUnit)
    {
        // Delete main image if exists
        if ($educationUnit->image) {
            $this->imageService->deleteImage($educationUnit->image);
        }

        // Delete principal image if exists
        if ($educationUnit->principal_image) {
            $this->imageService->deleteImage($educationUnit->principal_image);
        }

        $educationUnit->delete();

        return redirect()->route('admin.education-units.index')
            ->with('success', 'Education unit deleted successfully.');
    }
}
