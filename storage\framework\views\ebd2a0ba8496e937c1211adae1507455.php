<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Dashboard</h1>
    </div>

    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-primary-light">
                <div class="card-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <div class="card-title">Total News</div>
                <div class="card-value"><?php echo e($newsCount); ?></div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-success-light">
                <div class="card-icon">
                    <i class="fas fa-images"></i>
                </div>
                <div class="card-title">Total Gallery Items</div>
                <div class="card-value"><?php echo e($galleryCount); ?></div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-warning-light">
                <div class="card-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="card-title">Pending Registrations</div>
                <div class="card-value"><?php echo e($pendingRegistrations); ?></div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="dashboard-card bg-danger-light">
                <div class="card-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="card-title">Unread Messages</div>
                <div class="card-value"><?php echo e($unreadMessages); ?></div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Latest Registrations</h5>
                    <a href="<?php echo e(route('admin.registrations.index')); ?>" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $latestRegistrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $registration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($registration->full_name); ?></td>
                                        <td><?php echo e($registration->created_at ? $registration->created_at->format('d M Y') : 'N/A'); ?></td>
                                        <td>
                                            <?php if($registration->status == 'pending'): ?>
                                                <span class="badge bg-warning">Pending</span>
                                            <?php elseif($registration->status == 'approved'): ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php elseif($registration->status == 'rejected'): ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('admin.registrations.show', $registration)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No registrations found.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Latest Messages</h5>
                    <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Subject</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $latestMessages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="<?php echo e($message->is_read ? '' : 'table-light'); ?>">
                                        <td><?php echo e($message->name); ?></td>
                                        <td><?php echo e(\Illuminate\Support\Str::limit($message->subject, 30)); ?></td>
                                        <td><?php echo e($message->created_at->format('d M Y')); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('admin.contacts.show', $message)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No messages found.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/dashboard.blade.php ENDPATH**/ ?>