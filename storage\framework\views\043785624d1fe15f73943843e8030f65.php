<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Video' : 'Videos'); ?>

<?php $__env->startSection('meta_description', app()->getLocale() == 'id' ? 'Koleksi video dari ' . \App\Helpers\SettingHelper::getInstitutionName() : 'Video collection from ' . \App\Helpers\SettingHelper::getInstitutionNameEn()); ?>

<?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />
    <link rel="stylesheet" href="<?php echo e(asset('css/plyr-custom.css')); ?>" />
    <style>
        .page-header {
            background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('<?php echo e(asset('images/header-bg.jpg')); ?>') center/cover no-repeat;
            padding: 80px 0;
            margin-bottom: 50px;
        }

        .page-title {
            color: white;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .breadcrumb-item, .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
        }

        .breadcrumb-item.active {
            color: white;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Video card styling for center alignment */
        .video-card {
            width: 100%;
            margin: 0 auto;
            height: 100%;
        }

        .col-md-6.col-lg-4 {
            display: flex;
            justify-content: center;
        }

        .text-decoration-none.d-block {
            width: 100%;
        }

        .no-videos {
            padding: 50px 20px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin: 30px 0;
        }

        .no-videos i {
            font-size: 48px;
            color: #dee2e6;
            margin-bottom: 20px;
        }

        .no-videos h4 {
            color: #6c757d;
            margin-bottom: 10px;
        }

        .no-videos p {
            color: #adb5bd;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">
                        <?php echo e(app()->getLocale() == 'id' ? 'Video' : 'Videos'); ?>

                    </h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Video' : 'Videos'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Videos Section -->
    <section class="videos-section py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="section-title text-center mb-4" data-aos="fade-up">
                        <?php echo e(app()->getLocale() == 'id' ? 'Koleksi Video Kami' : 'Our Video Collection'); ?>

                    </h2>
                    <p class="text-center text-muted mb-5" data-aos="fade-up" data-aos-delay="100">
                        <?php echo e(app()->getLocale() == 'id' ? 'Jelajahi koleksi video kegiatan dan pembelajaran dari ' . \App\Helpers\SettingHelper::getInstitutionName() : 'Explore our collection of activity and learning videos from ' . \App\Helpers\SettingHelper::getInstitutionNameEn()); ?>

                    </p>
                </div>
            </div>

            <?php if($videos->count() > 0): ?>
                <div class="row g-4 justify-content-center">
                    <?php $__currentLoopData = $videos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $video): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo e($loop->iteration * 50); ?>">
                            <a href="<?php echo e(route('videos.show', $video->id)); ?>" class="text-decoration-none d-block">
                                <div class="video-card">
                                    <div class="video-thumbnail">
                                        <?php if($video->thumbnail): ?>
                                            <img src="<?php echo e(asset('storage/' . $video->thumbnail)); ?>" alt="<?php echo e($video->title); ?>" class="img-fluid">
                                        <?php else: ?>
                                            <div class="ratio ratio-16x9">
                                                <iframe src="<?php echo e($video->youtube_embed_url); ?>" title="<?php echo e($video->title); ?>" allowfullscreen></iframe>
                                            </div>
                                        <?php endif; ?>
                                        <div class="play-icon">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                    <div class="video-info">
                                        <h5 class="video-title"><?php echo e(app()->getLocale() == 'id' ? $video->title : $video->title_en); ?></h5>
                                        <div class="video-description summernote-content"><?php echo app()->getLocale() == 'id' ? $video->description : $video->description_en; ?></div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-5" data-aos="fade-up">
                    <?php echo e($videos->links()); ?>

                </div>
            <?php else: ?>
                <div class="no-videos" data-aos="fade-up">
                    <i class="fas fa-video-slash"></i>
                    <h4><?php echo e(app()->getLocale() == 'id' ? 'Belum Ada Video' : 'No Videos Yet'); ?></h4>
                    <p><?php echo e(app()->getLocale() == 'id' ? 'Video akan segera ditambahkan. Silakan kunjungi kembali nanti.' : 'Videos will be added soon. Please check back later.'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.plyr.io/3.7.8/plyr.polyfilled.js"></script>
    <script src="<?php echo e(asset('js/plyr-custom.js')); ?>"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/videos/index.blade.php ENDPATH**/ ?>