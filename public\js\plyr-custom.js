document.addEventListener('DOMContentLoaded', function() {
    // Initialize Plyr for all video players
    const players = Plyr.setup('.plyr-player', {
        controls: [
            'play-large', 'play', 'progress', 'current-time', 'mute', 
            'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
        ],
        autoplay: false,
        hideControls: true,
        keyboard: { focused: true, global: true }
    });
    
    // Handle YouTube embeds
    const youtubePlayer = document.getElementById('player');
    if (youtubePlayer) {
        const player = new Plyr('#player', {
            controls: [
                'play-large', 'play', 'progress', 'current-time', 'mute', 
                'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
            ],
            autoplay: false,
            hideControls: true,
            keyboard: { focused: true, global: true }
        });
    }
});
