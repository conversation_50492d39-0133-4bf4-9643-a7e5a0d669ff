<?php $__env->startSection('title', app()->getLocale() == 'id' ? $news->title : $news->title_en); ?>

<?php $__env->startSection('meta_description', app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(html_entity_decode(strip_tags($news->content)), 160) : \Illuminate\Support\Str::limit(html_entity_decode(strip_tags($news->content_en)), 160)); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? $news->title : $news->title_en); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('news')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Berita' : 'News'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($news->title, 30) : \Illuminate\Support\Str::limit($news->title_en, 30)); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- News Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 main-content">
                    <!-- News Detail -->
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-body p-4">
                            <div class="news-meta mb-3">
                                <span class="me-3"><i class="far fa-calendar-alt me-1"></i> <?php echo e($news->published_at->format('d M Y')); ?></span>
                                <span><i class="far fa-user me-1"></i> <?php echo e($news->user->name); ?></span>
                            </div>

                            <div class="news-image mb-4">
                                <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($news->image, 'news')); ?>" class="img-fluid rounded" alt="<?php echo e(app()->getLocale() == 'id' ? $news->title : $news->title_en); ?>">
                            </div>

                            <div class="news-content">
                                <?php echo app()->getLocale() == 'id' ? $news->content : $news->content_en; ?>

                            </div>

                            <!-- Social Share -->
                            <div class="social-share mt-4 pt-4 border-top">
                                <h5><?php echo e(app()->getLocale() == 'id' ? 'Bagikan' : 'Share'); ?></h5>
                                <div class="d-flex">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en))); ?>" class="btn btn-sm btn-outline-primary me-2" target="_blank">
                                        <i class="fab fa-facebook-f me-1"></i> Facebook
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en))); ?>&text=<?php echo e(urlencode(app()->getLocale() == 'id' ? $news->title : $news->title_en)); ?>" class="btn btn-sm btn-outline-info me-2" target="_blank">
                                        <i class="fab fa-twitter me-1"></i> Twitter
                                    </a>
                                    <a href="https://wa.me/?text=<?php echo e(urlencode((app()->getLocale() == 'id' ? $news->title : $news->title_en) . ' ' . route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en))); ?>" class="btn btn-sm btn-outline-success" target="_blank">
                                        <i class="fab fa-whatsapp me-1"></i> WhatsApp
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                    <!-- Related News -->
                    <div class="card border-0 shadow-sm mb-4 fade-in">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Berita Terkait' : 'Related News'); ?></h5>
                        </div>
                        <div class="card-body">
                            <?php $__empty_1 = true; $__currentLoopData = $relatedNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="related-news-item mb-3 pb-3 <?php echo e(!$loop->last ? 'border-bottom' : ''); ?>">
                                    <div class="row g-0">
                                        <div class="col-4">
                                            <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($related->thumbnail, 'news')); ?>" class="img-fluid rounded" alt="<?php echo e(app()->getLocale() == 'id' ? $related->title : $related->title_en); ?>">
                                        </div>
                                        <div class="col-8 ps-3">
                                            <div class="small text-muted mb-1"><?php echo e($related->published_at->format('d M Y')); ?></div>
                                            <h6 class="mb-0"><a href="<?php echo e(route('news.show', app()->getLocale() == 'id' ? $related->slug : $related->slug_en)); ?>" class="text-decoration-none"><?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($related->title, 50) : \Illuminate\Support\Str::limit($related->title_en, 50)); ?></a></h6>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada berita terkait.' : 'No related news.'); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Latest Announcements -->
                    <?php echo $__env->make('public.partials.latest-announcements', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <!-- Latest Agenda -->
                    <?php echo $__env->make('public.partials.latest-agenda', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <!-- CTA -->
                    <div class="card border-0 bg-success text-white shadow-sm fade-in">
                        <div class="card-body p-4 text-center">
                            <h5 class="mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></h5>
                            <p><?php echo e(app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family.'); ?></p>
                            <a href="<?php echo e(route('registration')); ?>" class="btn btn-light"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></a>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->startPush('scripts'); ?>
    <script src="<?php echo e(asset('js/sticky-sidebar.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/news/show.blade.php ENDPATH**/ ?>