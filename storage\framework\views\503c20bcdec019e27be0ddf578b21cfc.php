<?php $__env->startSection('title', 'Partnerships'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Partnerships</h1>
        <a href="<?php echo e(route('admin.partnerships.create')); ?>" class="btn btn-success">
            <i class="fas fa-plus me-1"></i> Add New
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if($partnerships->isEmpty()): ?>
                <div class="text-center py-5">
                    <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                    <p class="mb-0">No partnerships found. Click the "Add New" button to create one.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="80">Logo</th>
                                <th>Name</th>
                                <th>Website</th>
                                <th>Since</th>
                                <th width="120">Country</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $partnerships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partnership): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <?php if($partnership->logo): ?>
                                            <img src="<?php echo e(asset('storage/' . $partnership->logo)); ?>" alt="<?php echo e($partnership->name); ?>" class="img-thumbnail" width="60">
                                        <?php else: ?>
                                            <div class="bg-light text-center p-2 rounded">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo e($partnership->name); ?></div>
                                        <small class="text-muted"><?php echo e($partnership->name_en); ?></small>
                                    </td>
                                    <td>
                                        <?php if($partnership->website): ?>
                                            <a href="<?php echo e($partnership->website); ?>" target="_blank"><?php echo e($partnership->website); ?></a>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($partnership->partnership_since ? $partnership->partnership_since->format('d M Y') : '-'); ?></td>
                                    <td>
                                        <?php if($partnership->country_code): ?>
                                            <img src="https://flagcdn.com/24x18/<?php echo e(strtolower($partnership->country_code)); ?>.png" class="me-2" alt="<?php echo e($partnership->country_name); ?>" width="24" height="18">
                                            <?php echo e($partnership->country_name); ?>

                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.partnerships.edit', $partnership)); ?>" class="btn btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo e(route('admin.partnerships.show', $partnership)); ?>" class="btn btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo e($partnership->id); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?php echo e($partnership->id); ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo e($partnership->id); ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel<?php echo e($partnership->id); ?>">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        Are you sure you want to delete the partnership with "<?php echo e($partnership->name); ?>"?
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="<?php echo e(route('admin.partnerships.destroy', $partnership)); ?>" method="POST">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/partnerships/index.blade.php ENDPATH**/ ?>