<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Sejarah' : 'History'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Sejarah Pondok Pesantren' : 'History of Islamic Boarding School'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item"><a href="<?php echo e(route('profile')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Profil' : 'Profile'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Sejarah' : 'History'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- History Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto" data-aos="fade-up">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="card-title text-success mb-4"><?php echo e(app()->getLocale() == 'id' ? $history->title : $history->title_en); ?></h2>

                            <div class="text-center mb-4">
                                <?php if($history->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $history->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $history->title : $history->title_en); ?>" class="img-fluid rounded shadow-sm">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('images/history.jpg')); ?>" alt="History" class="img-fluid rounded shadow-sm">
                                <?php endif; ?>
                            </div>

                            <div class="card-text">
                                <?php echo app()->getLocale() == 'id' ? $history->content : $history->content_en; ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Timeline Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Perjalanan Waktu' : 'Timeline'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Tonggak penting dalam perjalanan Pondok Pesantren Nurul Hayah 4' : 'Important milestones in the journey of Nurul Hayah 4 Islamic Boarding School'); ?></p>
            </div>

            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <div class="timeline">
                        <?php if(count($timelines) > 0): ?>
                            <?php $__currentLoopData = $timelines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $timeline): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="timeline-item" data-aos="fade-up" data-aos-delay="<?php echo e($index * 100); ?>">
                                    <div class="timeline-year"><?php echo e($timeline->year); ?></div>
                                    <div class="timeline-content">
                                        <h4><?php echo e(app()->getLocale() == 'id' ? $timeline->title : $timeline->title_en); ?></h4>

                                        <?php if($timeline->image): ?>
                                        <div class="text-center mb-4">
                                            <img src="<?php echo e(asset('storage/' . $timeline->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $timeline->title : $timeline->title_en); ?>" class="img-fluid rounded shadow-sm" style="max-height: 200px;">
                                        </div>
                                        <?php endif; ?>

                                        <div><?php echo app()->getLocale() == 'id' ? $timeline->content : $timeline->content_en; ?></div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <p class="text-muted"><?php echo e(app()->getLocale() == 'id' ? 'Belum ada data timeline.' : 'No timeline data available.'); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .timeline {
        position: relative;
        padding: 20px 0;
    }

    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        width: 2px;
        background-color: #198754;
        transform: translateX(-50%);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 50px;
        display: flex;
        align-items: center;
    }

    .timeline-year {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        background-color: #198754;
        color: #fff;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        z-index: 1;
    }

    .timeline-content {
        width: 45%;
        padding: 20px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .timeline-item:nth-child(odd) .timeline-content {
        margin-right: 5%;
        margin-left: auto;
    }

    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 5%;
    }

    .timeline-content h4 {
        color: #198754;
        margin-bottom: 10px;
    }

    @media (max-width: 767.98px) {
        .timeline:before {
            left: 30px;
        }

        .timeline-item {
            flex-direction: column;
            align-items: flex-start;
        }

        .timeline-year {
            left: 30px;
            transform: translateX(-50%);
        }

        .timeline-content {
            width: calc(100% - 60px);
            margin-left: 60px !important;
            margin-right: 0 !important;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/profile/history.blade.php ENDPATH**/ ?>