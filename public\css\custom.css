/* Custom CSS for Nurul <PERSON> 4 Website */

/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    color: #333;
    line-height: 1.6;
    /* Hapus padding-top: 76px; */
}

@media (max-width: 991.98px) {
    body {
        /* Hapus padding-top: 70px; */
    }
}

a {
    color: #198754;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #146c43;
}

.btn {
    border-radius: 4px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-success:hover {
    background-color: #146c43;
    border-color: #146c43;
}

.btn-gold {
    background-color: #c9a55a;
    border-color: #c9a55a;
    color: white;
    font-weight: 500;
    padding: 8px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-gold:hover {
    background-color: #b08d45;
    border-color: #b08d45;
    color: white;
}

.section-padding {
    padding: 80px 0;
}

.section-title {
    margin-bottom: 50px;
    text-align: center;
}

.section-title h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.section-title h2:after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: #198754;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.section-title p {
    font-size: 16px;
    max-width: 700px;
    margin: 0 auto;
}

/* Ensure text in bg-success sections is white and bold */
.bg-success .section-title h2 {
    color: white;
    font-weight: 700 !important; /* Ensure bold text with !important */
    font-size: 36px !important; /* Ensure consistent font size */
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3); /* Add subtle text shadow for better readability */
}

.bg-success .section-title h2:after {
    background-color: white;
}

/* Header Styles */
.top-bar {
    font-size: 14px;
}

/* Gunakan sticky-top dengan CSS yang lebih sederhana */
header.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1050; /* Increased z-index to ensure it's above other elements */
    width: 100%;
    /* Ensure the header is fully visible */
    transform: translateZ(0);
    will-change: transform;
}

/* Hapus atau modifikasi fixed-navbar */
/* .fixed-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1030;
    transition: all 0.3s ease;
    background-color: #fff;
} */

.navbar-brand img {
    max-height: 50px;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 10px 15px;
    position: relative;
}

.navbar-nav .nav-link.active {
    color: #198754;
}

.navbar-nav .nav-link:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: #19875400;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    transition: width 0.3s ease;
}

.navbar-nav .nav-link.active:after,
.navbar-nav .nav-link:hover:after {
    width: 70%;
}

/* Hero Section */
.hero-section {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 150px 0;
    position: relative;
}

.hero-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.hero-content {
    position: relative;
    z-index: 1;
    color: #fff;
    text-align: center;
}

.hero-content h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 18px;
    max-width: 700px;
    margin: 0 auto 30px;
}

/* Features Section */
.feature-box {
    text-align: center;
    padding: 30px 20px;
    margin-bottom: 30px;
    border-radius: 5px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    background-color: #fff;
    z-index: 1;
}

.feature-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    border-color: rgba(25, 135, 84, 0.2);
}

.feature-box:hover::before {
    opacity: 1;
}

.feature-box::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #198754;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.feature-box:hover::after {
    transform: scaleX(1);
}

.feature-icon {
    font-size: 48px;
    color: #198754;
    margin-bottom: 20px;
    transition: all 0.4s ease;
}

.feature-box:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    color: #146c43;
    animation: feature-icon-pulse 1.5s ease-in-out infinite;
}

@keyframes feature-icon-pulse {
    0% {
        transform: scale(1.1) rotate(5deg);
    }
    50% {
        transform: scale(1.15) rotate(5deg);
    }
    100% {
        transform: scale(1.1) rotate(5deg);
    }
}

.feature-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.feature-box:hover .feature-title {
    color: #198754;
}

.feature-box p {
    transition: all 0.4s ease;
    position: relative;
    z-index: 1;
}

.feature-box:hover p {
    transform: translateY(-3px);
}

/* Programs Section */
.program-carousel-section {
    padding: 80px 0;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.program-carousel {
    padding: 20px 2rem;
    margin: 0;
    width: calc(100% - 4rem);
    position: relative;
    left: 2rem;
    right: 2rem;
}

.program-carousel .owl-stage-outer {
    overflow: visible;
}

.program-carousel .owl-dots {
    margin-top: 20px;
    text-align: center;
}

.program-carousel .owl-dot span {
    width: 10px;
    height: 10px;
    margin: 5px;
    background: #D6D6D6;
    display: block;
    transition: opacity .2s ease;
    border-radius: 30px;
}

.program-carousel .owl-dot.active span,
.program-carousel .owl-dot:hover span {
    background: #198754;
}

.program-card {
    border: none;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.program-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

@media (max-width: 767.98px) {
    .program-card {
        width: 85%;
        margin: 0 auto 20px;
    }

    .program-carousel .owl-item {
        padding: 10px 0;
    }

    .program-carousel {
        padding: 20px 2rem;
    }
}

.program-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

@media (max-width: 767.98px) {
    .program-card .card-img-top {
        height: 280px;
        object-fit: cover;
    }
}

.program-card .card-body {
    padding: 20px;
}

.program-card .card-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
}

@media (max-width: 767.98px) {
    .program-card .card-body {
        padding: 15px;
    }

    .program-card .card-title {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .program-card .card-text {
        font-size: 13px;
        line-height: 1.4;
    }
}

/* Newsroom Label */
.newsroom-label {
    position: relative;
    padding: 15px 25px;
    background-color: #198754;
    color: white;
    border-radius: 0 50px 50px 0;
    width: fit-content;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(-20px);
}

.newsroom-label h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 1px;
}

.newsroom-label:before {
    content: '';
    position: absolute;
    top: 0;
    left: -15px;
    width: 15px;
    height: 15px;
    background-color: #146c43;
    border-radius: 0 0 0 15px;
    z-index: -1;
}

/* Announcements Label */
.announcements-label {
    position: relative;
    padding: 15px 25px;
    background-color: #c9a55a;
    color: white;
    border-radius: 0 50px 50px 0;
    width: fit-content;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(-20px);
}

.announcements-label h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 1px;
}

.announcements-label:before {
    content: '';
    position: absolute;
    top: 0;
    left: -15px;
    width: 15px;
    height: 15px;
    background-color: #b08d45;
    border-radius: 0 0 0 15px;
    z-index: -1;
}

/* Agenda Label */
.agenda-label {
    position: relative;
    padding: 15px 25px;
    background-color: #198754;
    color: white;
    border-radius: 0 50px 50px 0;
    width: fit-content;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(-20px);
}

.agenda-label h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 1px;
}

.agenda-label:before {
    content: '';
    position: absolute;
    top: 0;
    left: -15px;
    width: 15px;
    height: 15px;
    background-color: #146c43;
    border-radius: 0 0 0 15px;
    z-index: -1;
}

/* Announcement Item */
.announcement-item {
    display: flex;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.announcement-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.announcement-title {
    display: block;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 3px;
    color: #333;
    text-decoration: none;
    transition: color 0.2s ease;
}

.announcement-title:hover {
    color: #198754;
    text-decoration: none;
}

.announcement-item .announcement-date {
    font-size: 11px;
    color: #dc3545;
    font-weight: 500;
    margin-bottom: 2px;
    line-height: 1.2;
}

/* Agenda Item */
.agenda-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.agenda-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.agenda-date {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    margin-right: 15px;
    min-width: 60px;
    border: 1px solid #e9ecef;
}

.agenda-date .day {
    font-size: 24px;
    font-weight: 700;
    color: #198754;
    line-height: 1;
}

.agenda-date .month {
    font-size: 14px;
    text-transform: uppercase;
    color: #6c757d;
    font-weight: 500;
}

/* Small agenda date for related items */
.agenda-date-small {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 5px;
    text-align: center;
    min-width: 45px;
    border: 1px solid #e9ecef;
}

.agenda-date-small .day {
    font-size: 18px;
    font-weight: 700;
    color: #198754;
    line-height: 1;
}

.agenda-date-small .month {
    font-size: 12px;
    text-transform: uppercase;
    color: #6c757d;
    font-weight: 500;
}

.agenda-content {
    flex: 1;
}

.agenda-content h5 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.agenda-content .agenda-info {
    font-size: 12px;
    color: #6c757d;
}

.agenda-content .agenda-info i,
.agenda-info i {
    margin-right: 5px;
    display: inline-block;
    width: 1.25em;
    text-align: center;
    font-style: normal;
}

/* Fix untuk ikon Font Awesome */
.agenda-info i.fas,
.agenda-info i.far,
.agenda-info i.fab {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands" !important;
}

.agenda-info i.fas {
    font-weight: 900 !important;
}

.agenda-info i.far {
    font-weight: 400 !important;
}

/* Related Items */
.related-item {
    transition: all 0.3s ease;
}

.related-item:hover {
    background-color: rgba(25, 135, 84, 0.05);
}

.related-item h6 a {
    color: #333;
    transition: color 0.3s ease;
}

.related-item h6 a:hover {
    color: #198754;
}

/* Sticky Sidebar */
.sticky-sidebar {
    position: sticky;
    top: 100px;
    transition: all 0.3s ease;
}

/* Disable sticky sidebar on mobile and tablet */
@media (max-width: 991.98px) {
    .sticky-sidebar {
        position: static;
        transform: none;
        width: 100%;
        top: 0;
    }
}

/* Information Center Logo */
.info-center-logo {
    display: block;
    margin: 0 auto 20px;
    max-width: 100px;
    height: auto;
}

.info-center-title {
    position: relative;
    text-align: center;
    padding-bottom: 10px;
}

.info-center-title h2 {
    margin-bottom: 10px;
}

.info-center-title p {
    max-width: 700px;
    margin: 0 auto;
}

/* News Section */
.news-card {
    border: none;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.news-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

.news-card .card-body {
    padding: 20px;
}

.news-card .card-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.news-card .card-text {
    font-size: 14px;
    margin-bottom: 15px;
}

.news-date {
    font-size: 12px;
    color: #6c757d;
}

.news-category .badge {
    font-weight: 500;
    padding: 5px 10px;
    font-size: 11px;
}

/* News Category Tabs */
#newsCategoryTabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 30px;
}

#newsCategoryTabs .nav-item {
    margin-right: 5px;
}

#newsCategoryTabs .nav-link {
    color: #495057;
    font-weight: 500;
    border: none;
    border-radius: 4px 4px 0 0;
    padding: 10px 20px;
    position: relative;
    transition: all 0.3s ease;
}

#newsCategoryTabs .nav-link:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 3px;
    background-color: #198754;
    transition: width 0.3s ease;
}

#newsCategoryTabs .nav-link:hover {
    color: #198754;
    background-color: rgba(25, 135, 84, 0.05);
}

#newsCategoryTabs .nav-link.active {
    color: #198754;
    background-color: rgba(25, 135, 84, 0.1);
    border: none;
}

#newsCategoryTabs .nav-link.active:after {
    width: 100%;
}

/* Tab Content Animation */
.tab-pane.fade {
    transition: opacity 0.3s ease-in-out;
}

.tab-pane.fade.show {
    opacity: 1;
}

/* Director's Insight Section */
.directors-insight-section {
    position: relative;
    padding: 0;
    margin: 0;
    overflow: hidden;
    background-color: #F8F9FA;
}

/* Animation Keyframes */
@keyframes slideInLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideUp {
    0% {
        transform: translateY(40px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.03);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shine {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Scroll Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.active {
    opacity: 1;
    transform: translateY(0);
}

/* Special handling for hero content */
.hero-section .hero-content.fade-in {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.8s ease-out 0.3s, transform 0.8s ease-out 0.3s;
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-left.active {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-right.active {
    opacity: 1;
    transform: translateX(0);
}

.zoom-in {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.zoom-in.active {
    opacity: 1;
    transform: scale(1);
}

/* Gold background on left side */
.gold-bg-left {
    position: absolute;
    top: 0;
    left: 0;
    width: 238px;
    height: 100%;
    background-color: #c9a55a;
    background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    background-size: 200% 100%;
    z-index: 0;
    transform: translateX(-100%);
    opacity: 0;
    will-change: transform, opacity;
}

.directors-insight-section.active .gold-bg-left {
    animation: slideInLeft 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards,
               shine 8s ease-in-out 2s infinite;
}

.directors-insight-section .container {
    display: flex;
    padding-top: 40px;
    padding-bottom: 40px;
}

/* Director Image */
.director-image-container {
    position: relative;
    width: 335px;
    margin-right: 30px;
    z-index: 1;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transform: translateY(30px);
    will-change: transform, opacity;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.directors-insight-section.active .director-image-container {
    animation: fadeInUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
}

.director-image-container:hover {
    transform: translateY(-5px);
}

.director-image-container img {
    width: 100%;
    aspect-ratio: 4/5;
    object-fit: cover;
    display: block;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.director-image-container:hover img {
    transform: scale(1.02);
}

.director-name-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 10px;
    text-align: center;
    font-weight: 600;
    transform: translateY(40px);
    opacity: 0;
    will-change: transform, opacity;
}

.directors-insight-section.active .director-name-overlay {
    animation: slideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
}

/* Director's Insight Content */
.directors-insight-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 1;
}

/* Director's Insight Header */
.directors-insight-header {
    background-color: #0f4229;
    color: #fff;
    padding: 15px 30px;
    border-radius: 15px;
    margin-bottom: 20px;
    width: fit-content;
    opacity: 0;
    transform: translateX(30px);
    will-change: transform, opacity;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.directors-insight-section.active .directors-insight-header {
    animation: fadeInRight 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s forwards,
               pulse 3s ease-in-out 2.5s infinite;
}

.directors-insight-header:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation-play-state: paused;
}

.directors-insight-header h2 {
    margin-bottom: 0;
    font-size: 28px;
    font-weight: 700;
}

/* Insight Card */
.insight-card {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    opacity: 0;
    transform: translateY(30px);
    will-change: transform, opacity, box-shadow;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.directors-insight-section.active .insight-card {
    animation: fadeInUp 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.7s forwards;
}

.insight-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

.insight-card-content {
    display: flex;
    padding: 20px;
}

.insight-thumbnail {
    flex: 0 0 60px;
    margin-right: 15px;
    overflow: hidden;
    border-radius: 5px;
}

.insight-thumbnail img {
    width: 100%;
    aspect-ratio: 4/5;
    object-fit: cover;
    border-radius: 5px;
    transition: all 0.5s ease;
}

.insight-card:hover .insight-thumbnail img {
    transform: scale(1.1);
}

.insight-text {
    flex: 1;
}

.insight-text h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    transition: color 0.3s ease;
}

.insight-card:hover .insight-text h4 {
    color: #198754;
}

.insight-date {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 10px;
}

.insight-text .insight-content {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 10px;
}

.insight-read-more {
    text-align: right;
}

.insight-read-more a {
    color: #198754;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
}

.insight-read-more a:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: #198754;
    transition: width 0.3s ease;
}

.insight-read-more a:hover:after {
    width: 100%;
}

/* Partnership Section */
.partner-carousel {
    padding: 20px 0;
}

.partner-logo {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.partner-logo:hover {
    transform: translateY(-5px);
}

.partner-logo img {
    max-height: 100px;
    max-width: 100%;
    object-fit: contain;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1); /* Make all logos white */
}

.partner-logo:hover img {
    transform: scale(1.05);
}

/* Hide navigation and pagination for partnership carousel */
.partner-carousel .owl-nav,
.partner-carousel .owl-dots {
    display: none;
}

/* Gallery Section */
.gallery-item {
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    border-radius: 5px;
    display: block;
    width: 100%;
    height: 100%;
}

.gallery-image-container {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    border-radius: 5px;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
    display: block;
    border-radius: 5px;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(25, 135, 84, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 2;
    border-radius: 5px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.gallery-overlay i {
    color: #fff;
    font-size: 24px;
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    display: block;
    margin: 0 auto;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: #fff;
    font-size: 30px;
    cursor: pointer;
}

/* Contact Section */
.contact-info {
    margin-bottom: 30px;
}

.contact-info i {
    font-size: 24px;
    color: #198754;
    margin-right: 15px;
}

.contact-form .form-control {
    border-radius: 4px;
    padding: 12px 15px;
    margin-bottom: 20px;
}

.contact-form .form-control:focus {
    box-shadow: none;
    border-color: #198754;
}

.map-container {
    height: 400px;
    margin-bottom: 30px;
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 5px;
}

/* Registration Form */
.registration-form {
    background-color: #f8f9fa;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.registration-form .form-control {
    border-radius: 4px;
    padding: 12px 15px;
    margin-bottom: 20px;
}

.registration-form .form-control:focus {
    box-shadow: none;
    border-color: #198754;
}

/* Footer */
footer {
    background-color: #212529;
    color: #fff;
    padding-top: 70px;
    padding-bottom: 20px;
}

footer h5 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    position: relative;
}

footer h5:after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background-color: #198754;
    bottom: -10px;
    left: 0;
}

footer p {
    margin-bottom: 15px;
}

footer .social-links a {
    display: inline-block;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    text-align: center;
    line-height: 36px;
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.3s ease;
}

footer .social-links a:hover {
    background-color: #198754;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: #198754;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background-color: #146c43;
    color: #fff;
    transform: translateY(-5px);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    /* Navbar mobile styles */
    .fixed-navbar {
        padding: 10px 0;
    }

    .navbar-collapse {
        background-color: white;
        padding: 15px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        max-height: 80vh;
        overflow-y: auto;
    }

    .navbar-nav .nav-link {
        padding: 10px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .navbar-nav .nav-item:last-child .nav-link {
        border-bottom: none;
    }

    .navbar-nav .nav-link:after {
        display: none;
    }

    .hero-content h1 {
        font-size: 36px;
    }

    .section-padding {
        padding: 60px 0;
    }

    .section-title h2 {
        font-size: 30px;
    }

    /* Director's Insight tablet styles */
    .directors-insight-section .container {
        flex-direction: column;
        align-items: center;
        padding-top: 30px;
        padding-bottom: 30px;
    }

    .gold-bg-left {
        width: 100%;
        height: 50%;
        animation: none;
        opacity: 1;
    }

    .director-image-container {
        width: 280px;
        margin-right: 0;
        margin-bottom: 30px;
        animation-delay: 0.1s;
    }

    .director-name-overlay {
        animation-delay: 0.8s;
    }

    .directors-insight-content {
        width: 100%;
        align-items: center;
    }

    .directors-insight-header {
        animation-delay: 0.3s;
    }

    .insight-card {
        animation-delay: 0.5s;
    }
}

@media (max-width: 767.98px) {
    .hero-content h1 {
        font-size: 28px;
    }

    .hero-content p {
        font-size: 16px;
    }

    .section-padding {
        padding: 40px 0;
    }

    .section-title h2 {
        font-size: 24px;
    }

    .map-container {
        height: 300px;
    }

    /* Director's Insight responsive styles */
    .directors-insight-section .container {
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .gold-bg-left {
        height: 40%;
    }

    .director-image-container {
        width: 220px;
        animation-duration: 0.8s;
    }

    .director-name-overlay {
        animation-duration: 0.6s;
        animation-delay: 0.6s;
    }

    .directors-insight-header {
        padding: 10px 20px;
        animation-duration: 0.8s;
        animation-delay: 0.2s;
    }

    .directors-insight-header h2 {
        font-size: 24px;
    }

    .insight-card {
        animation-duration: 0.8s;
        animation-delay: 0.4s;
    }

    .insight-card-content {
        flex-direction: column;
    }

    .insight-thumbnail {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100px;
    }
}

