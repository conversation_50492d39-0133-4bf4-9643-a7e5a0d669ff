<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RegistrationSchedule extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'start_date',
        'end_date',
        'title',
        'title_en',
        'description',
        'description_en',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Check if registration is currently open.
     *
     * @return bool
     */
    public static function isRegistrationOpen()
    {
        $today = now()->format('Y-m-d');
        return self::where('is_active', true)
            ->where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->exists();
    }

    /**
     * Get the current active registration schedule.
     *
     * @return \App\Models\RegistrationSchedule|null
     */
    public static function getCurrentSchedule()
    {
        $today = now()->format('Y-m-d');
        return self::where('is_active', true)
            ->where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->first();
    }
}
