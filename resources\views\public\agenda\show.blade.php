@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en)

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('agenda') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Agenda' : 'Agenda' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($agendaItem->title, 30) : \Illuminate\Support\Str::limit($agendaItem->title_en, 30) }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Agenda Content -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 main-content">
                    <div class="card shadow-sm mb-4" data-aos="fade-up">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-4">
                                <div class="agenda-date me-4">
                                    <div class="day">{{ $agendaItem->date->format('d') }}</div>
                                    <div class="month">{{ $agendaItem->date->format('M') }}</div>
                                </div>
                                <div>
                                    <h3>{{ app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en }}</h3>
                                    <div class="agenda-info">
                                        <p><i class="far fa-clock me-1"></i> {{ $agendaItem->time->format('H:i') }}</p>
                                        <p><i class="fas fa-map-marker-alt me-1"></i> {{ app()->getLocale() == 'id' ? $agendaItem->location : $agendaItem->location_en }}</p>
                                        @if($agendaItem->organizer)
                                            <p><i class="fas fa-user me-1"></i> {{ app()->getLocale() == 'id' ? $agendaItem->organizer : $agendaItem->organizer_en }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            @if($agendaItem->image)
                                <div class="text-center mb-4">
                                    <img src="{{ asset('storage/' . $agendaItem->image) }}" alt="{{ app()->getLocale() == 'id' ? $agendaItem->title : $agendaItem->title_en }}" class="img-fluid rounded">
                                </div>
                            @endif

                            <div class="agenda-content">
                                <h4>{{ app()->getLocale() == 'id' ? 'Deskripsi' : 'Description' }}</h4>
                                <div class="mb-4">
                                    {!! app()->getLocale() == 'id' ? $agendaItem->description : $agendaItem->description_en !!}
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('agenda') }}" class="btn btn-outline-success">
                                    <i class="fas fa-arrow-left me-2"></i>{{ app()->getLocale() == 'id' ? 'Kembali ke Daftar Agenda' : 'Back to Agenda' }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="sticky-sidebar">
                    <!-- Related Agenda -->
                    <div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Agenda Terkait' : 'Related Agenda' }}</h5>
                        </div>
                        <div class="card-body">
                            @forelse($relatedAgendaItems as $related)
                                <div class="related-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                    <div class="d-flex">
                                        <div class="agenda-date-small me-3">
                                            <div class="day">{{ $related->date->format('d') }}</div>
                                            <div class="month">{{ $related->date->format('M') }}</div>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                <a href="{{ route('agenda.show', $related->id) }}" class="text-decoration-none">
                                                    {{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($related->title, 50) : \Illuminate\Support\Str::limit($related->title_en, 50) }}
                                                </a>
                                            </h6>
                                            <div class="small text-muted">
                                                <i class="far fa-clock me-1"></i> {{ $related->time->format('H:i') }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Tidak ada agenda terkait.' : 'No related agenda.' }}</p>
                            @endforelse
                        </div>
                    </div>

                    <!-- Latest Announcements -->
                    @include('public.partials.latest-announcements')

                    <!-- Latest News -->
                    @include('public.partials.latest-news')

                    <!-- CTA -->
                    <div class="card border-0 bg-success text-white shadow-sm" data-aos="fade-up">
                        <div class="card-body p-4 text-center">
                            <h5 class="mb-3">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</h5>
                            <p>{{ app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family.' }}</p>
                            <a href="{{ route('registration') }}" class="btn btn-light">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

@push('scripts')
    <script src="{{ asset('js/sticky-sidebar.js') }}"></script>
@endpush
@endsection
