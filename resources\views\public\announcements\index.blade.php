@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Announcements List -->
    <section class="section-padding">
        <div class="container">
            <div class="row justify-content-center">
                @forelse($announcements as $announcement)
                    <div class="col-lg-6 mb-4 fade-in">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <div class="announcement-date mb-2">
                                    <i class="far fa-calendar-alt me-1"></i>
                                    {{ $announcement->start_date->format('d M Y') }} - {{ $announcement->end_date->format('d M Y') }}
                                </div>
                                <h4 class="card-title">{{ app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en }}</h4>
                                <p class="card-text">
                                    @php
                                        $content = app()->getLocale() == 'id' ? $announcement->content : $announcement->content_en;
                                        // Proses HTML entity dengan benar
                                        $content = html_entity_decode($content);
                                        // Hapus tag HTML
                                        $content = strip_tags($content);
                                        // Hapus whitespace berlebih
                                        $content = preg_replace('/\s+/', ' ', $content);
                                        $content = trim($content);
                                        // Batasi panjang teks
                                        $content = \Illuminate\Support\Str::limit($content, 200);
                                    @endphp
                                    {{ $content }}
                                </p>
                                <a href="{{ route('announcements.show', $announcement->id) }}" class="btn btn-sm btn-outline-success">{{ app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More' }}</a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada pengumuman yang tersedia.' : 'No announcements available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $announcements->links() }}
            </div>
        </div>
    </section>
@endsection
