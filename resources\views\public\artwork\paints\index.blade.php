@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Lukisan' : 'Paintings')
@section('meta_description', app()->getLocale() == 'id' ? 'Koleksi lukisan dari para seniman kami' : 'Collection of paintings from our artists')

@section('content')
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title" data-aos="fade-up">{{ app()->getLocale() == 'id' ? 'Lukisan' : 'Paintings' }}</h1>
                    <nav aria-label="breadcrumb" data-aos="fade-up" data-aos-delay="100">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('artwork') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Karya Seni' : 'Artwork' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Lukisan' : 'Paintings' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Paintings Grid -->
    <section class="section-padding">
        <div class="container">
            @if(isset($type))
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h2 data-aos="fade-up">{{ app()->getLocale() == 'id' ? $type->name : $type->name_en }}</h2>
                        @if($type->description || $type->description_en)
                            <p class="lead" data-aos="fade-up" data-aos-delay="100">
                                {{ app()->getLocale() == 'id' ? $type->description : $type->description_en }}
                            </p>
                        @endif
                    </div>
                </div>
            @endif

            <div class="row">
                <!-- Paint Content - 9 columns -->
                <div class="col-lg-9">
                    <div class="row justify-content-center">
                        @forelse($paints as $paint)
                            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                                <div class="artwork-card">
                                    <div class="artwork-image">
                                        @if($paint->image)
                                            <img src="{{ asset('storage/' . $paint->image) }}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}">
                                        @else
                                            <img src="{{ asset('images/artwork-placeholder.jpg') }}" alt="{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}">
                                        @endif
                                        <div class="artwork-overlay">
                                            <a href="{{ route('artwork.paints.show', $paint->id) }}" class="artwork-link">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="artwork-info bg-white p-3">
                                        <h5><a href="{{ route('artwork.paints.show', $paint->id) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? $paint->title : $paint->title_en }}</a></h5>
                                        <p class="artist mb-1"><i class="fas fa-user-alt me-2"></i>{{ app()->getLocale() == 'id' ? $paint->artist : $paint->artist_en }}</p>
                                        @if($paint->year)
                                            <p class="year mb-0"><i class="fas fa-calendar-alt me-2"></i>{{ $paint->year }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12 text-center">
                                <div class="alert alert-info">
                                    {{ app()->getLocale() == 'id' ? 'Belum ada lukisan yang tersedia.' : 'No paintings available yet.' }}
                                </div>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $paints->links() }}
                    </div>
                </div>

                <!-- Submission Info Card - 3 columns -->
                <div class="col-lg-3" data-aos="fade-up" data-aos-delay="200">
                    <div class="sticky-top" style="top: 20px; z-index: 10;">
                        <div class="artwork-card submission-card">
                            <div class="artwork-image submission-image">
                                <div class="submission-icon">
                                    <i class="fas fa-palette fa-3x"></i>
                                </div>
                            </div>
                            <div class="artwork-info bg-white p-3">
                                <h5 class="text-center">{{ app()->getLocale() == 'id' ? 'Kirimkan Karyamu' : 'Submit Your Work' }}</h5>
                                <div class="submission-info mb-3">
                                    <p>{{ app()->getLocale() == 'id' ? 'Anda dapat mengirimkan karya lukisan Anda ke redaksi kami melalui email:' : 'You can submit your painting work to our editorial team via email:' }}</p>
                                    <p class="text-center"><strong><EMAIL></strong></p>
                                    <p>{{ app()->getLocale() == 'id' ? 'Dengan subjek: Lukisan' : 'With subject: Painting' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .page-header {
        background: linear-gradient(rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.65)), url('{{ asset('images/header-bg.jpg') }}') center/cover no-repeat;
        padding: 80px 0;
        margin-bottom: 0;
    }

    .page-title {
        color: white;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .breadcrumb-item, .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
    }

    .breadcrumb-item.active {
        color: white;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: rgba(255, 255, 255, 0.6);
    }

    .section-padding {
        padding: 70px 0;
    }

    .artwork-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .artwork-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .artwork-image {
        position: relative;
        height: 250px;
        overflow: hidden;
    }

    .artwork-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .artwork-card:hover .artwork-image img {
        transform: scale(1.1);
    }

    .artwork-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(25, 135, 84, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .artwork-card:hover .artwork-overlay {
        opacity: 1;
    }

    .artwork-link {
        color: white;
        font-size: 24px;
    }

    .artist, .year, .type {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .submission-card {
        background-color: #f8f9fa;
        border: 2px dashed #28a745;
    }

    .submission-image {
        background-color: rgba(40, 167, 69, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 150px;
    }

    .submission-icon {
        color: #28a745;
    }

    .submission-info {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    @media (max-width: 991.98px) {
        .sticky-top {
            position: relative !important;
            top: 0 !important;
            margin-top: 2rem;
        }
    }
</style>
@endpush
