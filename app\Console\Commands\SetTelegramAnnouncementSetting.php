<?php

namespace App\Console\Commands;

use App\Models\Setting;
use Illuminate\Console\Command;

class SetTelegramAnnouncementSetting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:set-announcement-setting {value=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set the telegram_notify_announcements setting (1 for enabled, 0 for disabled)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $value = $this->argument('value');
        
        if (!in_array($value, ['0', '1'])) {
            $this->error('Value must be 0 or 1');
            return 1;
        }
        
        Setting::updateOrCreate(
            ['key' => 'telegram_notify_announcements'],
            ['value' => $value, 'group' => 'telegram']
        );
        
        $status = $value === '1' ? 'enabled' : 'disabled';
        $this->info("Telegram announcement notifications {$status} successfully.");
        
        return 0;
    }
}
