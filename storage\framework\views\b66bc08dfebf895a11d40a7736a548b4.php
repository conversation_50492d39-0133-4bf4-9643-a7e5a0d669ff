<?php $__env->startSection('title', 'Literature'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Literature</h1>
        <a href="<?php echo e(route('admin.artwork.literature.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Literature
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if($literature->isEmpty()): ?>
                <div class="alert alert-info">
                    No literature found. <a href="<?php echo e(route('admin.artwork.literature.create')); ?>">Create your first literature</a>.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Author</th>
                                <th>Type</th>
                                <th>Year</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $literature; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($item->title); ?></td>
                                    <td><?php echo e($item->author); ?></td>
                                    <td><?php echo e($item->litType ? $item->litType->name : $item->type); ?></td>
                                    <td><?php echo e($item->year ?: '-'); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('admin.artwork.literature.edit', $item->id)); ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <form action="<?php echo e(route('admin.artwork.literature.destroy', $item->id)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this literature?');">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    <?php echo e($literature->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/artwork/literature/index.blade.php ENDPATH**/ ?>