<template>
  <div class="menu-manager">
    <div class="row mb-3">
      <div class="col-md-6">
        <h5>Menu Items</h5>
      </div>
    </div>
    <div class="row mb-4">
      <div class="col-md-12">
        <button class="btn btn-primary" @click="showAddItemModal()">
          <i class="fas fa-plus"></i> Add Menu Item
        </button>
      </div>
    </div>

    <div v-if="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-2">Loading menu items...</p>
    </div>

    <div v-else-if="menuItems.length === 0" class="alert alert-info">
      No menu items found. Click "Add Menu Item" to create one.
      <div class="mt-3">
        <button class="btn btn-sm btn-secondary" @click="loadMenuItems">
          <i class="fas fa-sync"></i> Refresh Menu Items
        </button>
      </div>
    </div>

    <div v-else class="menu-items-container">
      <div class="mb-3">
        <div class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i>
          <strong>Cara Penggunaan:</strong>
          <ol class="mb-0 mt-2">
            <li class="mb-2">Drag item secara vertikal untuk mengubah urutan</li>
            <li class="mb-2">Untuk membuat submenu, klik tombol <span class="badge bg-primary"><i class="fas fa-level-down-alt fa-rotate-90"></i> Nest</span> pada item yang ingin dijadikan submenu (item tersebut akan menjadi submenu dari item di atasnya)</li>
            <li class="mb-2">Untuk menaikkan level submenu, klik tombol <span class="badge bg-secondary"><i class="fas fa-level-up-alt fa-rotate-90"></i> Up</span> pada submenu yang ingin dinaikkan levelnya</li>
            <li class="mb-2">Setelah selesai mengatur menu, jangan lupa klik tombol <span class="badge bg-success"><i class="fas fa-save"></i> Save Menu Order</span> di bawah</li>
          </ol>
        </div>
      </div>

      <draggable
        v-model="menuItems"
        group="menu-items"
        tag="ul"
        class="menu-tree list-unstyled"
        item-key="id"
        handle=".drag-handle"
        @end="onDragEnd"
        :animation="150"
        ghost-class="ghost-class"
        chosen-class="chosen-class"
      >
        <template #item="{ element }">
          <menu-item
            :item="element"
            :level="0"
            @edit="showEditItemModal"
            @delete="confirmDeleteItem"
            @add-child="showAddItemModal"
            @nest-in="nestIn"
            @nest-out="nestOut"
            @drag-end="onDragEnd"
          />
        </template>
      </draggable>

      <div class="mt-3">
        <button class="btn btn-success btn-lg" @click="saveMenuOrder">
          <i class="fas fa-save"></i> Save Menu Order
        </button>
        <div class="text-muted mt-2">
          <small><i class="fas fa-info-circle"></i> Jangan lupa klik tombol ini setelah selesai mengatur menu</small>
        </div>
      </div>
    </div>

    <!-- Add/Edit Menu Item Modal -->
    <div class="modal fade" id="menuItemModal" tabindex="-1" aria-labelledby="menuItemModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="menuItemModalLabel">{{ isEditing ? 'Edit' : 'Add' }} Menu Item</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveMenuItem">
              <div class="mb-3">
                <label for="title" class="form-label">Title (Indonesian) <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="title" v-model="currentItem.title" required>
              </div>

              <div class="mb-3">
                <label for="title_en" class="form-label">Title (English)</label>
                <input type="text" class="form-control" id="title_en" v-model="currentItem.title_en">
              </div>

              <div class="mb-3">
                <label for="url" class="form-label">URL</label>
                <input type="text" class="form-control" id="url" v-model="currentItem.url" placeholder="e.g., /contact">
                <small class="form-text text-muted">Leave empty if this is a dropdown parent.</small>
              </div>

              <div class="mb-3">
                <label for="parent_id" class="form-label">Parent Menu</label>
                <select class="form-select" id="parent_id" v-model="currentItem.parent_id">
                  <option :value="null">None (Top Level)</option>
                  <option v-for="item in availableParents" :key="item.id" :value="item.id">{{ item.title }}</option>
                </select>
              </div>

              <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_active" v-model="currentItem.is_active">
                <label class="form-check-label" for="is_active">Active</label>
              </div>

              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary">Save</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete this menu item? All child items will also be deleted.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-danger" @click="deleteMenuItem">Delete</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import draggable from 'vuedraggable';
import MenuItem from './MenuItem.vue';
import axios from 'axios';

export default {
  components: {
    draggable,
    MenuItem
  },

  setup() {
    const menuItems = ref([]);
    const loading = ref(true);
    const isEditing = ref(false);
    const currentItem = ref({
      title: '',
      title_en: '',
      url: '',
      parent_id: null,
      is_active: true
    });
    const itemToDelete = ref(null);
    const menuItemModal = ref(null);
    const deleteModal = ref(null);

    // Computed property to get available parents (to prevent circular references)
    const availableParents = computed(() => {
      if (!isEditing.value) {
        return menuItems.value;
      }

      // When editing, exclude the current item and its children
      return menuItems.value.filter(item => {
        return item.id !== currentItem.value.id && !isChildOf(item, currentItem.value.id);
      });
    });

    // Check if an item is a child of another item
    const isChildOf = (item, parentId) => {
      if (item.children && item.children.length > 0) {
        for (const child of item.children) {
          if (child.id === parentId || isChildOf(child, parentId)) {
            return true;
          }
        }
      }
      return false;
    };

    // Ensure all menu items have a children array
    const ensureChildrenArrays = (items) => {
      items.forEach(item => {
        // Initialize children array if it doesn't exist
        if (!item.children) {
          item.children = [];
        }

        // Process children recursively
        if (item.children.length > 0) {
          ensureChildrenArrays(item.children);
        }
      });
      return items;
    };

    // Load menu items
    const loadMenuItems = async () => {
      loading.value = true;

      try {
        console.log('Loading menu items...');

        // Add a timestamp to prevent caching
        const response = await axios.get('/admin/api/menu/items', {
          params: { _t: new Date().getTime() }
        });

        console.log('Menu items response:', response.data);

        if (response.data && response.data.menuItems && response.data.menuItems.length > 0) {
          // Ensure all items have a children array
          menuItems.value = ensureChildrenArrays(response.data.menuItems);
          console.log('Menu items loaded and processed:', menuItems.value.length);
        } else {
          console.warn('No menu items found in response, using static data');

          // Use static data if no menu items are returned
          menuItems.value = [
            {
              id: 1,
              title: 'Beranda',
              title_en: 'Home',
              url: '/',
              route_name: 'home',
              target: '_self',
              parent_id: null,
              order: 1,
              is_active: true,
              children: []
            },
            {
              id: 2,
              title: 'Profil',
              title_en: 'Profile',
              url: '#',
              route_name: null,
              target: '_self',
              parent_id: null,
              order: 2,
              is_active: true,
              children: [
                {
                  id: 3,
                  title: 'Tentang Kami',
                  title_en: 'About Us',
                  url: '/about',
                  route_name: 'about',
                  target: '_self',
                  parent_id: 2,
                  order: 1,
                  is_active: true,
                  children: []
                }
              ]
            },
            {
              id: 4,
              title: 'Kontak',
              title_en: 'Contact',
              url: '/contact',
              route_name: 'contact',
              target: '_self',
              parent_id: null,
              order: 3,
              is_active: true,
              children: []
            }
          ];
        }

        loading.value = false;
      } catch (error) {
        console.error('Error loading menu items:', error);
        console.error('Error details:', error.response ? error.response.data : 'No response data');

        // Use static data if there's an error
        console.warn('Using static data due to error');
        menuItems.value = [
          {
            id: 1,
            title: 'Beranda',
            title_en: 'Home',
            url: '/',
            route_name: 'home',
            target: '_self',
            parent_id: null,
            order: 1,
            is_active: true,
            children: []
          },
          {
            id: 2,
            title: 'Profil',
            title_en: 'Profile',
            url: '#',
            route_name: null,
            target: '_self',
            parent_id: null,
            order: 2,
            is_active: true,
            children: []
          },
          {
            id: 3,
            title: 'Kontak',
            title_en: 'Contact',
            url: '/contact',
            route_name: 'contact',
            target: '_self',
            parent_id: null,
            order: 3,
            is_active: true,
            children: []
          }
        ];

        // Ensure all items have a children array
        ensureChildrenArrays(menuItems.value);
        loading.value = false;
      }
    };

    // Show modal to add a new menu item
    const showAddItemModal = (parentId = null) => {
      isEditing.value = false;
      currentItem.value = {
        title: '',
        title_en: '',
        url: '',
        parent_id: parentId,
        is_active: true
      };

      const modal = new bootstrap.Modal(document.getElementById('menuItemModal'));
      modal.show();
    };

    // Show modal to edit a menu item
    const showEditItemModal = (item) => {
      isEditing.value = true;
      currentItem.value = { ...item };

      const modal = new bootstrap.Modal(document.getElementById('menuItemModal'));
      modal.show();
    };

    // Save a menu item (create or update)
    const saveMenuItem = async () => {
      try {
        let response;

        // Make sure we're sending a clean object without children array
        // (children will be handled by the backend)
        const itemToSave = { ...currentItem.value };
        delete itemToSave.children;

        if (isEditing.value) {
          // Update existing item
          console.log('Updating menu item:', itemToSave);
          response = await axios.put(`/admin/api/menu/items/${itemToSave.id}`, itemToSave);
        } else {
          // Create new item
          console.log('Creating new menu item:', itemToSave);
          response = await axios.post('/admin/api/menu/items', itemToSave);
        }

        console.log('Save response:', response.data);

        // Reload menu items
        await loadMenuItems();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('menuItemModal'));
        modal.hide();
      } catch (error) {
        console.error('Error saving menu item:', error);

        let errorMessage = 'Error saving menu item. Please try again.';

        if (error.response) {
          console.error('Error response:', error.response);
          console.error('Error details:', error.response.data);

          if (error.response.data.errors) {
            // Format validation errors
            const errors = error.response.data.errors;
            errorMessage = Object.keys(errors)
              .map(field => `${field}: ${errors[field].join(', ')}`)
              .join('\n');
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.error) {
            errorMessage = error.response.data.error;
          }
        }

        alert(errorMessage);
      }
    };

    // Show confirmation modal to delete a menu item
    const confirmDeleteItem = (item) => {
      itemToDelete.value = item;

      const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
      modal.show();
    };

    // Delete a menu item
    const deleteMenuItem = async () => {
      if (!itemToDelete.value) return;

      try {
        console.log(`Deleting menu item with ID: ${itemToDelete.value.id}`);
        await axios.delete(`/admin/api/menu/items/${itemToDelete.value.id}`);

        // Reload menu items
        loadMenuItems();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        modal.hide();
      } catch (error) {
        console.error('Error deleting menu item:', error);
        console.error('Error details:', error.response ? error.response.data : 'No response data');
        alert('Error deleting menu item. Please try again.');
      }
    };

    // Helper function to find an item by ID in the menu structure
    const findItemById = (items, id) => {
      for (const item of items) {
        if (item.id === id) return item;
        if (item.children && item.children.length > 0) {
          const found = findItemById(item.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    // Helper function to remove an item from the menu structure
    const removeItemFromArray = (items, id) => {
      for (let i = 0; i < items.length; i++) {
        if (items[i].id === id) {
          items.splice(i, 1);
          return true;
        }
        if (items[i].children && items[i].children.length > 0) {
          if (removeItemFromArray(items[i].children, id)) {
            return true;
          }
        }
      }
      return false;
    };

    // Helper function to get the level of an item in the menu structure
    const getItemLevel = (items, id, level = 0) => {
      for (const item of items) {
        if (item.id === id) return level;
        if (item.children && item.children.length > 0) {
          const foundLevel = getItemLevel(item.children, id, level + 1);
          if (foundLevel !== -1) return foundLevel;
        }
      }
      return -1;
    };

    // Function to nest an item under the previous sibling
    const nestIn = (itemId) => {
      console.log(`Nesting item ${itemId}`);

      // Find the item in our data structure
      const itemToNest = findItemById(menuItems.value, itemId);
      if (!itemToNest) {
        console.error('Could not find item to nest in data structure');
        showMessage('error', 'Item not found in menu structure');
        return;
      }

      // Helper function to show messages
      const showMessage = (type, text) => {
        const message = document.createElement('div');
        message.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'warning'} position-fixed top-0 end-0 m-3`;
        message.style.zIndex = '9999';
        message.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'} me-2"></i> ${text}`;
        document.body.appendChild(message);
        setTimeout(() => message.remove(), 3000);
      };

      // Find the item's position in the array
      const findItemPosition = (items, id, path = []) => {
        for (let i = 0; i < items.length; i++) {
          if (items[i].id === id) {
            return [...path, i];
          }
          if (items[i].children && items[i].children.length > 0) {
            const foundInChildren = findItemPosition(items[i].children, id, [...path, i, 'children']);
            if (foundInChildren) return foundInChildren;
          }
        }
        return null;
      };

      const position = findItemPosition(menuItems.value, itemId);
      if (!position) {
        console.error('Could not find item position in menu structure');
        showMessage('error', 'Item position not found');
        return;
      }

      console.log('Item position:', position);

      // Check if this is the first item in its level
      if (position.length === 1 && position[0] === 0) {
        // First item at root level, can't nest
        showMessage('warning', 'First item can\'t be nested (no previous item)');
        return;
      }

      // Get the parent array and index
      let parentArray = menuItems.value;
      let itemIndex = position[0];

      if (position.length > 1) {
        // Item is in a nested array
        parentArray = menuItems.value;
        for (let i = 0; i < position.length - 1; i++) {
          if (typeof position[i] === 'string') {
            // This is a property name like 'children'
            parentArray = parentArray[position[i]];
          } else {
            // This is an array index
            parentArray = parentArray[position[i]];
          }
        }
        itemIndex = position[position.length - 1];
      }

      console.log('Parent array:', parentArray);
      console.log('Item index:', itemIndex);

      // Can't nest if it's the first item in its level
      if (itemIndex === 0) {
        showMessage('warning', 'First item can\'t be nested (no previous item)');
        return;
      }

      // Get the previous sibling
      const previousSibling = parentArray[itemIndex - 1];
      if (!previousSibling) {
        showMessage('error', 'Previous sibling not found');
        return;
      }

      console.log('Previous sibling:', previousSibling);

      // Check if we're not creating a circular reference
      if (itemToNest.id === previousSibling.id || isChildOf(itemToNest, previousSibling.id)) {
        showMessage('error', 'Cannot create circular reference');
        return;
      }

      // Get the level of the previous item
      const previousItemLevel = getItemLevel(menuItems.value, previousSibling.id);
      console.log('Previous item level:', previousItemLevel);

      // Only allow nesting if it doesn't exceed level 3
      if (previousItemLevel >= 2) {
        showMessage('warning', 'Maximum nesting level reached (3 levels)');
        return;
      }

      // Ensure the previous sibling has a children array
      if (!previousSibling.children) {
        previousSibling.children = [];
        console.log('Created children array for previous sibling');
      }

      // Remove the item from its current position
      const itemToMove = parentArray.splice(itemIndex, 1)[0];
      console.log('Item removed from original position:', itemToMove);

      // Update parent_id
      itemToMove.parent_id = previousSibling.id;

      // Add to children of previous sibling
      previousSibling.children.push(itemToMove);
      console.log('Item added to previous sibling children:', previousSibling.children);

      // Show success message
      showMessage('success', 'Item nested successfully!');
    };

    // Function to move an item up one level
    const nestOut = (itemId) => {
      console.log(`Moving item ${itemId} up one level`);

      // Helper function to show messages
      const showMessage = (type, text) => {
        const message = document.createElement('div');
        message.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'warning'} position-fixed top-0 end-0 m-3`;
        message.style.zIndex = '9999';
        message.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'} me-2"></i> ${text}`;
        document.body.appendChild(message);
        setTimeout(() => message.remove(), 3000);
      };

      // Find the item in our data structure
      const itemToMoveUp = findItemById(menuItems.value, itemId);
      if (!itemToMoveUp) {
        console.error('Could not find item to move up in data structure');
        showMessage('error', 'Item not found in menu structure');
        return;
      }

      // If item has no parent, it's already at the top level
      if (!itemToMoveUp.parent_id) {
        showMessage('warning', 'Item is already at the top level');
        return;
      }

      // Find the parent
      const parentItem = findItemById(menuItems.value, itemToMoveUp.parent_id);
      if (!parentItem) {
        console.error('Could not find parent item in data structure');
        showMessage('error', 'Parent item not found');
        return;
      }

      console.log('Parent item:', parentItem);

      // Find the parent's position
      const findItemPosition = (items, id, path = []) => {
        for (let i = 0; i < items.length; i++) {
          if (items[i].id === id) {
            return [...path, i];
          }
          if (items[i].children && items[i].children.length > 0) {
            const foundInChildren = findItemPosition(items[i].children, id, [...path, i, 'children']);
            if (foundInChildren) return foundInChildren;
          }
        }
        return null;
      };

      const parentPosition = findItemPosition(menuItems.value, parentItem.id);
      if (!parentPosition) {
        console.error('Could not find parent position in menu structure');
        showMessage('error', 'Parent position not found');
        return;
      }

      console.log('Parent position:', parentPosition);

      // Find the item's index in the parent's children
      const itemIndex = parentItem.children.findIndex(child => child.id === itemId);
      if (itemIndex === -1) {
        console.error('Could not find item in parent\'s children');
        showMessage('error', 'Item not found in parent\'s children');
        return;
      }

      console.log('Item index in parent\'s children:', itemIndex);

      // Remove the item from parent's children
      const itemToMove = parentItem.children.splice(itemIndex, 1)[0];
      console.log('Item removed from parent\'s children:', itemToMove);

      // Find the grandparent (if any)
      let grandparentId = null;
      if (parentItem.parent_id) {
        grandparentId = parentItem.parent_id;
      }

      // Update parent_id to grandparent's id (or null if no grandparent)
      itemToMove.parent_id = grandparentId;

      // Get the parent array and index for insertion
      let targetArray = menuItems.value;
      let insertIndex = 0;

      // Simplified logic for finding the target array
      if (parentPosition.length === 1) {
        // Parent is at root level, insert at root level after parent
        targetArray = menuItems.value;
        insertIndex = parentPosition[0] + 1;
        console.log('Inserting at root level after parent');
      } else {
        // Parent is nested, find the grandparent array
        let currentArray = menuItems.value;
        let path = [];

        // Build the path to the grandparent array
        for (let i = 0; i < parentPosition.length; i++) {
          if (i % 2 === 0) { // Even indices are array indices
            path.push(parentPosition[i]);
          }
        }

        // Remove the last element (which is the parent's index)
        if (path.length > 0) {
          path.pop();
        }

        // Navigate to the grandparent array
        for (let i = 0; i < path.length; i++) {
          currentArray = currentArray[path[i]].children;
        }

        // If we have a path, insert after the last item in the path
        if (path.length > 0) {
          targetArray = currentArray;
          insertIndex = path[path.length - 1] + 1;
        } else {
          // If no path, insert at root level
          targetArray = menuItems.value;
          insertIndex = 0;
        }

        console.log('Path to grandparent:', path);
        console.log('Target array:', targetArray);
        console.log('Insert index:', insertIndex);
      }

      // Insert the item at the new position
      targetArray.splice(insertIndex, 0, itemToMove);
      console.log('Item inserted at new position');

      // Show success message
      showMessage('success', 'Item moved up one level!');
    };

    // Handle drag end event (only for vertical reordering now)
    const onDragEnd = (evt) => {
      // This function is called when a drag operation ends
      console.log('Drag ended:', evt);
      // We'll update the order when the user clicks the Save button
    };

    // Save menu order
    const saveMenuOrder = async () => {
      // Make sure all items have children arrays before saving
      ensureChildrenArrays(menuItems.value);

      // Flatten the nested structure and update order and parent_id
      const flattenedItems = [];

      const flattenItems = (items, parentId = null, order = 0) => {
        items.forEach((item, index) => {
          const newOrder = order + index;

          flattenedItems.push({
            id: item.id,
            order: newOrder,
            parent_id: parentId
          });

          if (item.children && item.children.length > 0) {
            flattenItems(item.children, item.id, 0);
          }
        });
      };

      flattenItems(menuItems.value);

      try {
        console.log('Saving menu order with items:', flattenedItems);
        const response = await axios.post('/admin/api/menu/items/update-order', { items: flattenedItems });
        console.log('Save order response:', response.data);

        // Helper function to show messages
        const showMessage = (type, text) => {
          const message = document.createElement('div');
          message.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'warning'} position-fixed top-0 end-0 m-3`;
          message.style.zIndex = '9999';
          message.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'} me-2"></i> ${text}`;
          document.body.appendChild(message);
          setTimeout(() => message.remove(), 3000);
        };

        // Show success message
        showMessage('success', 'Menu order saved successfully!');
      } catch (error) {
        console.error('Error saving menu order:', error);

        let errorMessage = 'Error saving menu order. Please try again.';

        if (error.response) {
          console.error('Error response:', error.response);
          console.error('Error details:', error.response.data);

          if (error.response.data.errors) {
            // Format validation errors
            const errors = error.response.data.errors;
            errorMessage = Object.keys(errors)
              .map(field => `${field}: ${errors[field].join(', ')}`)
              .join('\n');
          } else if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.error) {
            errorMessage = error.response.data.error;
          }
        }

        alert(errorMessage);
      }
    };

    onMounted(() => {
      loadMenuItems();
    });

    return {
      menuItems,
      loading,
      isEditing,
      currentItem,
      availableParents,
      loadMenuItems,
      showAddItemModal,
      showEditItemModal,
      saveMenuItem,
      confirmDeleteItem,
      deleteMenuItem,
      nestIn,
      nestOut,
      onDragEnd,
      saveMenuOrder
    };
  }
};
</script>

<style scoped>
.menu-tree {
  margin: 0;
  padding: 0;
}

.menu-items-container {
  /* Menghapus max-height dan overflow-y agar kartu menyesuaikan dengan jumlah menu items */
}

.btn-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* Ghost and chosen classes for draggable */
.ghost-class {
  opacity: 0.5;
  background: #c8ebfb;
  border: 2px dashed #0d6efd;
  position: relative;
}

.ghost-class::before {
  content: "Drag horizontally to nest/unnest";
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: #0d6efd;
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
}

.chosen-class {
  background: #e9f5ff;
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
  transition: all 0.2s ease;
}

/* Nesting guide */
.nesting-guide {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #0d6efd;
}

/* Horizontal drag animation */
@keyframes horizontal-pulse {
  0% { transform: translateX(0); }
  25% { transform: translateX(5px); }
  50% { transform: translateX(0); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.horizontal-drag-handle:hover {
  animation: horizontal-pulse 1.5s infinite;
}
</style>

