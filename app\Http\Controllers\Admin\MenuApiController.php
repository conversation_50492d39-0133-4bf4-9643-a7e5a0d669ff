<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Validator;

class MenuApiController extends Controller
{
    /**
     * Get all menu items.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMenuItems()
    {
        try {
            // Get all root menu items with their children
            $menuItems = MenuItem::whereNull('parent_id')
                ->with('children.children.children') // Load up to 4 levels of nesting
                ->orderBy('order', 'asc')
                ->get();

            // Log the menu items for debugging
            \Log::info('Menu items retrieved: ' . $menuItems->count());
            \Log::info('Menu items data: ' . json_encode($menuItems));

            // If no menu items found, create some dummy items for testing
            if ($menuItems->count() === 0) {
                \Log::warning('No menu items found, returning dummy data');

                // Create dummy menu items
                $dummyItems = [
                    [
                        'id' => 1,
                        'title' => 'Beranda',
                        'title_en' => 'Home',
                        'url' => '/',
                        'route_name' => 'home',
                        'target' => '_self',
                        'parent_id' => null,
                        'order' => 1,
                        'is_active' => true,
                        'children' => []
                    ],
                    [
                        'id' => 2,
                        'title' => 'Profil',
                        'title_en' => 'Profile',
                        'url' => '#',
                        'route_name' => null,
                        'target' => '_self',
                        'parent_id' => null,
                        'order' => 2,
                        'is_active' => true,
                        'children' => [
                            [
                                'id' => 3,
                                'title' => 'Tentang Kami',
                                'title_en' => 'About Us',
                                'url' => '/about',
                                'route_name' => 'about',
                                'target' => '_self',
                                'parent_id' => 2,
                                'order' => 1,
                                'is_active' => true,
                                'children' => []
                            ]
                        ]
                    ],
                    [
                        'id' => 4,
                        'title' => 'Kontak',
                        'title_en' => 'Contact',
                        'url' => '/contact',
                        'route_name' => 'contact',
                        'target' => '_self',
                        'parent_id' => null,
                        'order' => 3,
                        'is_active' => true,
                        'children' => []
                    ]
                ];

                return response()->json([
                    'menuItems' => $dummyItems
                ]);
            }

            return response()->json([
                'menuItems' => $menuItems
            ]);
        } catch (\Exception $e) {
            \Log::error('Error in getMenuItems: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());

            return response()->json([
                'error' => 'An error occurred while retrieving menu items',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * This method is no longer needed as we don't use menus anymore.
     * Keeping it for backward compatibility but returning a dummy menu.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMenus()
    {
        // Return a dummy menu for backward compatibility
        return response()->json([
            [
                'id' => 1,
                'name' => 'Main Menu',
                'location' => 'main',
                'is_active' => true
            ]
        ]);
    }

    /**
     * Create a new menu item.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createMenuItem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'url' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Set default order if not provided
            if (!$request->has('order')) {
                if ($request->parent_id) {
                    $maxOrder = MenuItem::where('parent_id', $request->parent_id)->max('order');
                } else {
                    $maxOrder = MenuItem::whereNull('parent_id')->max('order');
                }
                $request->merge(['order' => $maxOrder ? $maxOrder + 1 : 1]);
            }

            // Create a filtered array of attributes to create
            $attributes = $request->only([
                'title',
                'title_en',
                'url',
                'route_name',
                'target',
                'parent_id',
                'order',
                'is_active'
            ]);

            $menuItem = MenuItem::create($attributes);

            return response()->json([
                'message' => 'Menu item created successfully',
                'menuItem' => $menuItem
            ], 201);
        } catch (\Exception $e) {
            \Log::error('Error creating menu item: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while creating the menu item',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a menu item.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateMenuItem(Request $request, $id)
    {
        $menuItem = MenuItem::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'url' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Prevent circular reference
        if ($request->parent_id == $id) {
            return response()->json([
                'errors' => ['parent_id' => ['A menu item cannot be its own parent.']]
            ], 422);
        }

        try {
            // Create a filtered array of attributes to update
            $attributes = $request->only([
                'title',
                'title_en',
                'url',
                'route_name',
                'target',
                'parent_id',
                'order',
                'is_active'
            ]);

            // Update the menu item with only the valid attributes
            $menuItem->update($attributes);

            return response()->json([
                'message' => 'Menu item updated successfully',
                'menuItem' => $menuItem
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating menu item: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while updating the menu item',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a menu item.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMenuItem($id)
    {
        $menuItem = MenuItem::findOrFail($id);
        $menuItem->delete();

        return response()->json([
            'message' => 'Menu item deleted successfully'
        ]);
    }

    /**
     * Update the order of menu items.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateMenuItemsOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.id' => 'required|exists:menu_items,id',
            'items.*.order' => 'required|integer',
            'items.*.parent_id' => 'nullable|exists:menu_items,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            \DB::beginTransaction();

            foreach ($request->items as $item) {
                // Prevent circular reference
                if (isset($item['parent_id']) && $item['parent_id'] == $item['id']) {
                    \DB::rollBack();
                    return response()->json([
                        'errors' => ['parent_id' => ['A menu item cannot be its own parent.']]
                    ], 422);
                }

                // Check for circular references in the hierarchy
                if (isset($item['parent_id'])) {
                    $parentId = $item['parent_id'];
                    $itemId = $item['id'];

                    // Check if this would create a circular reference
                    $parent = MenuItem::find($parentId);
                    while ($parent && $parent->parent_id) {
                        if ($parent->parent_id == $itemId) {
                            \DB::rollBack();
                            return response()->json([
                                'errors' => ['parent_id' => ['This would create a circular reference in the menu hierarchy.']]
                            ], 422);
                        }
                        $parent = MenuItem::find($parent->parent_id);
                    }
                }

                MenuItem::where('id', $item['id'])->update([
                    'order' => $item['order'],
                    'parent_id' => $item['parent_id'] ?? null,
                ]);
            }

            \DB::commit();

            return response()->json([
                'message' => 'Menu items order updated successfully'
            ]);
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Error updating menu items order: ' . $e->getMessage());
            return response()->json([
                'error' => 'An error occurred while updating menu items order',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
