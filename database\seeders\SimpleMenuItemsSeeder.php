<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItem;

class SimpleMenuItemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing menu items
        MenuItem::truncate();

        // Create a few basic menu items
        $home = MenuItem::create([
            'title' => 'Beranda',
            'title_en' => 'Home',
            'route_name' => 'home',
            'order' => 1,
            'is_active' => true,
        ]);
        
        $profile = MenuItem::create([
            'title' => 'Profil',
            'title_en' => 'Profile',
            'url' => '#',
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Add child items to Profile
        MenuItem::create([
            'title' => 'Tentang Kami',
            'title_en' => 'About Us',
            'route_name' => 'about',
            'parent_id' => $profile->id,
            'order' => 1,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'title' => 'Visi & Misi',
            'title_en' => 'Vision & Mission',
            'route_name' => 'vision-mission',
            'parent_id' => $profile->id,
            'order' => 2,
            'is_active' => true,
        ]);
        
        // Add more top-level items
        MenuItem::create([
            'title' => 'Program',
            'title_en' => 'Programs',
            'route_name' => 'programs',
            'order' => 3,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'title' => 'Fasilitas',
            'title_en' => 'Facilities',
            'route_name' => 'facilities',
            'order' => 4,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'title' => 'Berita',
            'title_en' => 'News',
            'route_name' => 'news',
            'order' => 5,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'title' => 'Galeri',
            'title_en' => 'Gallery',
            'route_name' => 'gallery',
            'order' => 6,
            'is_active' => true,
        ]);
        
        MenuItem::create([
            'title' => 'Kontak',
            'title_en' => 'Contact',
            'route_name' => 'contact',
            'order' => 7,
            'is_active' => true,
        ]);
    }
}
