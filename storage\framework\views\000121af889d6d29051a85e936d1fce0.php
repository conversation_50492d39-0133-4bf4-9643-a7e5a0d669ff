<?php $__env->startSection('title', 'Manage Swiper Images'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <h1 class="mt-4">Manage Swiper Images</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Swiper Images</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div><i class="fas fa-images me-1"></i> Swiper Images</div>
            <?php if($canAddMore): ?>
                <a href="<?php echo e(route('admin.swipers.create')); ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> Add New Image
                </a>
            <?php else: ?>
                <button class="btn btn-secondary btn-sm" disabled title="Maximum of 3 swiper images reached">
                    <i class="fas fa-plus me-1"></i> Add New Image
                </button>
            <?php endif; ?>
        </div>
        <div class="card-body">

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-1"></i> These images will be displayed in the carousel on the home page. Maximum of 5 images allowed.
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Image</th>
                            <th>Title</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $swipers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $swiper): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($swiper->order); ?></td>
                                <td>
                                    <img src="<?php echo e(asset('storage/' . $swiper->image)); ?>" alt="<?php echo e($swiper->title); ?>" class="img-thumbnail" style="max-height: 100px;">
                                </td>
                                <td>
                                    <strong><?php echo e($swiper->title); ?></strong>
                                    <?php if($swiper->title_en): ?>
                                        <br><small class="text-muted"><?php echo e($swiper->title_en); ?></small>
                                    <?php endif; ?>
                                </td>

                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.swipers.edit', $swiper->id)); ?>" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.swipers.show', $swiper->id)); ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.swipers.destroy', $swiper->id)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this swiper image?');">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="4" class="text-center">No swiper images found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/swipers/index.blade.php ENDPATH**/ ?>