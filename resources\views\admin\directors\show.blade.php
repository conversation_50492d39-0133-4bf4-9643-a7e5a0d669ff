@extends('admin.layouts.app')

@section('title', 'View Director')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Director Profile</h1>
        <div>
            <a href="{{ route('admin.directors-insight.index', ['tab' => 'director']) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Directors
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Director Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="director-profile-header">
                    @if($director->image)
                        <img src="{{ asset('storage/' . $director->image) }}" alt="{{ $director->name }}" class="director-profile-image">
                    @else
                        <div class="director-profile-image-placeholder">
                            <i class="fas fa-user-tie fa-4x text-muted"></i>
                        </div>
                    @endif
                </div>
                <div class="card-body text-center">
                    <h3 class="card-title mb-1">{{ $director->name }}</h3>
                    <div class="position-badge mb-3">{{ $director->position }}</div>

                    <div class="director-stats">
                        <div class="stat-item">
                            <div class="stat-value">{{ $director->order }}</div>
                            <div class="stat-label">Display Order</div>
                        </div>
                        <div class="stat-divider"></div>
                        <div class="stat-item">
                            <div class="stat-value">
                                @if($director->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </div>
                            <div class="stat-label">Status</div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.directors.edit', $director) }}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>Edit Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Director Information Tabs -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <ul class="nav nav-tabs card-header-tabs" id="directorTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="indonesian-tab" data-bs-toggle="tab" data-bs-target="#indonesian" type="button" role="tab" aria-controls="indonesian" aria-selected="true">
                                <i class="fas fa-info-circle me-2"></i>Indonesian
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="english-tab" data-bs-toggle="tab" data-bs-target="#english" type="button" role="tab" aria-controls="english" aria-selected="false">
                                <i class="fas fa-globe me-2"></i>English
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="directorTabsContent">
                        <!-- Indonesian Tab -->
                        <div class="tab-pane fade show active" id="indonesian" role="tabpanel" aria-labelledby="indonesian-tab">
                            <div class="info-section">
                                <div class="info-header">
                                    <i class="fas fa-user me-2"></i>
                                    <h5 class="mb-0">Personal Information</h5>
                                </div>
                                <div class="info-content">
                                    <div class="info-item">
                                        <div class="info-label">Name</div>
                                        <div class="info-value">{{ $director->name }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">Position</div>
                                        <div class="info-value">{{ $director->position }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-section">
                                <div class="info-header">
                                    <i class="fas fa-quote-left me-2"></i>
                                    <h5 class="mb-0">Biography</h5>
                                </div>
                                <div class="info-content">
                                    <div class="bio-content">
                                        {!! $director->bio ?: '<div class="text-muted">No biography provided</div>' !!}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- English Tab -->
                        <div class="tab-pane fade" id="english" role="tabpanel" aria-labelledby="english-tab">
                            <div class="info-section">
                                <div class="info-header">
                                    <i class="fas fa-user me-2"></i>
                                    <h5 class="mb-0">Personal Information</h5>
                                </div>
                                <div class="info-content">
                                    <div class="info-item">
                                        <div class="info-label">Name</div>
                                        <div class="info-value">{{ $director->name_en ?: $director->name }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">Position</div>
                                        <div class="info-value">{{ $director->position_en ?: $director->position }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-section">
                                <div class="info-header">
                                    <i class="fas fa-quote-left me-2"></i>
                                    <h5 class="mb-0">Biography</h5>
                                </div>
                                <div class="info-content">
                                    <div class="bio-content">
                                        {!! $director->bio_en ?: '<div class="text-muted">No English biography provided</div>' !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* Director Profile Card Styles */
    .director-profile-header {
        height: 120px;
        background: linear-gradient(135deg, #4CAF50, #2E7D32);
        border-radius: 0.375rem 0.375rem 0 0;
        position: relative;
    }

    .director-profile-image {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 50%;
        border: 5px solid #fff;
        position: absolute;
        bottom: -75px;
        left: 50%;
        transform: translateX(-50%);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .director-profile-image-placeholder {
        width: 150px;
        height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-radius: 50%;
        border: 5px solid #fff;
        position: absolute;
        bottom: -75px;
        left: 50%;
        transform: translateX(-50%);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .card-title {
        margin-top: 60px;
        font-weight: 600;
        color: #333;
    }

    .position-badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.85em;
        font-weight: 500;
        color: #fff;
        background-color: #4CAF50;
        border-radius: 30px;
    }

    .director-stats {
        display: flex;
        justify-content: center;
        margin-top: 1.5rem;
    }

    .stat-item {
        text-align: center;
        padding: 0 1rem;
    }

    .stat-divider {
        width: 1px;
        background-color: #e9ecef;
    }

    .stat-value {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
    }

    .stat-label {
        font-size: 0.85rem;
        color: #6c757d;
    }

    /* Information Sections Styles */
    .info-section {
        margin-bottom: 2rem;
    }

    .info-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
        color: #4CAF50;
    }

    .info-content {
        padding: 0.5rem;
    }

    .info-item {
        display: flex;
        margin-bottom: 0.75rem;
    }

    .info-label {
        width: 120px;
        font-weight: 500;
        color: #6c757d;
    }

    .info-value {
        flex: 1;
        color: #333;
    }

    .bio-content {
        background-color: #f8f9fa;
        padding: 1.25rem;
        border-radius: 0.375rem;
        line-height: 1.7;
    }

    /* Tab Styles */
    .nav-tabs .nav-link {
        color: #6c757d;
        border: none;
        padding: 0.75rem 1rem;
    }

    .nav-tabs .nav-link.active {
        color: #4CAF50;
        background-color: transparent;
        border-bottom: 2px solid #4CAF50;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        border-bottom: 2px solid #e9ecef;
    }
</style>
@endpush
