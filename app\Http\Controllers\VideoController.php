<?php

namespace App\Http\Controllers;

use App\Models\Video;
use Illuminate\Http\Request;

class VideoController extends Controller
{
    /**
     * Display a listing of the videos.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $videos = Video::where('is_active', true)
            ->latest()  // Order by created_at DESC (newest first)
            ->paginate(12);
        return view('public.videos.index', compact('videos'));
    }

    /**
     * Display the specified video.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $video = Video::where('is_active', true)->findOrFail($id);

        // Get related videos (excluding the current one)
        $relatedVideos = Video::where('is_active', true)
            ->where('id', '!=', $video->id)
            ->latest()  // Order by created_at DESC (newest first)
            ->take(4)
            ->get();

        return view('public.videos.show', compact('video', 'relatedVideos'));
    }
}
