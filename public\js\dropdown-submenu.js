// Dropdown submenu functionality
document.addEventListener('DOMContentLoaded', function() {
    // Function to initialize dropdown submenu behavior
    function initDropdownSubmenu() {
        // First, remove all existing event listeners by cloning and replacing elements
        document.querySelectorAll('.dropdown-submenu > a').forEach(function(element) {
            const clone = element.cloneNode(true);
            element.parentNode.replaceChild(clone, element);
        });

        // Then add new event listeners
        document.querySelectorAll('.dropdown-submenu > a').forEach(function(element) {
            element.addEventListener('click', function(e) {
                // Only prevent default on mobile devices
                if (window.innerWidth < 992) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle the submenu
                    const parent = this.parentNode;
                    const submenu = this.nextElementSibling;

                    // Close all other open submenus at the same level
                    const siblings = parent.parentNode.querySelectorAll('.dropdown-submenu.show');
                    siblings.forEach(function(sibling) {
                        if (sibling !== parent) {
                            sibling.classList.remove('show');
                            const siblingSubmenu = sibling.querySelector('.dropdown-menu');
                            if (siblingSubmenu) {
                                siblingSubmenu.style.display = 'none';
                            }
                        }
                    });

                    // Toggle this submenu
                    if (submenu && submenu.classList.contains('dropdown-menu')) {
                        if (parent.classList.contains('show')) {
                            parent.classList.remove('show');
                            submenu.style.display = 'none';
                        } else {
                            parent.classList.add('show');
                            submenu.style.display = 'block';
                        }
                    }
                }
            });
        });

        // Handle hover behavior for desktop
        if (window.innerWidth >= 992) {
            document.querySelectorAll('.dropdown-submenu').forEach(function(element) {
                element.addEventListener('mouseenter', function() {
                    const submenu = this.querySelector('.dropdown-menu');
                    if (submenu) {
                        submenu.style.display = 'block';
                    }
                });

                element.addEventListener('mouseleave', function() {
                    const submenu = this.querySelector('.dropdown-menu');
                    if (submenu) {
                        submenu.style.display = '';
                    }
                });
            });
        }
    }

    // Initialize on page load
    initDropdownSubmenu();

    // Reinitialize when window is resized
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            initDropdownSubmenu();
        }, 250);
    });

    // Close all submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            document.querySelectorAll('.dropdown-submenu.show').forEach(function(element) {
                element.classList.remove('show');
                const submenu = element.querySelector('.dropdown-menu');
                if (submenu) {
                    submenu.style.display = 'none';
                }
            });
        }
    });
});
