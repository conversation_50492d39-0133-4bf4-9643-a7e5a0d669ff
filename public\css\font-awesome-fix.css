/* Font Awesome Fix CSS */

/* Ensure Font Awesome icons display correctly */
.fa,
.fas,
.far,
.fal,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -.0667em;
}

.fa-xs {
  font-size: .75em;
}

.fa-sm {
  font-size: .875em;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

/* Specific icons */
.fa-clock:before,
.far.fa-clock:before {
  content: "\f017";
}

.fa-map-marker-alt:before,
.fas.fa-map-marker-alt:before {
  content: "\f3c5";
}

.fa-user:before,
.fas.fa-user:before {
  content: "\f007";
}

.fa-arrow-left:before,
.fas.fa-arrow-left:before {
  content: "\f060";
}

/* Font weights */
.far {
  font-weight: 400;
}

.fa,
.fas {
  font-weight: 900;
}

/* Font family */
.fa,
.fas,
.far,
.fal,
.fab {
  font-family: "Font Awesome 5 Free", "FontAwesome", sans-serif;
}

.fab {
  font-family: "Font Awesome 5 Brands", "FontAwesome", sans-serif;
}

/* Agenda specific fixes */
.agenda-info i.far.fa-clock:before {
  content: "\f017";
  font-family: "Font Awesome 5 Free";
  font-weight: 400;
}

.agenda-info i.fas.fa-map-marker-alt:before {
  content: "\f3c5";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.agenda-info i.fas.fa-user:before {
  content: "\f007";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.agenda-info i.fas.fa-arrow-left:before {
  content: "\f060";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}
