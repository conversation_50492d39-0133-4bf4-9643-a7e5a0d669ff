<?php

namespace App\Http\Requests;

class NewsRequest extends ImageUploadRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the base validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    protected function baseRules(): array
    {
        $news = $this->route('news');
        $newsId = $news ? $news->id : null;

        return [
            'title' => 'required|string|max:255',
            'title_en' => 'required|string|max:255',
            'content' => 'required|string',
            'content_en' => 'required|string',
            'slug' => 'required|string|max:255|unique:news,slug,' . $newsId,
            'slug_en' => 'required|string|max:255|unique:news,slug_en,' . $newsId,
            'category_id' => 'required|exists:news_categories,id',
            'published_at' => 'required|date',
        ];
    }

    /**
     * Get the image fields for this request.
     *
     * @return array
     */
    protected function getImageFields(): array
    {
        return [
            'image' => false,
            'thumbnail' => false,
        ];
    }
}
