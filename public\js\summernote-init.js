// Summernote Lite initialization for content fields
$(document).ready(function() {
    console.log('Summernote Lite init script loaded');

    // Create a custom file input for image uploads
    $('body').append('<input type="file" id="summernote-file-input" style="display: none;" accept="image/*">');

    // Function to initialize Summernote Lite with custom toolbar
    function initSummernote(selector) {
        $(selector).summernote({
            placeholder: 'Write your content here...',
            tabsize: 2,
            height: 300,
            styleTags: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote'],
            fontNames: ['Segoe UI', 'Arial', 'sans-serif'],
            fontNamesIgnoreCheck: ['Segoe UI', 'Arial', 'sans-serif'],
            defaultFontName: 'Segoe UI',
            toolbar: [
                ['font', ['bold', 'underline', 'italic', 'clear']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']], // Added paragraph option which contains alignment
                ['table', ['table']],
                ['insert', ['link', 'picture']],
                ['view', ['fullscreen', 'codeview', 'help']],
            ],
            callbacks: {
                onImageUpload: function(files) {
                    // We'll override this with our custom upload handler
                    console.log('Native image upload triggered, redirecting to custom handler');
                    for (let i = 0; i < files.length; i++) {
                        customImageUpload(files[i], this);
                    }
                },
                onInit: function() {
                    // Set default text alignment to justify
                    $(this).summernote('justifyFull');

                    // Set default font
                    $(this).summernote('fontName', 'Segoe UI');

                    // Apply font and alignment to all paragraphs
                    const $editable = $(this).next('.note-editor').find('.note-editable');
                    $editable.find('p').css({
                        'text-align': 'justify',
                        'font-family': "'Segoe UI', Arial, sans-serif"
                    });
                },
                onChange: function(contents, $editable) {
                    // Apply consistent font to all paragraphs but preserve user-selected alignment
                    $editable.find('p').each(function() {
                        // Only set font-family, preserve existing text-align if set by user
                        $(this).css('font-family', "'Segoe UI', Arial, sans-serif");

                        // If no text-align is set, default to justify
                        if (!$(this).attr('style') || $(this).attr('style').indexOf('text-align') === -1) {
                            $(this).css('text-align', 'justify');
                        }
                    });
                }
            }
        });

        // Override the default image button click behavior
        $(selector).each(function() {
            const editor = this;
            const $editor = $(editor);

            // Find the image button in this editor's toolbar
            const $imageBtn = $editor.next('.note-editor').find('.note-btn[data-original-title="Picture"]');

            // Override the click event
            $imageBtn.off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Store the current editor in a data attribute
                $('#summernote-file-input').data('editor', editor);

                // Trigger the file input
                $('#summernote-file-input').click();
            });
        });
    }

    // Set up the file input change event
    $('#summernote-file-input').on('change', function(e) {
        const files = e.target.files;
        if (files && files.length > 0) {
            const editor = $(this).data('editor');
            customImageUpload(files[0], editor);

            // Reset the file input so the same file can be selected again
            $(this).val('');
        }
    });

    // Function to handle image upload for Summernote
    function customImageUpload(file, editor) {
        console.log('Starting custom image upload process');

        // Check file size before uploading (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('Image size exceeds 10MB limit. Please choose a smaller image.');
            return;
        }

        // Check file type
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
        if (!validTypes.includes(file.type)) {
            alert('Invalid file type. Please use JPG, PNG, or GIF images only.');
            return;
        }

        // Create form data
        var formData = new FormData();
        formData.append("image", file);
        formData.append("_token", $('meta[name="csrf-token"]').attr('content'));

        // Create a loading indicator
        var $loadingIndicator = $('<div class="summernote-loading-indicator">Uploading image...</div>');
        $('body').append($loadingIndicator);

        // Send the image to the server
        $.ajax({
            url: "/admin/upload-summernote-image",
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            cache: false,
            success: function(response) {
                console.log('Server response:', response);

                // Remove the loading indicator
                $loadingIndicator.remove();

                if (response.success) {
                    console.log('Image uploaded successfully, URL:', response.url);

                    // Insert the image using direct HTML
                    var imageHtml = '<img src="' + response.url + '" alt="' + (response.filename || 'Uploaded image') + '" style="max-width: 100%;">';

                    // Use pasteHTML to insert the image
                    $(editor).summernote('code', $(editor).summernote('code') + imageHtml);

                    console.log('Image inserted into editor');
                } else {
                    console.error('Upload failed:', response.message);
                    alert('Upload failed: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error during upload:', error);
                console.error('Server response:', xhr.responseText);

                // Remove the loading indicator
                $loadingIndicator.remove();

                // Parse error message
                let errorMessage = 'Failed to upload image';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    } else if (response.errors && response.errors.image) {
                        errorMessage = response.errors.image[0];
                    }
                } catch (e) {
                    errorMessage = 'Server error: ' + error;
                }

                alert(errorMessage);
            }
        });
    }

    // Initialize all content fields in news, agenda, berita, and insight sections
    initSummernote('.summernote');

    // Also initialize specific fields by ID for backward compatibility
    initSummernote('#content');
    initSummernote('#content_en');

    // Initialize description fields except in registration forms and swiper admin pages
    $('textarea#description, textarea#description_en').each(function() {
        // Skip description fields in registration schedule, requirement forms, and swiper admin pages
        if (!$(this).closest('#addScheduleModal, #editScheduleModal, #addRequirementModal, #editRequirementModal').length &&
            !$(this).attr('data-no-summernote') &&
            this.id !== 'swiper-description' &&
            this.id !== 'swiper-description-en') {
            initSummernote('#' + this.id);
        }
    });

    // Special initialization for bio fields with word limit
    $('#bio, #bio_en').each(function() {
        $(this).summernote({
            placeholder: 'Write your bio here (max 100 words)...',
            tabsize: 2,
            height: 200,
            fontNames: ['Segoe UI', 'Arial', 'sans-serif'],
            fontNamesIgnoreCheck: ['Segoe UI', 'Arial', 'sans-serif'],
            defaultFontName: 'Segoe UI',
            toolbar: [
                ['font', ['bold', 'underline', 'italic', 'clear']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']], // Added paragraph option which contains alignment
                ['table', ['table']],
                ['insert', ['link', 'picture']],
                ['view', ['fullscreen', 'codeview', 'help']],
            ],
            callbacks: {
                onKeydown: function(e) {
                    var t = e.currentTarget.innerText;
                    var wordCount = t.trim().split(/\s+/).length;

                    // If we're over the word limit and not pressing backspace/delete
                    if (wordCount >= 100 && e.keyCode !== 8 && e.keyCode !== 46) {
                        // Check if it's a word-creating key (space or enter)
                        if (e.keyCode === 32 || e.keyCode === 13) {
                            e.preventDefault();
                            return false;
                        }
                    }
                },
                onKeyup: function(e) {
                    updateWordCount(this);
                },
                onPaste: function(e) {
                    setTimeout(function() {
                        updateWordCount(this);
                    }.bind(this), 100);
                },
                onImageUpload: function(files) {
                    console.log('Bio image upload triggered');
                    for (let i = 0; i < files.length; i++) {
                        customImageUpload(files[i], this);
                    }
                },
                onMediaDelete: function(target) {
                    console.log('Image deleted:', target[0].src);
                },
                onInit: function() {
                    // Set default text alignment to justify
                    $(this).summernote('justifyFull');

                    // Set default font
                    $(this).summernote('fontName', 'Segoe UI');

                    // Apply font and alignment to all paragraphs
                    const $editable = $(this).next('.note-editor').find('.note-editable');
                    $editable.find('p').css({
                        'text-align': 'justify',
                        'font-family': "'Segoe UI', Arial, sans-serif"
                    });
                },
                onChange: function(contents, $editable) {
                    // Apply consistent font to all paragraphs but preserve user-selected alignment
                    $editable.find('p').each(function() {
                        // Only set font-family, preserve existing text-align if set by user
                        $(this).css('font-family', "'Segoe UI', Arial, sans-serif");

                        // If no text-align is set, default to justify
                        if (!$(this).attr('style') || $(this).attr('style').indexOf('text-align') === -1) {
                            $(this).css('text-align', 'justify');
                        }
                    });
                }
            }
        });
    });

    // Override the default image button click behavior for bio fields
    $('#bio, #bio_en').each(function() {
        const editor = this;
        const $editor = $(editor);

        // Find the image button in this editor's toolbar
        const $imageBtn = $editor.next('.note-editor').find('.note-btn[data-original-title="Picture"]');

        // Override the click event
        $imageBtn.off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Store the current editor in a data attribute
            $('#summernote-file-input').data('editor', editor);

            // Trigger the file input
            $('#summernote-file-input').click();
        });
    });

    // Function to update word count display
    function updateWordCount(editor) {
        var text = $(editor).summernote('code');
        var plainText = $('<div>').html(text).text();
        var words = plainText.trim().split(/\s+/);
        var wordCount = plainText.trim() === '' ? 0 : words.length;

        // Find the counter element
        var $counter = $(editor).siblings('.word-counter');
        if ($counter.length === 0) {
            // Create counter if it doesn't exist
            $counter = $('<div class="word-counter mt-1 text-muted small"></div>');
            $(editor).after($counter);
        }

        // Update counter text and style
        $counter.text('Word count: ' + wordCount + ' / 100');

        // Add warning style if approaching limit
        if (wordCount > 90) {
            $counter.removeClass('text-muted').addClass('text-warning');
        } else if (wordCount >= 100) {
            $counter.removeClass('text-muted text-warning').addClass('text-danger');
        } else {
            $counter.removeClass('text-warning text-danger').addClass('text-muted');
        }
    }

    // Education unit fields
    initSummernote('#facilities');
    initSummernote('#facilities_en');
    initSummernote('#curriculum');
    initSummernote('#curriculum_en');
    initSummernote('#principal_education');
    initSummernote('#principal_education_en');
    initSummernote('#principal_experience');
    initSummernote('#principal_experience_en');
    initSummernote('#principal_achievements');
    initSummernote('#principal_achievements_en');

    console.log('Summernote Lite editors initialized');

    // Function to ensure all paragraphs in Summernote editors are justified and have consistent font
    function justifyAllSummernoteContent() {
        // For all Summernote editors (excluding registration schedule and requirement description fields)
        $('.summernote, #content, #content_en, #bio, #bio_en, #facilities, #facilities_en, #curriculum, #curriculum_en, #principal_education, #principal_education_en, #principal_experience, #principal_experience_en, #principal_achievements, #principal_achievements_en').each(function() {
            // Skip description fields in registration schedule, requirement forms, and swiper admin pages
            if ((this.id === 'description' || this.id === 'description_en') &&
                ($(this).closest('#addScheduleModal, #editScheduleModal, #addRequirementModal, #editRequirementModal').length ||
                 $(this).attr('data-no-summernote') ||
                 this.id === 'swiper-description' ||
                 this.id === 'swiper-description-en')) {
                return;
            }
            const $editor = $(this);
            const content = $editor.summernote('code');

            // Create a temporary div to manipulate the HTML
            const $temp = $('<div>').html(content);

            // Apply consistent font to all paragraphs but preserve user-selected alignment
            $temp.find('p').each(function() {
                // Only set font-family, preserve existing text-align if set by user
                $(this).css('font-family', "'Segoe UI', Arial, sans-serif");

                // If no text-align is set, default to justify
                if (!$(this).attr('style') || $(this).attr('style').indexOf('text-align') === -1) {
                    $(this).css('text-align', 'justify');
                }
            });

            // Apply consistent font to all elements
            $temp.find('*').css('font-family', "'Segoe UI', Arial, sans-serif");

            // Update the editor content
            $editor.summernote('code', $temp.html());
        });
    }

    // Apply justified alignment to all existing content after a short delay
    // to ensure all editors are fully initialized
    setTimeout(justifyAllSummernoteContent, 500);
});
