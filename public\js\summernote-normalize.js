/**
 * Summernote Content Normalizer
 * This script normalizes Summernote content styling across the site
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Summernote Content Normalizer loaded');

    // Find all Summernote content containers
    const contentContainers = document.querySelectorAll(
        '.summernote-content, .news-content, .announcement-content, .agenda-content, ' +
        '.edu-unit-detail-desc, .edu-unit-detail-facilities, .principal-section .section-content, ' +
        '.content-wrapper'
    );

    // Process each container
    contentContainers.forEach(function(container) {
        // Normalize font family
        const elements = container.querySelectorAll('*');
        elements.forEach(function(el) {
            // Set font family to Poppins, Arial, or sans-serif
            el.style.fontFamily = "'Poppins', Arial, sans-serif";

            // Remove font tags
            if (el.tagName.toLowerCase() === 'font') {
                // Move the content outside the font tag
                while (el.firstChild) {
                    el.parentNode.insertBefore(el.firstChild, el);
                }
                // Remove the empty font tag
                el.parentNode.removeChild(el);
            }
        });

        // Normalize font sizes and ensure paragraphs are justified
        const textElements = container.querySelectorAll('p, span, div, li, td, th');
        textElements.forEach(function(el) {
            // Remove inline font-size styles
            if (el.style.fontSize) {
                el.style.fontSize = '';
            }

            // Ensure paragraphs are justified
            if (el.tagName.toLowerCase() === 'p') {
                el.style.textAlign = 'justify';
            }
        });

        // Preserve heading sizes
        const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(function(heading) {
            // Remove inline font-size styles from headings
            if (heading.style.fontSize) {
                heading.style.fontSize = '';
            }
        });

        // Add the normalized class to indicate this container has been processed
        container.classList.add('summernote-normalized');
    });

    console.log('Summernote content normalized');
});
