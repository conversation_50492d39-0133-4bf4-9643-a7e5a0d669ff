<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingHelper
{
    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        // Try to get from cache first
        return Cache::remember('setting_' . $key, 60 * 24, function () use ($key, $default) {
            $setting = Setting::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Get all settings as an array
     *
     * @return array
     */
    public static function getAll()
    {
        return Cache::remember('settings_all', 60 * 24, function () {
            return Setting::pluck('value', 'key')->toArray();
        });
    }

    /**
     * Get the website name
     *
     * @return string
     */
    public static function getWebsiteName()
    {
        return self::get('site_name', config('app.name', 'Pondok Pesantren Nurul Hayah 4'));
    }

    /**
     * Get the institution name
     *
     * @return string
     */
    public static function getInstitutionName()
    {
        // Fallback to site_name if institution_name is not set
        $institutionName = self::get('institution_name');
        if (!$institutionName) {
            return self::getWebsiteName();
        }
        return $institutionName;
    }

    /**
     * Get the institution name in English
     *
     * @return string
     */
    public static function getInstitutionNameEn()
    {
        // Fallback to site_name_en if institution_name_en is not set
        $institutionNameEn = self::get('institution_name_en');
        if (!$institutionNameEn) {
            return self::get('site_name_en', 'Nurul Hayah 4 Islamic Boarding School');
        }
        return $institutionNameEn;
    }

    /**
     * Get the website logo
     *
     * @return string
     */
    public static function getLogo()
    {
        $logo = self::get('logo', 'images/logo.png');

        // If the logo is stored in the storage directory, prepend 'storage/'
        if ($logo && !str_starts_with($logo, 'http') && !str_starts_with($logo, 'https') && !str_starts_with($logo, 'images/') && !str_starts_with($logo, 'storage/')) {
            return 'storage/' . $logo;
        }

        return $logo;
    }

    /**
     * Get the website favicon
     *
     * @return string
     */
    public static function getFavicon()
    {
        $favicon = self::get('favicon', 'images/favicon.ico');

        // If the favicon is a URL, return it as is
        if ($favicon && (str_starts_with($favicon, 'http') || str_starts_with($favicon, 'https'))) {
            return $favicon;
        }

        // If the favicon already has storage/ prefix, return it as is
        if ($favicon && str_starts_with($favicon, 'storage/')) {
            return $favicon;
        }

        // If the favicon is in the images directory, return it as is
        if ($favicon && str_starts_with($favicon, 'images/')) {
            return $favicon;
        }

        // For all other cases, assume it's in storage and add the prefix
        return 'storage/' . $favicon;
    }

    /**
     * Get the website meta description
     *
     * @return string
     */
    public static function getMetaDescription()
    {
        return self::get('meta_description', 'Pondok Pesantren Modern dengan Pendidikan Berkualitas');
    }

    /**
     * Get the website meta keywords
     *
     * @return string
     */
    public static function getMetaKeywords()
    {
        return self::get('meta_keywords', 'pondok pesantren, pendidikan islam, sekolah islam, pesantren modern');
    }
}
