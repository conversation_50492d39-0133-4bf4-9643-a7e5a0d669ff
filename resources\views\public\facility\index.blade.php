@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Fasilitas Pondok' : 'Boarding School Facilities' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Fasilitas' : 'Facilities' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Facilities Introduction -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right">
                    <img src="{{ asset('images/facilities.jpg') }}" alt="Facilities" class="img-fluid rounded shadow-sm">
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <h2 class="text-success mb-4">{{ app()->getLocale() == 'id' ? 'Fasilitas Modern untuk Pendidikan Berkualitas' : 'Modern Facilities for Quality Education' }}</h2>
                    <p>{{ app()->getLocale() == 'id' ? 'Pondok Pesantren Nurul Hayah 4 dilengkapi dengan berbagai fasilitas modern untuk mendukung kegiatan belajar mengajar dan pengembangan potensi santri. Fasilitas-fasilitas ini dirancang untuk menciptakan lingkungan belajar yang nyaman, aman, dan kondusif.' : 'Nurul Hayah 4 Islamic Boarding School is equipped with various modern facilities to support teaching and learning activities and the development of students\' potential. These facilities are designed to create a comfortable, safe, and conducive learning environment.' }}</p>
                    <p>{{ app()->getLocale() == 'id' ? 'Kami berkomitmen untuk terus meningkatkan kualitas fasilitas kami demi memberikan pengalaman pendidikan terbaik bagi para santri.' : 'We are committed to continuously improving the quality of our facilities to provide the best educational experience for our students.' }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Facilities List -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>{{ app()->getLocale() == 'id' ? 'Fasilitas Kami' : 'Our Facilities' }}</h2>
                <p>{{ app()->getLocale() == 'id' ? 'Berbagai fasilitas yang tersedia di Pondok Pesantren Nurul Hayah 4' : 'Various facilities available at Nurul Hayah 4 Islamic Boarding School' }}</p>
            </div>

            <div class="row">
                @forelse($facilities as $facility)
                    <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->iteration % 3 * 100 }}">
                        <div class="card h-100 border-0 shadow-sm">
                            @if($facility->image)
                                <img src="{{ asset('storage/' . $facility->image) }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $facility->name : $facility->name_en }}">
                            @else
                                <img src="{{ asset('images/facility-placeholder.jpg') }}" class="card-img-top" alt="{{ app()->getLocale() == 'id' ? $facility->name : $facility->name_en }}">
                            @endif
                            <div class="card-body">
                                <div class="facility-icon mb-3 text-success">
                                    @if($facility->icon)
                                        <i class="{{ $facility->icon }} fa-2x"></i>
                                    @else
                                        <i class="fas fa-building fa-2x"></i>
                                    @endif
                                </div>
                                <h4 class="card-title">{{ app()->getLocale() == 'id' ? $facility->name : $facility->name_en }}</h4>
                                <div class="card-text">{!! app()->getLocale() == 'id' ? $facility->description : $facility->description_en !!}</div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            {{ app()->getLocale() == 'id' ? 'Belum ada fasilitas yang tersedia.' : 'No facilities available yet.' }}
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-success text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8" data-aos="fade-right">
                    <h2 class="mb-3">{{ app()->getLocale() == 'id' ? 'Tertarik untuk Bergabung?' : 'Interested in Joining?' }}</h2>
                    <p class="mb-4">{{ app()->getLocale() == 'id' ? 'Daftarkan diri Anda sekarang dan nikmati fasilitas modern kami untuk pendidikan berkualitas.' : 'Register yourself now and enjoy our modern facilities for quality education.' }}</p>
                </div>
                <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                    <a href="{{ route('registration') }}" class="btn btn-light btn-lg">{{ app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now' }}</a>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    /* Facility card text formatting */
    .card-text {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .card-text p {
        margin-bottom: 0.75rem;
    }

    .card-text ul, .card-text ol {
        padding-left: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .card-text table {
        width: 100%;
        margin-bottom: 0.75rem;
        border-collapse: collapse;
    }

    .card-text table th, .card-text table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .card-text table th {
        background-color: #f8f9fa;
    }

    .card-text img {
        max-width: 100%;
        height: auto;
        margin: 0.75rem 0;
        border-radius: 0.25rem;
    }

    .card-text a {
        color: #28a745;
        text-decoration: none;
    }

    .card-text a:hover {
        text-decoration: underline;
    }
</style>
@endpush
