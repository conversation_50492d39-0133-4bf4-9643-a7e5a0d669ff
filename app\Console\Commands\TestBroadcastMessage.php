<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramBotService;
use App\Models\Setting;

class TestBroadcastMessage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test-broadcast';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending a broadcast message to Telegram subscribers and group topic';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Telegram broadcast message...');
        
        $telegramService = new TelegramBotService();
        $chatId = Setting::getValue('telegram_chat_id');
        $topicId = (int)Setting::getValue('telegram_group_topic_id');
        
        // Check if the chat ID is a group chat (starts with -)
        $isGroupChat = strpos($chatId, '-') === 0;
        
        $this->info('Chat ID: ' . $chatId);
        $this->info('Is Group Chat: ' . ($isGroupChat ? 'Yes' : 'No'));
        $this->info('Topic ID: ' . $topicId);
        
        if (empty($chatId)) {
            $this->error('Chat ID is missing. Please configure it in the admin panel.');
            return 1;
        }
        
        // Send test message to Telegram
        $message = 'This is a test broadcast message from the telegram:test-broadcast command at ' . now()->format('Y-m-d H:i:s');
        
        try {
            // First send to admin chat with topic if applicable
            if ($isGroupChat && $topicId > 0) {
                $this->info('Sending message to group chat with topic ID: ' . $topicId);
                $result = $telegramService->sendMessage($chatId, $message, [], $topicId);
                
                if ($result && isset($result['ok']) && $result['ok']) {
                    $this->info('Message sent to group topic successfully!');
                } else {
                    $this->error('Failed to send message to group topic: ' . json_encode($result));
                }
            }
            
            // Then broadcast to subscribers
            $this->info('Broadcasting message to subscribers...');
            $successCount = $telegramService->broadcastMessage($message);
            $this->info('Message broadcast to ' . $successCount . ' subscribers successfully!');
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
