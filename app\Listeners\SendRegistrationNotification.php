<?php

namespace App\Listeners;

use App\Models\Registration;
use App\Models\Setting;
use Illuminate\Support\Facades\Log;

class SendRegistrationNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        // Constructor
    }

    /**
     * Handle the event.
     */
    public function handle(object $event): void
    {
        Log::info('SendRegistrationNotification listener triggered');

        // Check if the event has a registration
        if (!isset($event->registration) || !($event->registration instanceof Registration)) {
            Log::error('Event does not have a valid registration object');
            return;
        }

        $registration = $event->registration;
        Log::info('Processing registration notification', ['registration_id' => $registration->id, 'name' => $registration->full_name]);

        // Here you could implement other notification methods like email
        // For now, we'll just log that we received the registration
        Log::info('Registration received', [
            'registration_id' => $registration->id,
            'name' => $registration->full_name,
            'email' => $registration->email,
            'phone' => $registration->phone
        ]);
    }
}
