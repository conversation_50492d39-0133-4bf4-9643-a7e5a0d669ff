/* Custom CSS for Admin Panel */

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

#wrapper {
    display: flex;
}

#sidebar-wrapper {
    min-height: 100vh;
    width: 250px;
    transition: all 0.25s ease-out;
}

#sidebar-wrapper .sidebar-heading {
    padding: 0.875rem 1.25rem;
    font-size: 1.2rem;
}

#sidebar-wrapper .list-group {
    width: 250px;
}

/* Mini sidebar styles */
#wrapper.mini-sidebar #sidebar-wrapper {
    width: 70px;
    overflow: hidden;
}

#wrapper.mini-sidebar #sidebar-wrapper .sidebar-heading {
    padding: 0.875rem 0.5rem;
    text-align: center;
}

#wrapper.mini-sidebar #sidebar-wrapper .sidebar-heading div {
    display: none;
}

#wrapper.mini-sidebar #sidebar-wrapper .list-group-item span,
#wrapper.mini-sidebar #sidebar-wrapper .submenu-toggle {
    display: none;
}

#wrapper.mini-sidebar #sidebar-wrapper .list-group-item {
    text-align: center;
    padding: 15px 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

#wrapper.mini-sidebar #sidebar-wrapper .list-group-item i {
    font-size: 1.2rem;
    margin-right: 0;
    display: block !important;
    visibility: visible !important;
}

#wrapper.mini-sidebar #sidebar-wrapper .sidebar-submenu {
    display: none;
}

#sidebar-wrapper .list-group-item {
    border: none;
    padding: 15px 20px;
    font-size: 14px;
    background-color: transparent;
    color: #fff;
    border-radius: 0;
    transition: all 0.3s;
    display: flex;
    align-items: center;
}

#sidebar-wrapper .list-group-item i {
    margin-right: 10px;
    min-width: 20px;
    text-align: center;
}

#sidebar-wrapper .list-group-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

#sidebar-wrapper .list-group-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 4px solid #fff;
}

/* Submenu styles */
.sidebar-submenu {
    list-style: none;
    padding-left: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
}

.sidebar-submenu.show {
    max-height: 1000px;
}

.sidebar-submenu .submenu-item {
    padding: 10px 10px 10px 50px;
    font-size: 13px;
    color: #fff;
    opacity: 0.8;
    display: block;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-submenu .submenu-item:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.05);
}

.sidebar-submenu .submenu-item.active {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 2px solid #fff;
}

.has-submenu {
    position: relative;
    cursor: pointer;
}

.has-submenu .submenu-toggle {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s;
    pointer-events: none; /* Make the icon not interfere with clicks */
}

.has-submenu.open .submenu-toggle {
    transform: translateY(-50%) rotate(180deg);
}

#page-content-wrapper {
    min-width: 100vw;
    transition: all 0.25s ease-out;
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: -250px;
}

/* Desktop: page content has margin for sidebar by default */
@media (min-width: 768px) {
    #page-content-wrapper {
        margin-left: 0;
        width: calc(100% - 250px);
    }
}

.navbar {
    padding: 15px 20px;
}

/* Sidebar toggle button */
#sidebarToggle {
    transition: all 0.25s ease-out;
}

/* Desktop: position the toggle button differently when sidebar is mini */
@media (min-width: 768px) {
    #wrapper.mini-sidebar #sidebarToggle {
        margin-left: 70px;
    }

    #wrapper:not(.mini-sidebar) #sidebarToggle {
        margin-left: 0;
    }
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 15px 20px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

.btn {
    border-radius: 4px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-success:hover {
    background-color: #146c43;
    border-color: #146c43;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    vertical-align: middle;
}

.table th {
    font-weight: 600;
    padding: 12px 15px;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.form-control {
    border-radius: 4px;
    padding: 10px 15px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

.dashboard-card {
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-icon {
    font-size: 36px;
    margin-bottom: 15px;
}

.dashboard-card .card-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 5px;
}

.dashboard-card .card-value {
    font-size: 24px;
    font-weight: 700;
}

.bg-primary-light {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.bg-success-light {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.bg-warning-light {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.pagination {
    margin-top: 20px;
    justify-content: center;
}

.pagination .page-item .page-link {
    color: #198754;
    padding: 8px 16px;
}

.pagination .page-item.active .page-link {
    background-color: #198754;
    border-color: #198754;
    color: #fff;
}

.pagination .page-item .page-link:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.alert {
    border-radius: 5px;
    padding: 15px 20px;
}

.alert-dismissible .btn-close {
    padding: 15px 20px;
}

/* Responsive */
@media (min-width: 768px) {
    #page-content-wrapper {
        min-width: 0;
        width: calc(100% - 250px);
        transition: all 0.25s ease-out;
    }

    /* Desktop: toggled class is not used for showing/hiding sidebar */
    #wrapper.toggled #sidebar-wrapper {
        margin-left: 0;
    }

    /* Desktop: mini-sidebar adjustments */
    #wrapper.mini-sidebar #page-content-wrapper {
        width: calc(100% - 70px);
    }

    #wrapper:not(.mini-sidebar) #page-content-wrapper {
        width: calc(100% - 250px);
    }
}

/* Summernote image placeholder */
.summernote-image-placeholder {
    background-color: #f8f9fa;
    border: 1px dashed #ced4da;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    color: #6c757d;
    margin: 10px 0;
    font-style: italic;
}

/* Summernote loading indicator */
.summernote-loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px 30px;
    border-radius: 5px;
    z-index: 9999;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

@media (max-width: 767.98px) {
    /* Mobile: sidebar is hidden by default */
    #sidebar-wrapper {
        margin-left: -250px;
        position: fixed;
        z-index: 1000;
        height: 100%;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        transition: margin-left 0.25s ease-out;
    }

    /* Mobile: toggled class shows the sidebar */
    #wrapper.toggled #sidebar-wrapper {
        margin-left: 0;
    }

    /* Mobile: page content takes full width */
    #page-content-wrapper {
        min-width: 100vw;
        width: 100%;
        margin-left: 0;
    }

    /* Mobile: position the toggle button */
    #sidebarToggle {
        margin-left: 0;
        z-index: 1050;
    }

    /* Mobile: mini-sidebar class has no effect */
    #wrapper.mini-sidebar #sidebar-wrapper {
        width: 250px;
    }

    #wrapper.mini-sidebar #sidebar-wrapper .sidebar-heading div {
        display: block;
    }

    #wrapper.mini-sidebar #sidebar-wrapper .list-group-item span,
    #wrapper.mini-sidebar #sidebar-wrapper .submenu-toggle {
        display: inline-block;
    }

    #wrapper.mini-sidebar #sidebar-wrapper .list-group-item {
        text-align: left;
        padding: 15px 20px;
        display: block;
    }

    #wrapper.mini-sidebar #sidebar-wrapper .list-group-item i {
        margin-right: 0.5rem;
        display: inline-block;
    }

    #wrapper.mini-sidebar #sidebar-wrapper .sidebar-submenu {
        display: block;
    }

    /* Add overlay when sidebar is open */
    #wrapper.toggled::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }
}
