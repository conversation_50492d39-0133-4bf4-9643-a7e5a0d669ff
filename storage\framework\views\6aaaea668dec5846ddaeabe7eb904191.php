<?php $__env->startSection('title', app()->getLocale() == 'id' ? '<PERSON><PERSON><PERSON><PERSON>' : 'Our Leaders'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Pemimpin <PERSON>' : 'Our Leaders'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Pemimpin Kami' : 'Our Leaders'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Leaders Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title text-center fade-in">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Pemimpin Visioner Kami' : 'Our Visionary Leaders'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Kenali para pemimpin yang membawa ' . \App\Helpers\SettingHelper::getInstitutionName() . ' menuju keunggulan.' : 'Meet the leaders who drive ' . \App\Helpers\SettingHelper::getInstitutionNameEn() . ' towards excellence.'); ?></p>
            </div>

            <div class="row justify-content-center">
                <?php $__empty_1 = true; $__currentLoopData = $leaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $leader): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="<?php echo e($loop->iteration <= 2 ? 'col-lg-6' : 'col-lg-4'); ?> col-md-6 mb-4">
                        <div class="leader-card fade-in" style="transition-delay: <?php echo e($loop->iteration * 0.1); ?>s">
                            <div class="leader-image">
                                <?php
                                    $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                    // If English name is empty, use Indonesian name
                                    if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                        $name = $leader->name;
                                    }
                                ?>
                                <?php if($leader->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $leader->image)); ?>" alt="<?php echo e($name); ?>" class="img-fluid">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('images/leader-placeholder.jpg')); ?>" alt="<?php echo e($name); ?>" class="img-fluid">
                                <?php endif; ?>
                                <div class="leader-social">
                                    <?php if($leader->social_facebook): ?>
                                        <a href="<?php echo e($leader->social_facebook); ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                    <?php endif; ?>
                                    <?php if($leader->social_twitter): ?>
                                        <a href="<?php echo e($leader->social_twitter); ?>" target="_blank"><i class="fab fa-x-twitter"></i></a>
                                    <?php endif; ?>
                                    <?php if($leader->social_instagram): ?>
                                        <a href="<?php echo e($leader->social_instagram); ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                    <?php endif; ?>
                                    <?php if($leader->social_linkedin): ?>
                                        <a href="<?php echo e($leader->social_linkedin); ?>" target="_blank"><i class="fab fa-telegram"></i></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="leader-info">
                                <?php
                                    $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                    $position = app()->getLocale() == 'id' ? $leader->position : $leader->position_en;

                                    // If English name is empty, use Indonesian name
                                    if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                        $name = $leader->name;
                                    }

                                    // If English position is empty, use Indonesian position
                                    if (app()->getLocale() == 'en' && empty($position) && !empty($leader->position)) {
                                        $position = $leader->position;
                                    }
                                ?>
                                <h3><?php echo e($name); ?></h3>
                                <p class="leader-position"><?php echo e($position); ?></p>
                                <div class="leader-bio">
                                    <?php
                                        $bio = app()->getLocale() == 'id' ? $leader->bio : $leader->bio_en;
                                        // If English bio is empty, use Indonesian bio
                                        if (app()->getLocale() == 'en' && empty($bio) && !empty($leader->bio)) {
                                            $bio = $leader->bio;
                                        }
                                    ?>
                                    <?php echo \Illuminate\Support\Str::limit(strip_tags($bio), 150); ?>

                                </div>
                                <div class="leader-contact">
                                    <?php if($leader->email): ?>
                                        <div><i class="fas fa-envelope"></i> <?php echo e($leader->email); ?></div>
                                    <?php endif; ?>
                                    <?php if($leader->phone): ?>
                                        <div><i class="fas fa-phone"></i> <?php echo e($leader->phone); ?></div>
                                    <?php endif; ?>
                                </div>
                                <button class="btn btn-sm btn-outline-success mt-3 leader-details-btn" data-bs-toggle="modal" data-bs-target="#leaderModal<?php echo e($leader->id); ?>">
                                    <?php echo e(app()->getLocale() == 'id' ? 'Lihat Detail' : 'View Details'); ?>

                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Leader Modal -->
                    <div class="modal fade" id="leaderModal<?php echo e($leader->id); ?>" tabindex="-1" aria-labelledby="leaderModalLabel<?php echo e($leader->id); ?>" aria-hidden="true">
                        <div class="modal-dialog modal-lg modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <?php
                                        $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                        // If English name is empty, use Indonesian name
                                        if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                            $name = $leader->name;
                                        }
                                    ?>
                                    <h5 class="modal-title" id="leaderModalLabel<?php echo e($leader->id); ?>"><?php echo e($name); ?></h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-4 mb-md-0">
                                            <div class="leader-modal-image">
                                                <?php
                                                    $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                                    // If English name is empty, use Indonesian name
                                                    if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                                        $name = $leader->name;
                                                    }
                                                ?>
                                                <?php if($leader->image): ?>
                                                    <img src="<?php echo e(asset('storage/' . $leader->image)); ?>" alt="<?php echo e($name); ?>" class="img-fluid rounded">
                                                <?php else: ?>
                                                    <img src="<?php echo e(asset('images/leader-placeholder.jpg')); ?>" alt="<?php echo e($name); ?>" class="img-fluid rounded">
                                                <?php endif; ?>
                                            </div>
                                            <div class="leader-modal-contact mt-3">
                                                <?php if($leader->email): ?>
                                                    <div class="mb-2"><i class="fas fa-envelope me-2"></i> <?php echo e($leader->email); ?></div>
                                                <?php endif; ?>
                                                <?php if($leader->phone): ?>
                                                    <div class="mb-2"><i class="fas fa-phone me-2"></i> <?php echo e($leader->phone); ?></div>
                                                <?php endif; ?>
                                                <div class="leader-modal-social mt-3">
                                                    <?php if($leader->social_facebook): ?>
                                                        <a href="<?php echo e($leader->social_facebook); ?>" target="_blank" class="me-2"><i class="fab fa-facebook-f"></i></a>
                                                    <?php endif; ?>
                                                    <?php if($leader->social_twitter): ?>
                                                        <a href="<?php echo e($leader->social_twitter); ?>" target="_blank" class="me-2"><i class="fab fa-x-twitter"></i></a>
                                                    <?php endif; ?>
                                                    <?php if($leader->social_instagram): ?>
                                                        <a href="<?php echo e($leader->social_instagram); ?>" target="_blank" class="me-2"><i class="fab fa-instagram"></i></a>
                                                    <?php endif; ?>
                                                    <?php if($leader->social_linkedin): ?>
                                                        <a href="<?php echo e($leader->social_linkedin); ?>" target="_blank"><i class="fab fa-telegram"></i></a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <?php
                                                $name = app()->getLocale() == 'id' ? $leader->name : $leader->name_en;
                                                $position = app()->getLocale() == 'id' ? $leader->position : $leader->position_en;

                                                // If English name is empty, use Indonesian name
                                                if (app()->getLocale() == 'en' && empty($name) && !empty($leader->name)) {
                                                    $name = $leader->name;
                                                }

                                                // If English position is empty, use Indonesian position
                                                if (app()->getLocale() == 'en' && empty($position) && !empty($leader->position)) {
                                                    $position = $leader->position;
                                                }
                                            ?>
                                            <h4 class="mb-1"><?php echo e($name); ?></h4>
                                            <p class="text-muted mb-3"><?php echo e($position); ?></p>

                                            <div class="leader-modal-bio mb-4">
                                                <h5><?php echo e(app()->getLocale() == 'id' ? 'Biografi' : 'Biography'); ?></h5>
                                                <?php
                                                    $bio = app()->getLocale() == 'id' ? $leader->bio : $leader->bio_en;
                                                    // If English bio is empty, use Indonesian bio
                                                    if (app()->getLocale() == 'en' && empty($bio) && !empty($leader->bio)) {
                                                        $bio = $leader->bio;
                                                    }
                                                ?>
                                                <div><?php echo $bio; ?></div>
                                            </div>

                                            <!-- Tabs for additional information -->
                                            <ul class="nav nav-tabs" id="leaderTabs<?php echo e($leader->id); ?>" role="tablist">
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link active" id="motto-tab<?php echo e($leader->id); ?>" data-bs-toggle="tab" data-bs-target="#motto<?php echo e($leader->id); ?>" type="button" role="tab" aria-controls="motto<?php echo e($leader->id); ?>" aria-selected="true">
                                                        <?php echo e(app()->getLocale() == 'id' ? 'Motto' : 'Motto'); ?>

                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="education-tab<?php echo e($leader->id); ?>" data-bs-toggle="tab" data-bs-target="#education<?php echo e($leader->id); ?>" type="button" role="tab" aria-controls="education<?php echo e($leader->id); ?>" aria-selected="false">
                                                        <?php echo e(app()->getLocale() == 'id' ? 'Pendidikan' : 'Education'); ?>

                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="achievements-tab<?php echo e($leader->id); ?>" data-bs-toggle="tab" data-bs-target="#achievements<?php echo e($leader->id); ?>" type="button" role="tab" aria-controls="achievements<?php echo e($leader->id); ?>" aria-selected="false">
                                                        <?php echo e(app()->getLocale() == 'id' ? 'Prestasi' : 'Achievements'); ?>

                                                    </button>
                                                </li>
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link" id="experience-tab<?php echo e($leader->id); ?>" data-bs-toggle="tab" data-bs-target="#experience<?php echo e($leader->id); ?>" type="button" role="tab" aria-controls="experience<?php echo e($leader->id); ?>" aria-selected="false">
                                                        <?php echo e(app()->getLocale() == 'id' ? 'Pengalaman' : 'Experience'); ?>

                                                    </button>
                                                </li>
                                            </ul>

                                            <div class="tab-content pt-3" id="leaderTabsContent<?php echo e($leader->id); ?>">
                                                <!-- Motto Tab -->
                                                <div class="tab-pane fade show active" id="motto<?php echo e($leader->id); ?>" role="tabpanel" aria-labelledby="motto-tab<?php echo e($leader->id); ?>">
                                                    <?php
                                                        $motto = app()->getLocale() == 'id' ? $leader->motto : $leader->motto_en;
                                                        // If English motto is empty, use Indonesian motto
                                                        if (app()->getLocale() == 'en' && empty($motto) && !empty($leader->motto)) {
                                                            $motto = $leader->motto;
                                                        }
                                                    ?>

                                                    <?php if($motto): ?>
                                                        <div class="motto-content text-center py-4">
                                                            <blockquote class="blockquote">
                                                                <p class="mb-0"><?php echo e($motto); ?></p>
                                                            </blockquote>
                                                        </div>
                                                    <?php else: ?>
                                                        <p class="text-muted"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada motto yang tersedia.' : 'No motto available.'); ?></p>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Education Tab -->
                                                <div class="tab-pane fade" id="education<?php echo e($leader->id); ?>" role="tabpanel" aria-labelledby="education-tab<?php echo e($leader->id); ?>">
                                                    <?php
                                                        $educationHistory = json_decode(app()->getLocale() == 'id' ? $leader->education_history : $leader->education_history_en, true) ?? [];
                                                        $educationHistoryId = json_decode($leader->education_history, true) ?? [];

                                                        // If English version is empty but Indonesian version exists, use Indonesian data
                                                        if (app()->getLocale() == 'en' && (empty($educationHistory) || count($educationHistory) == 0) && count($educationHistoryId) > 0) {
                                                            $educationHistory = $educationHistoryId;
                                                        } else if (app()->getLocale() == 'en' && count($educationHistory) > 0 && count($educationHistoryId) > 0) {
                                                            // For each item in English version, fill in missing fields from Indonesian version
                                                            foreach ($educationHistory as $key => $item) {
                                                                if (isset($educationHistoryId[$key])) {
                                                                    // For fields that should be the same in both languages
                                                                    if (!isset($item['year']) || empty($item['year'])) {
                                                                        $educationHistory[$key]['year'] = $educationHistoryId[$key]['year'] ?? '';
                                                                    }
                                                                    if (!isset($item['location']) || empty($item['location'])) {
                                                                        $educationHistory[$key]['location'] = $educationHistoryId[$key]['location'] ?? '';
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    ?>

                                                    <?php if(count($educationHistory) > 0): ?>
                                                        <div class="timeline">
                                                            <?php $__currentLoopData = $educationHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $education): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="timeline-item">
                                                                    <div class="timeline-dot"></div>
                                                                    <div class="timeline-content">
                                                                        <h6><?php echo e($education['institution'] ?? ''); ?></h6>
                                                                        <div><?php echo $education['degree'] ?? ''; ?></div>
                                                                        <span class="timeline-date"><?php echo e($education['year'] ?? ''); ?></span>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <p class="text-muted"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada riwayat pendidikan yang tersedia.' : 'No education history available.'); ?></p>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Achievements Tab -->
                                                <div class="tab-pane fade" id="achievements<?php echo e($leader->id); ?>" role="tabpanel" aria-labelledby="achievements-tab<?php echo e($leader->id); ?>">
                                                    <?php
                                                        $achievements = json_decode(app()->getLocale() == 'id' ? $leader->achievements : $leader->achievements_en, true) ?? [];
                                                        $achievementsId = json_decode($leader->achievements, true) ?? [];

                                                        // If English version is empty but Indonesian version exists, use Indonesian data
                                                        if (app()->getLocale() == 'en' && (empty($achievements) || count($achievements) == 0) && count($achievementsId) > 0) {
                                                            $achievements = $achievementsId;
                                                        } else if (app()->getLocale() == 'en' && count($achievements) > 0 && count($achievementsId) > 0) {
                                                            // For each item in English version, fill in missing fields from Indonesian version
                                                            foreach ($achievements as $key => $item) {
                                                                if (isset($achievementsId[$key])) {
                                                                    // For fields that should be the same in both languages
                                                                    if (!isset($item['year']) || empty($item['year'])) {
                                                                        $achievements[$key]['year'] = $achievementsId[$key]['year'] ?? '';
                                                                    }
                                                                    if (!isset($item['description']) || empty($item['description'])) {
                                                                        $achievements[$key]['description'] = $achievementsId[$key]['description'] ?? '';
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    ?>

                                                    <?php if(count($achievements) > 0): ?>
                                                        <div class="timeline">
                                                            <?php $__currentLoopData = $achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="timeline-item">
                                                                    <div class="timeline-dot"></div>
                                                                    <div class="timeline-content">
                                                                        <h6><?php echo e($achievement['title'] ?? ''); ?></h6>
                                                                        <div class="timeline-description"><?php echo $achievement['description'] ?? ''; ?></div>
                                                                        <span class="timeline-date"><?php echo e($achievement['year'] ?? ''); ?></span>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <p class="text-muted"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada prestasi yang tersedia.' : 'No achievements available.'); ?></p>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Experience Tab -->
                                                <div class="tab-pane fade" id="experience<?php echo e($leader->id); ?>" role="tabpanel" aria-labelledby="experience-tab<?php echo e($leader->id); ?>">
                                                    <?php
                                                        $workExperience = json_decode(app()->getLocale() == 'id' ? $leader->work_experience : $leader->work_experience_en, true) ?? [];
                                                        $workExperienceId = json_decode($leader->work_experience, true) ?? [];

                                                        // If English version is empty but Indonesian version exists, use Indonesian data
                                                        if (app()->getLocale() == 'en' && (empty($workExperience) || count($workExperience) == 0) && count($workExperienceId) > 0) {
                                                            $workExperience = $workExperienceId;
                                                        } else if (app()->getLocale() == 'en' && count($workExperience) > 0 && count($workExperienceId) > 0) {
                                                            // For each item in English version, fill in missing fields from Indonesian version
                                                            foreach ($workExperience as $key => $item) {
                                                                if (isset($workExperienceId[$key])) {
                                                                    // For fields that should be the same in both languages
                                                                    if (!isset($item['period']) || empty($item['period'])) {
                                                                        $workExperience[$key]['period'] = $workExperienceId[$key]['period'] ?? '';
                                                                    }
                                                                    if (!isset($item['location']) || empty($item['location'])) {
                                                                        $workExperience[$key]['location'] = $workExperienceId[$key]['location'] ?? '';
                                                                    }
                                                                    if (!isset($item['description']) || empty($item['description'])) {
                                                                        $workExperience[$key]['description'] = $workExperienceId[$key]['description'] ?? '';
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    ?>

                                                    <?php if(count($workExperience) > 0): ?>
                                                        <div class="timeline">
                                                            <?php $__currentLoopData = $workExperience; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $experience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="timeline-item">
                                                                    <div class="timeline-dot"></div>
                                                                    <div class="timeline-content">
                                                                        <h6><?php echo e($experience['position'] ?? ''); ?></h6>
                                                                        <div><?php echo e($experience['company'] ?? ''); ?></div>
                                                                        <div class="timeline-description mt-2"><?php echo $experience['description'] ?? ''; ?></div>
                                                                        <span class="timeline-date"><?php echo e($experience['period'] ?? ''); ?></span>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <p class="text-muted"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada pengalaman kerja yang tersedia.' : 'No work experience available.'); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <a href="<?php echo e(route('leaders.show', $leader->id)); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Kunjungi' : 'Visit'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <?php echo e(app()->getLocale() == 'id' ? 'Belum ada data pemimpin yang tersedia.' : 'No leader data available yet.'); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Leader Card Styles */
    .leader-card {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .leader-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .leader-image {
        position: relative;
        overflow: hidden;
    }

    .leader-image img {
        width: 100%;
        aspect-ratio: 4/5;
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .leader-card:hover .leader-image img {
        transform: scale(1.05);
    }

    .leader-social {
        position: absolute;
        bottom: -50px;
        left: 0;
        right: 0;
        background-color: rgba(25, 135, 84, 0.9);
        display: flex;
        justify-content: center;
        padding: 10px 0;
        transition: all 0.3s ease;
    }

    .leader-card:hover .leader-social {
        bottom: 0;
    }

    .leader-social a {
        color: #fff;
        margin: 0 10px;
        font-size: 16px;
        transition: all 0.3s ease;
    }

    .leader-social a:hover {
        transform: scale(1.2);
    }

    .leader-info {
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .leader-info h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .leader-position {
        color: #198754;
        font-weight: 500;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .leader-bio {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        flex-grow: 1;
    }

    /* Text formatting styles */
    .leader-bio p, .leader-modal-bio p, .timeline-description p {
        margin-bottom: 0.5rem;
    }

    .leader-modal-bio ul, .leader-modal-bio ol, .timeline-description ul, .timeline-description ol {
        padding-left: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .leader-modal-bio table, .timeline-description table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }

    .leader-modal-bio table th, .leader-modal-bio table td,
    .timeline-description table th, .timeline-description table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .leader-modal-bio table th, .timeline-description table th {
        background-color: #f8f9fa;
    }

    .leader-modal-bio img, .timeline-description img {
        max-width: 100%;
        height: auto;
        margin-bottom: 0.5rem;
    }

    .leader-contact {
        font-size: 13px;
        color: #777;
        margin-bottom: 10px;
    }

    .leader-contact div {
        margin-bottom: 5px;
    }

    .leader-contact i {
        color: #198754;
        width: 20px;
        text-align: center;
        margin-right: 5px;
    }

    .leader-details-btn {
        align-self: flex-start;
        transition: all 0.3s ease;
    }

    .leader-details-btn:hover {
        background-color: #198754;
        color: #fff;
    }

    /* Modal Styles */
    .leader-modal-image img {
        width: 100%;
        aspect-ratio: 4/5;
        object-fit: cover;
    }

    .leader-modal-contact {
        font-size: 14px;
    }

    .leader-modal-bio {
        font-size: 14px;
        color: #333;
    }

    .leader-modal-bio h1, .leader-modal-bio h2, .leader-modal-bio h3,
    .leader-modal-bio h4, .leader-modal-bio h5, .leader-modal-bio h6 {
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }

    /* Motto Styles */
    .motto-content {
        background-color: #f8f9fa;
        border-radius: 8px;
        position: relative;
    }

    .motto-content .blockquote {
        position: relative;
        padding: 1.5rem;
        font-style: italic;
        color: #333;
    }

    .motto-content .blockquote:before,
    .motto-content .blockquote:after {
        content: '"';
        font-size: 2.5rem;
        color: #198754;
        opacity: 0.3;
        position: absolute;
    }

    .motto-content .blockquote:before {
        top: -10px;
        left: 10px;
    }

    .motto-content .blockquote:after {
        bottom: -30px;
        right: 10px;
    }

    .leader-modal-social a {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 32px;
        height: 32px;
        background-color: #f8f9fa;
        border-radius: 50%;
        color: #198754;
        transition: all 0.3s ease;
    }

    .leader-modal-social a:hover {
        background-color: #198754;
        color: #fff;
        transform: translateY(-3px);
    }

    /* Timeline Styles */
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline:before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e9ecef;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-dot {
        position: absolute;
        left: -30px;
        top: 5px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #198754;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }

    .timeline-content {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        position: relative;
    }

    .timeline-content h6 {
        margin-bottom: 5px;
        font-weight: 600;
    }

    .timeline-content p {
        margin-bottom: 5px;
        font-size: 14px;
    }

    .timeline-description {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .timeline-date {
        display: inline-block;
        font-size: 12px;
        color: #6c757d;
        font-weight: 500;
    }

    /* Responsive Styles */
    @media (max-width: 767.98px) {
        .leader-card {
            margin-bottom: 30px;
        }

        .leader-info h3 {
            font-size: 18px;
        }

        .leader-position {
            font-size: 13px;
        }

        .leader-bio {
            font-size: 13px;
        }

        .leader-modal-image {
            margin-bottom: 20px;
        }

        .timeline {
            padding-left: 25px;
        }

        .timeline:before {
            left: 8px;
        }

        .timeline-dot {
            left: -25px;
            width: 16px;
            height: 16px;
        }

        .timeline-content {
            padding: 12px;
        }

        .timeline-content h6 {
            font-size: 14px;
        }

        .timeline-description {
            font-size: 13px;
        }

        .timeline-date {
            font-size: 11px;
        }

        .nav-tabs .nav-link {
            padding: 0.5rem 0.75rem;
            font-size: 14px;
        }
    }

    @media (max-width: 575.98px) {
        .leader-modal-social a {
            width: 28px;
            height: 28px;
            font-size: 14px;
        }

        .leader-modal-bio {
            font-size: 13px;
        }

        .nav-tabs .nav-link {
            padding: 0.4rem 0.5rem;
            font-size: 13px;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations for leader cards
        const leaderCards = document.querySelectorAll('.leader-card');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        leaderCards.forEach(card => {
            observer.observe(card);
        });

        // Initialize animations for section title
        const sectionTitle = document.querySelector('.section-title');
        if (sectionTitle) {
            observer.observe(sectionTitle);
        }

        // Initialize tab animations in modals
        const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');

        tabLinks.forEach(link => {
            link.addEventListener('click', function() {
                // Get the target tab content
                const targetId = this.getAttribute('data-bs-target');
                const targetPane = document.querySelector(targetId);

                // Add a slight delay to allow the Bootstrap tab transition to complete
                setTimeout(() => {
                    // Trigger animations for elements inside the active tab
                    const animElements = targetPane.querySelectorAll('.timeline-item');
                    animElements.forEach((element, index) => {
                        // Add staggered animation delay
                        element.style.opacity = '0';
                        element.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            element.style.transition = 'all 0.5s ease';
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                }, 150);
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/leaders/index.blade.php ENDPATH**/ ?>