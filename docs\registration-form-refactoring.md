# Registration Form Refactoring

This document explains the refactoring of the registration form in the application.

## Overview

The registration form has been refactored to improve user experience, form validation, and code maintainability. The refactoring includes:

1. Creation of a dedicated form request class for validation
2. Implementation of the DocumentUploadable trait for document handling
3. Improved JavaScript validation and user interaction
4. Enhanced terms and conditions display
5. Better form field formatting and validation

## Components

### RegistrationRequest Class

The `RegistrationRequest` class provides:

- Centralized validation rules for all registration form fields
- Custom validation messages in both Indonesian and English
- Authorization check to ensure registration is open
- Custom error handling for failed authorization

### DocumentUploadable Trait

The `DocumentUploadable` trait provides:

- Automatic document upload handling
- Automatic document deletion when a model is deleted
- Configurable document fields and paths
- Methods for handling document uploads and deletions

### Registration Model Updates

The Registration model has been updated to:

- Extend the `BaseModel` class
- Implement the `DocumentUploadable` trait
- Define document fields and paths

### JavaScript Improvements

The JavaScript code has been refactored to:

- Make the form initially hidden and appear with a fade effect when the 'Daftar Sekarang' button is clicked
- Add numeric form field validation
- Add name field auto-uppercase
- Add NIK field validation for exactly 16 digits
- Add location field auto-capitalization
- Make NISN field optional
- Add hobby/ambition field title case formatting
- Improve phone number field with country code selection and flags

### Terms and Conditions Dialog

The terms and conditions display has been improved to:

- Show a dialog box that automatically opens on first checkbox click
- Provide accept and decline buttons
- Improve readability with better formatting

## Usage

### Form Request Validation

The form request validation is automatically applied when the controller uses the `RegistrationRequest` class:

```php
public function store(RegistrationRequest $request)
{
    // The request is already validated
    $data = $request->validated();
    
    // Process the validated data
    // ...
}
```

### Document Upload

Document uploads are handled through the `DocumentUploadable` trait:

```php
if ($request->hasFile('document')) {
    $data['document'] = Registration::make()->handleDocumentUpload($request->file('document'), 'document');
}
```

### JavaScript Features

The JavaScript features are automatically applied to form fields with the appropriate classes:

- `.numeric-only` - Only allows numeric input
- `.name-field` - Automatically converts input to uppercase
- `.text-uppercase` - Automatically converts input to uppercase
- `.location-field` - Automatically converts input to uppercase
- `.capitalize-words` - Automatically capitalizes the first letter of each word

## Benefits

This refactoring provides several benefits:

1. **Improved code organization**: Validation logic is centralized in the form request class
2. **Better user experience**: Form fields are automatically formatted as the user types
3. **Enhanced validation**: Client-side validation provides immediate feedback to users
4. **Consistent error handling**: Server-side validation provides consistent error messages
5. **Improved terms and conditions display**: Users can easily read and accept the terms and conditions

## Future Improvements

Potential future improvements include:

1. Adding client-side form validation for all fields
2. Implementing a multi-step form process
3. Adding progress indicators for form completion
4. Implementing form autosave functionality
5. Adding support for document preview before upload
