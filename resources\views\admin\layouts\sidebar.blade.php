<div class="bg-dark text-white" id="sidebar-wrapper">
    <div class="sidebar-heading text-center py-4">
        <div class="fs-5">Admin Panel</div>
    </div>
    <div class="list-group list-group-flush my-3">
        <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action bg-transparent text-white {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
            <i class="fas fa-tachometer-alt me-2"></i><span>Dashboard</span>
        </a>

        <!-- Website Menu - Admin and Operator only -->
        @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
        <a href="#" class="list-group-item list-group-item-action bg-transparent text-white has-submenu {{ request()->routeIs('admin.website.*') || request()->routeIs('admin.profiles*') || request()->routeIs('admin.pages*') || request()->routeIs('admin.swipers*') ? 'active' : '' }}">
            <i class="fas fa-globe me-2"></i><span>Website</span>
            <i class="fas fa-chevron-down submenu-toggle"></i>
        </a>
        <div class="sidebar-submenu {{ request()->routeIs('admin.website.*') || request()->routeIs('admin.profiles*') || request()->routeIs('admin.pages*') || request()->routeIs('admin.swipers*') ? 'show' : '' }}">
            <a href="{{ route('admin.website.identity') }}" class="submenu-item {{ request()->routeIs('admin.website.identity') ? 'active' : '' }}">
                <i class="fas fa-id-card me-2"></i><span>Identity</span>
            </a>
            <a href="{{ route('admin.swipers.index') }}" class="submenu-item {{ request()->routeIs('admin.swipers*') ? 'active' : '' }}">
                <i class="fas fa-images me-2"></i><span>Swiper</span>
            </a>
            <a href="{{ route('admin.website.menu') }}" class="submenu-item {{ request()->routeIs('admin.website.menu*') ? 'active' : '' }}">
                <i class="fas fa-bars me-2"></i><span>Menu</span>
            </a>
            <a href="{{ route('admin.pages.index') }}" class="submenu-item {{ request()->routeIs('admin.pages*') ? 'active' : '' }}">
                <i class="fas fa-file me-2"></i><span>Pages</span>
            </a>
            <a href="{{ route('admin.profiles.index') }}" class="submenu-item {{ request()->routeIs('admin.profiles*') ? 'active' : '' }}">
                <i class="fas fa-school me-2"></i><span>Profile</span>
            </a>
        </div>
        @endif

        <!-- News and Media Menu -->
        <a href="#" class="list-group-item list-group-item-action bg-transparent text-white has-submenu {{
            request()->routeIs('admin.news*') ||
            request()->routeIs('admin.galleries*') ||
            request()->routeIs('admin.videos*') ||
            request()->routeIs('admin.categories*') ? 'active' : ''
        }}">
            <i class="fas fa-newspaper me-2"></i><span>News and Media</span>
            <i class="fas fa-chevron-down submenu-toggle"></i>
        </a>
        <div class="sidebar-submenu {{
            request()->routeIs('admin.news*') ||
            request()->routeIs('admin.galleries*') ||
            request()->routeIs('admin.videos*') ||
            request()->routeIs('admin.categories*') ? 'show' : ''
        }}">
            <a href="{{ route('admin.news.index') }}" class="submenu-item {{ request()->routeIs('admin.news*') ? 'active' : '' }}">
                <i class="fas fa-newspaper me-2"></i>News
            </a>
            <a href="{{ route('admin.galleries.index') }}" class="submenu-item {{ request()->routeIs('admin.galleries*') ? 'active' : '' }}">
                <i class="fas fa-images me-2"></i>Gallery
            </a>
            <a href="{{ route('admin.videos.index') }}" class="submenu-item {{ request()->routeIs('admin.videos*') ? 'active' : '' }}">
                <i class="fas fa-video me-2"></i>Videos
            </a>
            @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
            <a href="{{ route('admin.categories.index') }}" class="submenu-item {{ request()->routeIs('admin.categories*') ? 'active' : '' }}">
                <i class="fas fa-tags me-2"></i>News Categories
            </a>
            @endif
        </div>

        <!-- Artwork Menu - Accessible by all roles -->
        <a href="#" class="list-group-item list-group-item-action bg-transparent text-white has-submenu {{
            request()->routeIs('admin.artwork.*') ? 'active' : ''
        }}">
            <i class="fas fa-palette me-2"></i><span>Artwork</span>
            <i class="fas fa-chevron-down submenu-toggle"></i>
        </a>
        <div class="sidebar-submenu {{
            request()->routeIs('admin.artwork.*') ? 'show' : ''
        }}">
            <a href="{{ route('admin.artwork.paints.index') }}" class="submenu-item {{ request()->routeIs('admin.artwork.paints*') ? 'active' : '' }}">
                <i class="fas fa-paint-brush me-2"></i>Paints
            </a>
            <a href="{{ route('admin.artwork.literature.index') }}" class="submenu-item {{ request()->routeIs('admin.artwork.literature*') ? 'active' : '' }}">
                <i class="fas fa-book me-2"></i>Literature
            </a>
            @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
            <a href="{{ route('admin.artwork.types.index') }}" class="submenu-item {{ request()->routeIs('admin.artwork.types*') ? 'active' : '' }}">
                <i class="fas fa-tags me-2"></i>Literature Types
            </a>
            @endif
        </div>

        <!-- Institution Module Menu -->
        <a href="#" class="list-group-item list-group-item-action bg-transparent text-white has-submenu {{
            request()->routeIs('admin.leaders*') ||
            request()->routeIs('admin.facilities*') ||
            request()->routeIs('admin.programs*') ||
            request()->routeIs('admin.education-units*') ||
            request()->routeIs('admin.achievements*') ||
            request()->routeIs('admin.partnerships*') ? 'active' : ''
        }}">
            <i class="fas fa-university me-2"></i><span>Institution Module</span>
            <i class="fas fa-chevron-down submenu-toggle"></i>
        </a>
        <div class="sidebar-submenu {{
            request()->routeIs('admin.leaders*') ||
            request()->routeIs('admin.facilities*') ||
            request()->routeIs('admin.programs*') ||
            request()->routeIs('admin.education-units*') ||
            request()->routeIs('admin.achievements*') ||
            request()->routeIs('admin.partnerships*') ? 'show' : ''
        }}">
            @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
            <a href="{{ route('admin.leaders.index') }}" class="submenu-item {{ request()->routeIs('admin.leaders*') ? 'active' : '' }}">
                <i class="fas fa-user-tie me-2"></i>Leaders
            </a>
            <a href="{{ route('admin.facilities.index') }}" class="submenu-item {{ request()->routeIs('admin.facilities*') ? 'active' : '' }}">
                <i class="fas fa-building me-2"></i>Facilities
            </a>
            <a href="{{ route('admin.programs.index') }}" class="submenu-item {{ request()->routeIs('admin.programs*') ? 'active' : '' }}">
                <i class="fas fa-graduation-cap me-2"></i>Programs
            </a>
            <a href="{{ route('admin.education-units.index') }}" class="submenu-item {{ request()->routeIs('admin.education-units*') ? 'active' : '' }}">
                <i class="fas fa-school me-2"></i>Education Units
            </a>
            <a href="{{ route('admin.partnerships.index') }}" class="submenu-item {{ request()->routeIs('admin.partnerships*') ? 'active' : '' }}">
                <i class="fas fa-handshake me-2"></i>Partnership
            </a>
            @endif
            <!-- Achievements - accessible by all roles -->
            <a href="{{ route('admin.achievements.index') }}" class="submenu-item {{ request()->routeIs('admin.achievements*') ? 'active' : '' }}">
                <i class="fas fa-trophy me-2"></i>Achievements
            </a>
        </div>

        <!-- Curriculum Affairs Menu -->
        <a href="#" class="list-group-item list-group-item-action bg-transparent text-white has-submenu {{
            request()->routeIs('admin.calendars*') ||
            request()->routeIs('admin.schedules*') ||
            request()->routeIs('admin.curricula*') ||
            request()->routeIs('admin.agendas*') ||
            request()->routeIs('admin.announcements*') ? 'active' : ''
        }}">
            <i class="fas fa-book me-2"></i><span>Curriculum Affairs</span>
            <i class="fas fa-chevron-down submenu-toggle"></i>
        </a>
        <div class="sidebar-submenu {{
            request()->routeIs('admin.calendars*') ||
            request()->routeIs('admin.schedules*') ||
            request()->routeIs('admin.curricula*') ||
            request()->routeIs('admin.agendas*') ||
            request()->routeIs('admin.announcements*') ? 'show' : ''
        }}">
            <!-- Academic Calendar - accessible by all roles -->
            <a href="{{ route('admin.calendars.index') }}" class="submenu-item {{ request()->routeIs('admin.calendars*') ? 'active' : '' }}">
                <i class="fas fa-calendar-alt me-2"></i>Academic Calendar
            </a>
            <!-- Activity Schedule - accessible by all roles -->
            <a href="{{ route('admin.schedules.index') }}" class="submenu-item {{ request()->routeIs('admin.schedules*') ? 'active' : '' }}">
                <i class="fas fa-calendar-day me-2"></i>Activity Schedule
            </a>
            @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
            <a href="{{ route('admin.curricula.index') }}" class="submenu-item {{ request()->routeIs('admin.curricula*') ? 'active' : '' }}">
                <i class="fas fa-book-open me-2"></i>Curriculum
            </a>
            @endif
            <!-- Agenda - accessible by all roles -->
            <a href="{{ route('admin.agendas.index') }}" class="submenu-item {{ request()->routeIs('admin.agendas*') ? 'active' : '' }}">
                <i class="fas fa-calendar-check me-2"></i>Agenda
            </a>
            <!-- Announcements - accessible by all roles -->
            <a href="{{ route('admin.announcements.index') }}" class="submenu-item {{ request()->routeIs('admin.announcements*') ? 'active' : '' }}">
                <i class="fas fa-bullhorn me-2"></i>Announcements
            </a>
        </div>

        <!-- Director's Insight - Admin only -->
        @if(auth()->user()->isAdmin())
        <a href="{{ route('admin.directors-insight.index') }}" class="list-group-item list-group-item-action bg-transparent text-white {{
            request()->routeIs('admin.directors-insight*') ||
            request()->routeIs('admin.directors*') ||
            request()->routeIs('admin.insights*') ? 'active' : ''
        }}">
            <i class="fas fa-lightbulb me-2"></i><span>Director's Insight</span>
        </a>
        @endif

        <!-- Registration - Admin and Operator only -->
        @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
        <a href="{{ route('admin.registrations.index') }}" class="list-group-item list-group-item-action bg-transparent text-white {{ request()->routeIs('admin.registrations*') ? 'active' : '' }}">
            <i class="fas fa-user-plus me-2"></i><span>Registration</span>
            @php
                $pendingCount = \App\Models\Registration::where('status', 'pending')->count();
            @endphp
            @if($pendingCount > 0)
                <span class="badge bg-warning rounded-pill ms-2">{{ $pendingCount }}</span>
            @endif
        </a>
        @endif

        <!-- Contact - Admin and Operator only -->
        @if(auth()->user()->isAdmin() || auth()->user()->isOperator())
        <a href="{{ route('admin.contacts.index') }}" class="list-group-item list-group-item-action bg-transparent text-white {{ request()->routeIs('admin.contacts*') ? 'active' : '' }}">
            <i class="fas fa-envelope me-2"></i><span>Contact</span>
            @php
                $unreadCount = \App\Models\Contact::where('is_read', false)->count();
            @endphp
            @if($unreadCount > 0)
                <span class="badge bg-danger rounded-pill ms-2">{{ $unreadCount }}</span>
            @endif
        </a>
        @endif



        <!-- Settings Menu - Admin only -->
        @if(auth()->user()->isAdmin())
        <a href="#" class="list-group-item list-group-item-action bg-transparent text-white has-submenu {{ request()->routeIs('admin.settings.*') ? 'active' : '' }}">
            <i class="fas fa-cogs me-2"></i><span>Settings</span>
            <i class="fas fa-chevron-down submenu-toggle"></i>
        </a>
        <div class="sidebar-submenu {{ request()->routeIs('admin.settings.*') ? 'show' : '' }}">
            <a href="{{ route('admin.settings.users.index') }}" class="submenu-item {{ request()->routeIs('admin.settings.users*') ? 'active' : '' }}">
                <i class="fas fa-users me-2"></i>Users
            </a>
            <a href="{{ route('admin.settings.configuration.operator.index') }}" class="submenu-item {{ request()->routeIs('admin.settings.configuration*') ? 'active' : '' }}">
                <i class="fas fa-sliders-h me-2"></i>Configuration
            </a>
        </div>
        @elseif(auth()->user()->isOperator())
        <!-- Configuration Settings - Operator only -->
        <a href="{{ route('admin.settings.configuration.operator.index', ['tab' => 'general']) }}" class="list-group-item list-group-item-action bg-transparent text-white {{ request()->routeIs('admin.settings.configuration.operator*') ? 'active' : '' }}">
            <i class="fas fa-sliders-h me-2"></i><span>Configuration</span>
        </a>
        @endif


    </div>
</div>
