<!-- Latest Agenda -->
<div class="card border-0 shadow-sm mb-4 fade-in">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Agenda Terbaru' : 'Latest Agenda'); ?></h5>
    </div>
    <div class="card-body">
        <?php $__empty_1 = true; $__currentLoopData = $latestAgenda; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agenda): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="related-item mb-3 pb-3 <?php echo e(!$loop->last ? 'border-bottom' : ''); ?>">
                <div class="d-flex">
                    <div class="agenda-date-small me-3">
                        <div class="day"><?php echo e($agenda->date->format('d')); ?></div>
                        <div class="month"><?php echo e($agenda->date->format('M')); ?></div>
                    </div>
                    <div>
                        <h6 class="mb-1">
                            <a href="<?php echo e(route('agenda.show', $agenda->id)); ?>" class="text-decoration-none">
                                <?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($agenda->title, 50) : \Illuminate\Support\Str::limit($agenda->title_en, 50)); ?>

                            </a>
                        </h6>
                        <div class="small text-muted">
                            <i class="far fa-clock me-1"></i> <?php echo e($agenda->time->format('H:i')); ?>

                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada agenda terbaru.' : 'No latest agenda.'); ?></p>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH /home/<USER>/laravel/resources/views/public/partials/latest-agenda.blade.php ENDPATH**/ ?>