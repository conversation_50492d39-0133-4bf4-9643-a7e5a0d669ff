/**
 * Custom Summernote Image Upload Handler
 * This script provides a custom implementation for handling image uploads in Summernote
 */

$(document).ready(function() {
    console.log('Summernote Image Upload Handler loaded');

    // Create a hidden file input for image uploads
    if ($('#summernote-file-input').length === 0) {
        $('body').append('<input type="file" id="summernote-file-input" style="display: none;" accept="image/*">');
    }

    // Function to initialize Summernote editors
    function initSummernoteEditors() {
        // Skip description fields in swiper pages
        let selector = '.summernote, #content, #content_en, #bio, #bio_en';

        // Only include description fields if we're not on a swiper page
        if (!window.location.href.includes('/admin/swipers/')) {
            selector += ', #description, #description_en';
        }

        // Find all Summernote editors
        $(selector).each(function() {
            // Skip elements with data-no-summernote attribute
            if ($(this).attr('data-no-summernote') === 'true') return;

            const editor = this;

            // Find the image button in this editor's toolbar
            const $imageBtn = $(editor).next('.note-editor').find('.note-btn[data-original-title="Picture"]');
            if ($imageBtn.length === 0) return;

            // Override the click event
            $imageBtn.off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Store the current editor in a data attribute
                $('#summernote-file-input').data('editor', editor);

                // Trigger the file input
                $('#summernote-file-input').click();
            });
        });
    }

    // Set up the file input change event
    $('#summernote-file-input').on('change', function(e) {
        const files = e.target.files;
        if (files && files.length > 0) {
            const editor = $(this).data('editor');
            uploadImage(files[0], editor);

            // Reset the file input so the same file can be selected again
            $(this).val('');
        }
    });

    // Function to handle image upload
    function uploadImage(file, editor) {
        console.log('Starting image upload process');

        // Check file size before uploading (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('Image size exceeds 10MB limit. Please choose a smaller image.');
            return;
        }

        // Check file type
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
        if (!validTypes.includes(file.type)) {
            alert('Invalid file type. Please use JPG, PNG, or GIF images only.');
            return;
        }

        // Create form data
        var formData = new FormData();
        formData.append("image", file);
        formData.append("_token", $('meta[name="csrf-token"]').attr('content'));

        // Show loading indicator
        var $loadingIndicator = $('<div class="summernote-loading-indicator">Uploading image...</div>');
        $('body').append($loadingIndicator);

        // Send the image to the server
        $.ajax({
            url: "/admin/upload-summernote-image",
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            cache: false,
            success: function(response) {
                console.log('Server response:', response);

                // Remove loading indicator
                $loadingIndicator.remove();

                if (response.success) {
                    console.log('Image uploaded successfully, URL:', response.url);
                    console.log('Absolute URL:', response.absolute_url);

                    // Test if the image is accessible by creating a temporary image element
                    var testImg = new Image();
                    testImg.onload = function() {
                        console.log('Image loaded successfully from URL:', response.url);
                    };
                    testImg.onerror = function() {
                        console.error('Failed to load image from URL:', response.url);
                        // Try with absolute URL as fallback
                        var testImg2 = new Image();
                        testImg2.onload = function() {
                            console.log('Image loaded successfully from absolute URL:', response.absolute_url);
                            // Use absolute URL instead since relative URL failed
                            updateEditorWithImage(response.absolute_url);
                        };
                        testImg2.onerror = function() {
                            console.error('Failed to load image from absolute URL:', response.absolute_url);
                            // Try with a modified URL as last resort
                            var modifiedUrl = response.url.replace('/storage/', '/public/storage/');
                            console.log('Trying modified URL:', modifiedUrl);
                            updateEditorWithImage(response.url);
                        };
                        testImg2.src = response.absolute_url;
                    };
                    testImg.src = response.url;

                    // Function to update the editor with the image
                    function updateEditorWithImage(imgUrl) {
                        // Get the current content
                        var currentContent = $(editor).summernote('code');

                        // Create image HTML
                        var imageHtml = '<img src="' + imgUrl + '" alt="' + (response.filename || 'Uploaded image') + '" style="max-width: 100%;">';

                        // Append the image to the content
                        $(editor).summernote('code', currentContent + imageHtml);

                        console.log('Image HTML added to editor content with URL:', imgUrl);
                    }

                    // Update the editor with the default URL
                    updateEditorWithImage(response.url);
                } else {
                    console.error('Upload failed:', response.message);
                    alert('Upload failed: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error during upload:', error);
                console.error('Server response:', xhr.responseText);

                // Remove loading indicator
                $loadingIndicator.remove();

                // Parse error message
                let errorMessage = 'Failed to upload image';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    } else if (response.errors && response.errors.image) {
                        errorMessage = response.errors.image[0];
                    }
                } catch (e) {
                    errorMessage = 'Server error: ' + error;
                }

                alert(errorMessage);
            }
        });
    }

    // Initialize after a short delay to ensure Summernote is fully loaded
    setTimeout(function() {
        initSummernoteEditors();

        // Also initialize when the DOM changes (for dynamically added editors)
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    initSummernoteEditors();
                }
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });

        console.log('Summernote image upload handlers initialized');
    }, 1000);
});
