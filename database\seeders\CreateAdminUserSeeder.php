<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateAdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('<PERSON><PERSON>ullah'),
            'email_verified_at' => now(),
        ]);
    }
}
