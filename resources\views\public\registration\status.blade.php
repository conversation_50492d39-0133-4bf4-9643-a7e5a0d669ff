@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? 'Status Pendaftaran' : 'Registration Status')

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Status Pendaftaran' : 'Registration Status' }}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Beranda' : 'Home' }}</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('registration') }}" class="text-white">{{ app()->getLocale() == 'id' ? 'Pendaftaran' : 'Registration' }}</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ app()->getLocale() == 'id' ? 'Status' : 'Status' }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Status Information -->
    <section class="section-padding">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto" data-aos="fade-up">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="card-title text-success mb-4 text-center">{{ app()->getLocale() == 'id' ? 'Informasi Status Pendaftaran' : 'Registration Status Information' }}</h2>

                            <div class="registration-info mb-4">
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">{{ app()->getLocale() == 'id' ? 'Nomor Pendaftaran' : 'Registration Number' }}:</div>
                                    <div class="col-md-8">{{ $registration->registration_number }}</div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">{{ app()->getLocale() == 'id' ? 'Nama Lengkap' : 'Full Name' }}:</div>
                                    <div class="col-md-8">{{ $registration->full_name }}</div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">{{ app()->getLocale() == 'id' ? 'Tanggal Pendaftaran' : 'Registration Date' }}:</div>
                                    <div class="col-md-8">{{ $registration->created_at ? $registration->created_at->format('d M Y') : (app()->getLocale() == 'id' ? 'Tidak tersedia' : 'Not available') }}</div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">{{ app()->getLocale() == 'id' ? 'Status' : 'Status' }}:</div>
                                    <div class="col-md-8">
                                        @if($registration->status == 'pending')
                                            <span class="badge bg-warning">{{ app()->getLocale() == 'id' ? 'Menunggu' : 'Pending' }}</span>
                                        @elseif($registration->status == 'approved')
                                            <span class="badge bg-success">{{ app()->getLocale() == 'id' ? 'Diterima' : 'Approved' }}</span>
                                        @elseif($registration->status == 'rejected')
                                            <span class="badge bg-danger">{{ app()->getLocale() == 'id' ? 'Ditolak' : 'Rejected' }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="status-message p-4 rounded
                                @if($registration->status == 'pending') bg-warning bg-opacity-10 border border-warning
                                @elseif($registration->status == 'approved') bg-success bg-opacity-10 border border-success
                                @elseif($registration->status == 'rejected') bg-danger bg-opacity-10 border border-danger
                                @endif
                            ">
                                @if($registration->status == 'pending')
                                    <h5 class="text-warning mb-3">{{ app()->getLocale() == 'id' ? 'Pendaftaran Anda sedang diproses' : 'Your registration is being processed' }}</h5>
                                    <p>{{ app()->getLocale() == 'id' ? 'Terima kasih telah mendaftar di Pondok Pesantren Nurul Hayah 4. Pendaftaran Anda sedang dalam proses peninjauan. Kami akan menghubungi Anda segera setelah proses peninjauan selesai.' : 'Thank you for registering at Nurul Hayah 4 Islamic Boarding School. Your registration is currently under review. We will contact you as soon as the review process is complete.' }}</p>
                                    <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Pastikan Anda telah melakukan pembayaran biaya pendaftaran dan mengirimkan konfirmasi pembayaran untuk mempercepat proses peninjauan.' : 'Make sure you have made the registration fee payment and sent the payment confirmation to speed up the review process.' }}</p>
                                @elseif($registration->status == 'approved')
                                    <h5 class="text-success mb-3">{{ app()->getLocale() == 'id' ? 'Selamat! Pendaftaran Anda telah diterima' : 'Congratulations! Your registration has been approved' }}</h5>
                                    <p>{{ app()->getLocale() == 'id' ? 'Kami dengan senang hati memberitahukan bahwa pendaftaran Anda sebagai santri baru di Pondok Pesantren Nurul Hayah 4 telah diterima.' : 'We are pleased to inform you that your registration as a new student at Nurul Hayah 4 Islamic Boarding School has been approved.' }}</p>
                                    <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Kami akan menghubungi Anda melalui telepon atau email untuk memberikan informasi lebih lanjut mengenai proses penerimaan dan orientasi santri baru.' : 'We will contact you by phone or email to provide further information regarding the admission process and new student orientation.' }}</p>
                                @elseif($registration->status == 'rejected')
                                    <h5 class="text-danger mb-3">{{ app()->getLocale() == 'id' ? 'Mohon maaf, pendaftaran Anda belum dapat diterima' : 'We are sorry, your registration cannot be accepted at this time' }}</h5>
                                    <p>{{ app()->getLocale() == 'id' ? 'Kami mohon maaf untuk memberitahukan bahwa pendaftaran Anda sebagai santri baru di Pondok Pesantren Nurul Hayah 4 belum dapat kami terima saat ini.' : 'We regret to inform you that your registration as a new student at Nurul Hayah 4 Islamic Boarding School cannot be accepted at this time.' }}</p>
                                    <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Untuk informasi lebih lanjut, silakan hubungi kami melalui telepon atau email yang tertera di halaman Kontak.' : 'For more information, please contact us via phone or email listed on the Contact page.' }}</p>
                                @endif
                            </div>

                            <div class="text-center mt-4">
                                <a href="{{ route('registration') }}" class="btn btn-outline-success me-2">{{ app()->getLocale() == 'id' ? 'Kembali ke Halaman Pendaftaran' : 'Back to Registration Page' }}</a>
                                <a href="{{ route('registration.print', ['registration' => $registration->id]) }}" class="btn btn-success me-2">{{ app()->getLocale() == 'id' ? 'Cetak Bukti Pendaftaran' : 'Print Registration Proof' }}</a>
                                <a href="{{ route('contact') }}" class="btn btn-outline-secondary">{{ app()->getLocale() == 'id' ? 'Hubungi Kami' : 'Contact Us' }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
