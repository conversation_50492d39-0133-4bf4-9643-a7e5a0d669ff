<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;

class EmailService
{
    /**
     * Get email configuration for a specific email type
     *
     * @param string $emailType The type of email (password_reset, notifications, confirmations, transactional, default)
     * @return array The email configuration
     */
    public function getEmailConfig(string $emailType = 'default'): array
    {
        // Validate email type
        $validTypes = ['default', 'password_reset', 'notifications', 'confirmations', 'transactional'];
        if (!in_array($emailType, $validTypes)) {
            \Illuminate\Support\Facades\Log::warning("Invalid email type '{$emailType}' requested, falling back to 'default'");
            $emailType = 'default';
        }

        // Get mailer configuration from config/mail.php
        $mailerConfig = config("mail.mailers.{$emailType}");
        $fromConfig = config("mail.from_addresses.{$emailType}", config('mail.from'));

        // Log the configuration being used (without password)
        $logConfig = [
            'mailer' => $emailType,
            'host' => $mailerConfig['host'] ?? 'not set',
            'port' => $mailerConfig['port'] ?? 'not set',
            'username' => $mailerConfig['username'] ?? 'not set',
            'scheme' => $mailerConfig['scheme'] ?? 'smtps',
            'transport' => $mailerConfig['transport'] ?? 'smtp',
            'from_address' => $fromConfig['address'] ?? 'not set',
            'from_name' => $fromConfig['name'] ?? 'not set',
        ];
        \Illuminate\Support\Facades\Log::debug("Email configuration for type '{$emailType}':", $logConfig);

        // Build configuration array
        $config = [
            'mailer' => $emailType,
            'host' => $mailerConfig['host'],
            'port' => $mailerConfig['port'],
            'username' => $mailerConfig['username'],
            'password' => $mailerConfig['password'],
            'encryption' => $mailerConfig['encryption'] ?? null,
            'scheme' => $mailerConfig['scheme'] ?? 'smtps',
            'transport' => $mailerConfig['transport'] ?? 'smtp',
            'from_address' => $fromConfig['address'],
            'from_name' => $fromConfig['name'],
            'use_custom_settings' => true,
        ];

        return $config;
    }

    /**
     * Send an email with the configuration for a specific email type
     *
     * @param string $to The recipient email address
     * @param string $subject The email subject
     * @param string $content The email content
     * @param string $emailType The type of email (password_reset, notifications, confirmations, transactional, default)
     * @param string|null $locale The locale to use for the email (defaults to current app locale)
     * @return bool Whether the email was sent successfully
     */
    public function sendEmail(string $to, string $subject, string $content, string $emailType = 'default', ?string $locale = null): bool
    {
        try {
            // Set locale for this email if provided
            $originalLocale = app()->getLocale();
            if ($locale) {
                app()->setLocale($locale);
            }

            // Get email configuration
            $config = $this->getEmailConfig($emailType);

            // Set the mailer for this specific email
            config(['mail.default' => $config['mailer']]);

            // Send email
            Mail::raw($content, function (Message $message) use ($to, $subject, $config) {
                $message->to($to)
                    ->subject($subject);

                // Set from address and name explicitly in the message
                if (!empty($config['from_address'])) {
                    $message->from($config['from_address'], $config['from_name']);
                }
            });

            // Restore original locale
            if ($locale) {
                app()->setLocale($originalLocale);
            }

            return true;
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Failed to send email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send an email with HTML content
     *
     * @param string $to The recipient email address
     * @param string $subject The email subject
     * @param string $view The view to use for the email
     * @param array $data The data to pass to the view
     * @param string $emailType The type of email (password_reset, notifications, confirmations, transactional, default)
     * @param string|null $locale The locale to use for the email (defaults to current app locale)
     * @return bool Whether the email was sent successfully
     */
    public function sendEmailWithView(string $to, string $subject, string $view, array $data = [], string $emailType = 'default', ?string $locale = null): bool
    {
        try {
            \Illuminate\Support\Facades\Log::info("Attempting to send email to: {$to}, Subject: {$subject}, Template: {$view}, Type: {$emailType}");

            // Set locale for this email if provided
            $originalLocale = app()->getLocale();
            if ($locale) {
                app()->setLocale($locale);
                \Illuminate\Support\Facades\Log::debug("Email locale set to: {$locale}");
            }

            // Get email configuration
            $config = $this->getEmailConfig($emailType);

            // Set the mailer for this specific email
            config(['mail.default' => $config['mailer']]);
            \Illuminate\Support\Facades\Log::debug("Mail default mailer set to: {$config['mailer']}");

            // Add locale to data
            $data['locale'] = app()->getLocale();

            // Check if the view exists
            if (!\Illuminate\Support\Facades\View::exists($view)) {
                \Illuminate\Support\Facades\Log::error("Email template not found: {$view}");
                return false;
            }

            // Log template data (excluding sensitive information)
            $logData = array_filter($data, function($key) {
                return !in_array($key, ['password', 'token']);
            }, ARRAY_FILTER_USE_KEY);
            \Illuminate\Support\Facades\Log::debug("Email template data:", $logData);

            // Send email
            \Illuminate\Support\Facades\Log::debug("Sending email using Mail::send...");
            Mail::send($view, $data, function (Message $message) use ($to, $subject, $config) {
                $message->to($to)
                    ->subject($subject);

                // Set from address and name explicitly in the message
                if (!empty($config['from_address'])) {
                    $message->from($config['from_address'], $config['from_name']);
                    \Illuminate\Support\Facades\Log::debug("From address set to: {$config['from_address']}");
                }
            });

            // In Laravel 12, we don't have Mail::failures() anymore
            // We'll rely on exceptions for failure detection

            // Restore original locale
            if ($locale) {
                app()->setLocale($originalLocale);
            }

            \Illuminate\Support\Facades\Log::info("Email sent successfully to: {$to} using template: {$view}");
            return true;
        } catch (\Swift_TransportException $e) {
            // Handle SMTP authentication errors specifically
            \Illuminate\Support\Facades\Log::error('SMTP Authentication Error: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error('Email details: To: ' . $to . ', Subject: ' . $subject . ', View: ' . $view);

            // Log SMTP configuration (without password)
            $mailerType = $config['mailer'] ?? 'default';
            $mailerConfig = config("mail.mailers.{$mailerType}");
            \Illuminate\Support\Facades\Log::debug('SMTP Configuration:', [
                'host' => $mailerConfig['host'] ?? 'not set',
                'port' => $mailerConfig['port'] ?? 'not set',
                'username' => $mailerConfig['username'] ?? 'not set',
                'encryption' => $mailerConfig['encryption'] ?? 'not set',
                'scheme' => $mailerConfig['scheme'] ?? 'not set',
            ]);

            return false;
        } catch (\Symfony\Component\Mailer\Exception\TransportExceptionInterface $e) {
            // Handle Symfony Mailer transport exceptions (Laravel 12+)
            \Illuminate\Support\Facades\Log::error('Mail Transport Error: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error('Email details: To: ' . $to . ', Subject: ' . $subject . ', View: ' . $view);

            // Log SMTP configuration (without password)
            $mailerType = $config['mailer'] ?? 'default';
            $mailerConfig = config("mail.mailers.{$mailerType}");
            \Illuminate\Support\Facades\Log::debug('SMTP Configuration:', [
                'host' => $mailerConfig['host'] ?? 'not set',
                'port' => $mailerConfig['port'] ?? 'not set',
                'username' => $mailerConfig['username'] ?? 'not set',
                'encryption' => $mailerConfig['encryption'] ?? 'not set',
                'scheme' => $mailerConfig['scheme'] ?? 'not set',
            ]);

            return false;
        } catch (\Exception $e) {
            // Log the error with more details
            \Illuminate\Support\Facades\Log::error('Failed to send email with view: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error('Email details: To: ' . $to . ', Subject: ' . $subject . ', View: ' . $view);
            \Illuminate\Support\Facades\Log::error('Stack trace: ' . $e->getTraceAsString());

            // Log additional debug information
            \Illuminate\Support\Facades\Log::debug('Mail configuration:', [
                'default' => config('mail.default'),
                'from' => config('mail.from'),
                'mailers' => array_keys(config('mail.mailers')),
            ]);

            return false;
        }
    }
}
