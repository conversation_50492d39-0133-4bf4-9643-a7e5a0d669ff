<template>
  <li class="menu-item" :class="'level-' + level" :data-id="item.id" :data-level="level">
    <div class="menu-item-content d-flex align-items-center" :data-id="item.id">
      <!-- Vertical drag handle -->
      <i class="fas fa-grip-vertical drag-handle me-2" :data-id="item.id"></i>

      <!-- Nesting buttons with clear labels -->
      <div class="nesting-buttons me-3" :class="{ 'ms-2': level > 0 }">
        <button v-if="level < 2" class="btn btn-primary nest-in-btn" @click="$emit('nest-in', item.id)" title="Make this a child of the previous item">
          <i class="fas fa-level-down-alt fa-rotate-90 me-1"></i> Nest
        </button>
        <button v-if="level > 0" class="btn btn-secondary nest-out-btn" @click="$emit('nest-out', item.id)" title="Move this up one level">
          <i class="fas fa-level-up-alt fa-rotate-90 me-1"></i> Up
        </button>
      </div>

      <!-- Level indicator -->
      <div class="level-indicator" v-if="level > 0">
        <span class="badge bg-secondary me-2">Level {{ level }}</span>
      </div>

      <div class="flex-grow-1">
        <span class="menu-title">{{ item.title }}</span>
        <small v-if="item.title_en" class="text-muted ms-2">({{ item.title_en }})</small>
      </div>

      <div class="menu-actions">
        <button class="btn btn-sm btn-link" @click="$emit('add-child', item.id)" title="Add Child Item">
          <i class="fas fa-plus"></i>
        </button>
        <button class="btn btn-sm btn-link" @click="$emit('edit', item)" title="Edit Item">
          <i class="fas fa-edit"></i>
        </button>
        <button class="btn btn-sm btn-link text-danger" @click="$emit('delete', item)" title="Delete Item">
          <i class="fas fa-trash"></i>
        </button>
        <button class="btn btn-sm btn-link" @click="toggleExpanded" v-if="item.children && item.children.length > 0" title="Toggle Children">
          <i :class="expanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
        </button>
      </div>
    </div>

    <!-- Nested children -->
    <draggable
      v-if="item.children && item.children.length > 0 && expanded"
      v-model="item.children"
      group="menu-items"
      tag="ul"
      class="menu-tree list-unstyled"
      item-key="id"
      handle=".drag-handle"
      :animation="150"
      ghost-class="ghost-class"
      chosen-class="chosen-class"
      @end="$emit('drag-end', $event)"
    >
      <template #item="{ element }">
        <menu-item
          :item="element"
          :level="level + 1"
          @edit="$emit('edit', $event)"
          @delete="$emit('delete', $event)"
          @add-child="$emit('add-child', $event)"
          @nest-in="$emit('nest-in', $event)"
          @nest-out="$emit('nest-out', $event)"
          @drag-end="$emit('drag-end', $event)"
        />
      </template>
    </draggable>
  </li>
</template>

<script>
import { ref, computed } from 'vue';
import draggable from 'vuedraggable';

export default {
  name: 'MenuItem',
  components: {
    draggable
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    const expanded = ref(true);
    const hasChildren = computed(() => {
      // Ensure children array exists
      if (!props.item.children) {
        props.item.children = [];
      }
      return props.item.children.length > 0;
    });

    const toggleExpanded = () => {
      expanded.value = !expanded.value;
    };

    return {
      expanded,
      hasChildren,
      toggleExpanded
    };
  }
};
</script>

<style scoped>
.menu-item {
  margin-bottom: 5px;
  position: relative;
}

.menu-item-content {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  align-items: center;
  position: relative;
}

/* Add a subtle indicator that items can receive children */
.menu-item-content::after {
  content: "";
  position: absolute;
  right: 5px;
  bottom: 5px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(13, 110, 253, 0.3);
  transition: all 0.2s ease;
}

.menu-item-content:hover::after {
  background-color: rgba(13, 110, 253, 0.7);
  transform: scale(1.2);
}

.drag-handle {
  cursor: move;
  color: #6c757d;
  padding: 5px;
}

.drag-handle:hover {
  color: #343a40;
}

.nesting-buttons {
  display: flex;
  gap: 5px;
  margin-right: 10px;
}

.nest-in-btn, .nest-out-btn {
  padding: 3px 8px;
  font-size: 0.8rem;
  line-height: 1;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nest-in-btn {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.nest-out-btn {
  background-color: #6c757d;
  border-color: #6c757d;
}

.nest-in-btn:hover, .nest-out-btn:hover {
  transform: scale(1.1);
  transition: transform 0.2s;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.menu-actions {
  display: flex;
  gap: 5px;
}

.menu-tree {
  padding-left: 20px;
  margin-top: 5px;
}

/* Level styling */
.level-1 > .menu-item-content {
  border-left: 4px solid #0d6efd;
  margin-left: 15px;
}

.level-2 > .menu-item-content {
  border-left: 4px solid #6610f2;
  margin-left: 30px;
}

.level-3 > .menu-item-content {
  border-left: 4px solid #dc3545;
  margin-left: 45px;
}

/* Ghost and chosen classes for draggable */
.ghost-class {
  opacity: 0.5;
  background: #c8ebfb;
}

.chosen-class {
  background: #e9f5ff;
}

/* Horizontal nesting indicator */
.horizontal-nesting-indicator {
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 2px;
  background-color: #6c757d;
}

.level-indicator {
  margin-right: 5px;
}

/* Hover effects for drag handles */
.horizontal-drag-handle:hover {
  color: #0a58ca;
  transform: scale(1.2);
  transition: all 0.2s ease;
}

.drag-handle:hover {
  color: #0a58ca;
  transform: scale(1.2);
  transition: all 0.2s ease;
}
</style>

