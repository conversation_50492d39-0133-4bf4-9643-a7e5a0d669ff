<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\MenuItem;

class MenuController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Check if we have a main menu, create one if not
        $menu = Menu::where('location', 'main')->first();

        if (!$menu) {
            $menu = Menu::create([
                'name' => 'Main Menu',
                'location' => 'main',
                'is_active' => true,
            ]);
        }

        $menuItems = MenuItem::where('menu_id', $menu->id)
            ->whereNull('parent_id')
            ->orderBy('order', 'asc')
            ->get();

        return view('admin.menus.index', compact('menu', 'menuItems'));
    }

    /**
     * Display the Vue-based menu manager.
     *
     * @return \Illuminate\View\View
     */
    public function vueIndex()
    {
        return view('admin.menus.vue-index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $menu = Menu::where('location', 'main')->first();
        $menuItems = MenuItem::where('menu_id', $menu->id)
            ->whereNull('parent_id')
            ->orderBy('order', 'asc')
            ->get();

        return view('admin.menus.create', compact('menu', 'menuItems'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'menu_id' => 'required|exists:menus,id',
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'url' => 'nullable|string|max:255',
            'route_name' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'order' => 'nullable|integer',
        ]);

        $data = $request->all();

        // Set default order if not provided
        if (!isset($data['order'])) {
            if ($request->parent_id) {
                $maxOrder = MenuItem::where('parent_id', $request->parent_id)->max('order');
            } else {
                $maxOrder = MenuItem::where('menu_id', $request->menu_id)
                    ->whereNull('parent_id')
                    ->max('order');
            }
            $data['order'] = $maxOrder ? $maxOrder + 1 : 1;
        }

        // Set is_active to true by default
        $data['is_active'] = true;

        MenuItem::create($data);

        return redirect()->route('admin.website.menu')
            ->with('success', 'Menu item created successfully.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $menuItem = MenuItem::findOrFail($id);
        $menu = $menuItem->menu;
        $menuItems = MenuItem::where('menu_id', $menu->id)
            ->whereNull('parent_id')
            ->where('id', '!=', $id)
            ->orderBy('order', 'asc')
            ->get();

        return view('admin.menus.edit', compact('menuItem', 'menu', 'menuItems'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'url' => 'nullable|string|max:255',
            'route_name' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:menu_items,id',
            'order' => 'nullable|integer',
        ]);

        $menuItem = MenuItem::findOrFail($id);

        // Prevent circular reference
        if ($request->parent_id == $id) {
            return redirect()->back()
                ->with('error', 'A menu item cannot be its own parent.')
                ->withInput();
        }

        $menuItem->update($request->all());

        return redirect()->route('admin.website.menu')
            ->with('success', 'Menu item updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $menuItem = MenuItem::findOrFail($id);
        $menuItem->delete();

        return redirect()->route('admin.website.menu')
            ->with('success', 'Menu item deleted successfully.');
    }

    /**
     * Update the order of menu items.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateOrder(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:menu_items,id',
            'items.*.order' => 'required|integer',
            'items.*.parent_id' => 'nullable|exists:menu_items,id',
        ]);

        try {
            foreach ($request->items as $item) {
                // Prevent circular reference
                if (isset($item['parent_id']) && $item['parent_id'] == $item['id']) {
                    return response()->json([
                        'success' => false,
                        'message' => 'A menu item cannot be its own parent.'
                    ], 422);
                }

                MenuItem::where('id', $item['id'])->update([
                    'order' => $item['order'],
                    'parent_id' => $item['parent_id'] ?? null,
                ]);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Error updating menu order: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating menu order.'
            ], 500);
        }
    }
}
