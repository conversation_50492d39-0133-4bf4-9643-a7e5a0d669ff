<!-- Latest News -->
<div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">{{ app()->getLocale() == 'id' ? 'Berita Terbaru' : 'Latest News' }}</h5>
    </div>
    <div class="card-body">
        @forelse($latestNews as $news)
            <div class="related-item mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                <div class="row g-0">
                    <div class="col-4">
                        @if($news->thumbnail)
                            <img src="{{ asset('storage/' . $news->thumbnail) }}" class="img-fluid rounded" alt="{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}">
                        @else
                            <img src="{{ asset('images/news-placeholder.jpg') }}" class="img-fluid rounded" alt="{{ app()->getLocale() == 'id' ? $news->title : $news->title_en }}">
                        @endif
                    </div>
                    <div class="col-8 ps-3">
                        <div class="small text-muted mb-1">{{ $news->published_at->format('d M Y') }}</div>
                        <h6 class="mb-0"><a href="{{ route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en) }}" class="text-decoration-none">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($news->title, 50) : \Illuminate\Support\Str::limit($news->title_en, 50) }}</a></h6>
                    </div>
                </div>
            </div>
        @empty
            <p class="mb-0">{{ app()->getLocale() == 'id' ? 'Tidak ada berita terbaru.' : 'No latest news.' }}</p>
        @endforelse
    </div>
</div>
