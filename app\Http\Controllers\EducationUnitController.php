<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\EducationUnit;
use App\Models\Curriculum;

class EducationUnitController extends Controller
{
    /**
     * Display a listing of the education units.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Get active tab from request or default to 'formal'
        $activeTab = $request->query('tab', 'formal');

        // Get formal education units
        $formalUnits = EducationUnit::active()
            ->formal()
            ->get();

        // Get non-formal education units
        $nonFormalUnits = EducationUnit::active()
            ->nonFormal()
            ->get();

        return view('public.education_units.index', compact('formalUnits', 'nonFormalUnits', 'activeTab'));
    }

    /**
     * Display the specified education unit.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Find the education unit by ID
        $unit = EducationUnit::active()->findOrFail($id);

        // Get related education units of the same type (formal or non-formal)
        $relatedUnits = EducationUnit::active()
            ->where('id', '!=', $unit->id)
            ->where('edu_type', $unit->edu_type)
            ->limit(3)
            ->get();

        // Get curricula related to this education unit
        $curricula = Curriculum::active()
            ->where('education_unit_id', $unit->id)
            ->orWhere(function($query) use ($unit) {
                $query->whereNull('education_unit_id')
                      ->where('edu_type', $unit->edu_type);
            })
            ->get();

        return view('public.education_units.show', compact('unit', 'relatedUnits', 'curricula'));
    }
}
