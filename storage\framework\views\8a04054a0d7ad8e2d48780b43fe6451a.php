<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Agenda' : 'Agenda'); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Agenda' : 'Agenda'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Agenda' : 'Agenda'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Agenda List -->
    <section class="section-padding">
        <div class="container">
            <div class="row justify-content-center">
                <?php $__empty_1 = true; $__currentLoopData = $agendaItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agenda): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-lg-6 mb-4 fade-in">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex">
                                    <div class="agenda-date">
                                        <div class="day"><?php echo e($agenda->date->format('d')); ?></div>
                                        <div class="month"><?php echo e($agenda->date->format('M')); ?></div>
                                    </div>
                                    <div class="agenda-content">
                                        <h4 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $agenda->title : $agenda->title_en); ?></h4>
                                        <div class="agenda-info mb-3">
                                            <p><i class="far fa-clock me-1"></i> <?php echo e($agenda->time->format('H:i')); ?></p>
                                            <p><i class="fas fa-map-marker-alt me-1"></i> <?php echo e(app()->getLocale() == 'id' ? $agenda->location : $agenda->location_en); ?></p>
                                            <?php if($agenda->organizer): ?>
                                                <p><i class="fas fa-user me-1"></i> <?php echo e(app()->getLocale() == 'id' ? $agenda->organizer : $agenda->organizer_en); ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <p class="card-text"><?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($agenda->description), 150) : \Illuminate\Support\Str::limit(strip_tags($agenda->description_en), 150)); ?></p>
                                        <a href="<?php echo e(route('agenda.show', $agenda->id)); ?>" class="btn btn-sm btn-outline-success"><?php echo e(app()->getLocale() == 'id' ? 'Detail Agenda' : 'Agenda Details'); ?></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <div class="alert alert-info">
                            <?php echo e(app()->getLocale() == 'id' ? 'Belum ada agenda yang tersedia.' : 'No agenda available yet.'); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($agendaItems->links()); ?>

            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/agenda/index.blade.php ENDPATH**/ ?>