@extends('admin.layouts.app')

@section('title', 'Videos')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Videos</h1>
        <a href="{{ route('admin.videos.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Video
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Thumbnail</th>
                            <th>Title</th>
                            <th>YouTube URL</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($videos as $video)
                            <tr>
                                <td>
                                    @if($video->thumbnail)
                                        <img src="{{ asset('storage/' . $video->thumbnail) }}" alt="{{ $video->title }}" width="50" height="50" class="img-thumbnail">
                                    @else
                                        <div class="ratio ratio-16x9" style="width: 80px; height: 45px;">
                                            <iframe src="{{ $video->youtube_embed_url }}" title="{{ $video->title }}" allowfullscreen></iframe>
                                        </div>
                                    @endif
                                </td>
                                <td>{{ $video->title }}</td>
                                <td>
                                    <a href="{{ $video->youtube_url }}" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                                        {{ $video->youtube_url }}
                                    </a>
                                </td>
                                <td>{{ $video->order }}</td>
                                <td>
                                    @if($video->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.videos.show', $video) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.videos.edit', $video) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.videos.destroy', $video) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger btn-delete" onclick="return confirm('Are you sure you want to delete this video?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">No videos found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $videos->links() }}
            </div>
        </div>
    </div>
@endsection
