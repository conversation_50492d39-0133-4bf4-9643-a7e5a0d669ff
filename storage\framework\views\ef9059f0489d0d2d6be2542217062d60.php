<?php $__env->startSection('title', 'View Message'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Message</h1>
        <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Message Details</h5>
                    <div>
                        <span class="badge <?php echo e($contact->is_read ? 'bg-success' : 'bg-warning'); ?> fs-6">
                            <?php echo e($contact->is_read ? 'Read' : 'Unread'); ?>

                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Subject:</div>
                        <div class="col-md-9"><?php echo e($contact->subject); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Date:</div>
                        <div class="col-md-9"><?php echo e($contact->created_at->format('d M Y H:i:s')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">From:</div>
                        <div class="col-md-9"><?php echo e($contact->name); ?> (<?php echo e($contact->email); ?>)</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Phone:</div>
                        <div class="col-md-9"><?php echo e($contact->phone ?: 'Not provided'); ?></div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Message:</div>
                        <div class="col-md-9">
                            <div class="p-3 bg-light rounded">
                                <?php echo nl2br(e($contact->message)); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if($contact->hasReply()): ?>
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        Your Reply

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Date:</div>
                        <div class="col-md-9"><?php echo e($contact->replied_at->format('d M Y H:i:s')); ?></div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">By:</div>
                        <div class="col-md-9">
                            <?php echo e($contact->repliedBy ? $contact->repliedBy->name : 'System'); ?>

                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3 fw-bold">Reply:</div>
                        <div class="col-md-9">
                            <div class="p-3 bg-light rounded">
                                <?php echo nl2br(e($contact->reply_message)); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Reply to Message</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.contacts.reply', $contact)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="mb-3">
                            <label for="reply_message" class="form-label">Your Reply</label>
                            <textarea class="form-control <?php $__errorArgs = ['reply_message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="reply_message" name="reply_message" rows="6" required><?php echo e(old('reply_message')); ?></textarea>
                            <?php $__errorArgs = ['reply_message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane me-1"></i> Send Reply
                        </button>
                    </form>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <a href="mailto:<?php echo e($contact->email); ?>" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-reply me-1"></i> Reply via Email
                    </a>

                    <?php if(!$contact->is_read): ?>
                        <form action="<?php echo e(route('admin.contacts.mark-as-read', $contact)); ?>" method="POST" class="mb-2">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-check me-1"></i> Mark as Read
                            </button>
                        </form>
                    <?php endif; ?>

                    <form action="<?php echo e(route('admin.contacts.destroy', $contact)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to delete this message? This action cannot be undone.')">
                            <i class="fas fa-trash me-1"></i> Delete Message
                        </button>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="bg-light rounded-circle p-2">
                                <i class="fas fa-user text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <div class="small text-muted">Name</div>
                            <div><?php echo e($contact->name); ?></div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="bg-light rounded-circle p-2">
                                <i class="fas fa-envelope text-primary"></i>
                            </div>
                        </div>
                        <div>
                            <div class="small text-muted">Email</div>
                            <div><?php echo e($contact->email); ?></div>
                        </div>
                    </div>

                    <?php if($contact->phone): ?>
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-light rounded-circle p-2">
                                    <i class="fas fa-phone text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-muted">Phone</div>
                                <div><?php echo e($contact->phone); ?></div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/admin/contacts/show.blade.php ENDPATH**/ ?>