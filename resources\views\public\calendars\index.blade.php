@extends('public.layouts.app')

@section('title', app()->getLocale() == 'id' ? '<PERSON><PERSON><PERSON> Akademi<PERSON>' : 'Academic Calendar')

@push('styles')
<style>
    /* Calendar Card Styles */
    .calendar-card {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
        border-left: 5px solid #198754;
    }

    .calendar-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .calendar-card.exam {
        border-left-color: #dc3545;
    }

    .calendar-card.holiday {
        border-left-color: #0d6efd;
    }

    .calendar-card.semester {
        border-left-color: #ffc107;
    }

    .calendar-card.event {
        border-left-color: #6f42c1;
    }

    .calendar-date {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .calendar-date-icon {
        width: 50px;
        height: 50px;
        background-color: #f8f9fa;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-right: 15px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .calendar-date-icon .day {
        font-size: 18px;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 2px;
    }

    .calendar-date-icon .month {
        font-size: 12px;
        text-transform: uppercase;
        line-height: 1;
    }

    .calendar-date-range {
        font-size: 14px;
        color: #6c757d;
    }

    .calendar-type {
        display: inline-block;
        padding: 3px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 10px;
        background-color: #e9ecef;
    }

    .calendar-type.exam {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .calendar-type.holiday {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }

    .calendar-type.semester {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .calendar-type.event {
        background-color: rgba(111, 66, 193, 0.1);
        color: #6f42c1;
    }

    .month-divider {
        position: relative;
        text-align: center;
        margin: 40px 0 30px;
    }

    .month-divider:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #dee2e6;
        z-index: 1;
    }

    .month-divider span {
        position: relative;
        z-index: 2;
        background-color: #fff;
        padding: 0 20px;
        font-size: 18px;
        font-weight: 600;
        color: #198754;
    }

    .filter-buttons {
        margin-bottom: 30px;
    }

    .filter-buttons .btn {
        margin-right: 10px;
        margin-bottom: 10px;
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 14px;
        font-weight: 500;
    }

    .filter-buttons .btn.active {
        background-color: #198754;
        color: white;
    }

    .calendar-location {
        margin-top: 10px;
        font-size: 14px;
        color: #6c757d;
    }

    .calendar-location i {
        margin-right: 5px;
    }

    /* Animation for calendar cards */
    .calendar-card {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    }

    .calendar-card.active {
        opacity: 1;
        transform: translateY(0);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .month-divider span {
            font-size: 16px;
        }
    }
</style>
@endpush

@section('content')
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3">{{ app()->getLocale() == 'id' ? 'Kalender Akademik' : 'Academic Calendar' }}</h1>
                    <p>{{ app()->getLocale() == 'id' ? 'Jadwal kegiatan akademik di ' . \App\Helpers\SettingHelper::getInstitutionName() : 'Academic activities schedule at ' . \App\Helpers\SettingHelper::getInstitutionNameEn() }}</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Calendar List -->
    <section class="section-padding">
        <div class="container">
            <!-- Filter Buttons -->
            <div class="filter-buttons text-center">
                <button class="btn btn-outline-success active" data-filter="all">{{ app()->getLocale() == 'id' ? 'Semua' : 'All' }}</button>
                @foreach($calendarTypes as $type)
                    <button class="btn btn-outline-success" data-filter="{{ strtolower($type) }}">{{ $type }}</button>
                @endforeach
            </div>

            @if($calendarsByMonth->isEmpty())
                <div class="text-center">
                    <div class="alert alert-info">
                        {{ app()->getLocale() == 'id' ? 'Belum ada kalender akademik yang tersedia.' : 'No academic calendar available yet.' }}
                    </div>
                </div>
            @else
                @foreach($calendarsByMonth as $yearMonth => $monthCalendars)
                    <div class="month-divider">
                        <span>{{ \Carbon\Carbon::createFromFormat('Y-m', $yearMonth)->translatedFormat('F Y') }}</span>
                    </div>
                    <div class="row justify-content-center">
                        @foreach($monthCalendars as $calendar)
                            <div class="col-lg-6 mb-4 calendar-item" data-type="{{ strtolower($calendar->type ?? 'other') }}">
                                <div class="calendar-card {{ strtolower($calendar->type ?? 'other') }}">
                                    <div class="card-body p-4">
                                        <div class="calendar-date">
                                            <div class="calendar-date-icon">
                                                <div class="day">{{ $calendar->start_date->format('d') }}</div>
                                                <div class="month">{{ $calendar->start_date->format('M') }}</div>
                                            </div>
                                            <div class="calendar-date-range">
                                                <i class="far fa-calendar-alt me-1"></i>
                                                {{ $calendar->start_date->format('d M Y') }}
                                                @if($calendar->start_date->format('Y-m-d') != $calendar->end_date->format('Y-m-d'))
                                                    - {{ $calendar->end_date->format('d M Y') }}
                                                @endif
                                            </div>
                                        </div>

                                        @if($calendar->type)
                                            <div class="calendar-type {{ strtolower($calendar->type) }}">
                                                {{ $calendar->type }}
                                            </div>
                                        @endif

                                        <h4 class="card-title">{{ app()->getLocale() == 'id' ? $calendar->title : $calendar->title_en }}</h4>

                                        @if($calendar->description || $calendar->description_en)
                                            <p class="card-text">{{ app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($calendar->description), 150) : \Illuminate\Support\Str::limit(strip_tags($calendar->description_en), 150) }}</p>
                                        @endif

                                        @if($calendar->location || $calendar->location_en)
                                            <div class="calendar-location">
                                                <i class="fas fa-map-marker-alt"></i>
                                                {{ app()->getLocale() == 'id' ? $calendar->location : $calendar->location_en }}
                                            </div>
                                        @endif

                                        <a href="{{ route('calendars.show', $calendar->id) }}" class="btn btn-sm btn-outline-success mt-3">
                                            {{ app()->getLocale() == 'id' ? 'Detail Kalender' : 'Calendar Details' }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endforeach
            @endif
        </div>
    </section>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize animations for calendar cards
        const calendarCards = document.querySelectorAll('.calendar-card');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, {
            threshold: 0.1
        });

        calendarCards.forEach(card => {
            observer.observe(card);
        });

        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-buttons .btn');
        const calendarItems = document.querySelectorAll('.calendar-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                // Get filter value
                const filterValue = this.getAttribute('data-filter');

                // Show/hide calendar items based on filter
                calendarItems.forEach(item => {
                    if (filterValue === 'all' || item.getAttribute('data-type') === filterValue) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
@endpush
