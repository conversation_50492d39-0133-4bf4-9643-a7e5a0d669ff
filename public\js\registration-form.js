/**
 * Registration Form JavaScript
 *
 * This file contains all the JavaScript functionality for the registration form.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const registerBtn = document.getElementById('register-now-btn');
    const registrationFormContainers = document.querySelectorAll('.registration-form-container');
    const registrationInfoContainer = document.querySelector('.registration-info-container');
    const targetSection = document.getElementById('registration-form');
    const termsCheckbox = document.getElementById('terms_accepted');
    const termsDialog = document.getElementById('terms-dialog');

    // Initialize form visibility
    initializeFormVisibility();

    // Initialize form field validations
    initializeFormValidations();

    // Initialize phone input
    initializePhoneInput();

    /**
     * Initialize form visibility and register button click handler
     */
    function initializeFormVisibility() {
        // Disable AOS on all form containers to prevent conflicts
        registrationFormContainers.forEach(container => {
            container.removeAttribute('data-aos');
        });

        // Initially hide the form containers
        registrationFormContainers.forEach(container => {
            container.style.display = 'none';
        });

        // Add click event to the register button
        if (registerBtn) {
            registerBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Hide registration info section with fade effect
                if (registrationInfoContainer) {
                    registrationInfoContainer.classList.add('hide');
                }

                // Show all registration form containers with fade effect
                if (registrationFormContainers.length > 0) {
                    // Delay to allow the info container to fade out first
                    setTimeout(function() {
                        registrationFormContainers.forEach(container => {
                            container.style.display = 'block';
                            setTimeout(() => {
                                container.classList.add('show');
                            }, 50);
                        });

                        // Scroll to the form section after a slight delay
                        if (targetSection) {
                            setTimeout(function() {
                                targetSection.scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }, 300);
                        }
                    }, 600);
                }
            });
        }
    }

    /**
     * Initialize form field validations
     */
    function initializeFormValidations() {
        // Handle numeric-only inputs
        const numericInputs = document.querySelectorAll('.numeric-only');
        numericInputs.forEach(input => {
            // Remove any non-numeric characters on input
            input.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');

                // Special handling for NIK field - exactly 16 digits
                if (this.id === 'nik' && this.value.length > 16) {
                    this.value = this.value.substring(0, 16);
                }
            });

            // Prevent non-numeric key presses
            input.addEventListener('keypress', function(e) {
                const key = e.which || e.keyCode;
                const keyChar = String.fromCharCode(key);

                // If not a number, prevent default
                if (!/[0-9]/.test(keyChar)) {
                    e.preventDefault();
                }
            });
        });

        // Handle name fields (convert to uppercase)
        const nameFields = document.querySelectorAll('.name-field');
        nameFields.forEach(input => {
            // Convert to uppercase on input
            input.addEventListener('input', function(e) {
                this.value = this.value.toUpperCase();
            });

            // Convert to uppercase on blur (in case it was pasted)
            input.addEventListener('blur', function(e) {
                this.value = this.value.toUpperCase();
            });
        });

        // Handle location fields (auto-capitalize)
        const locationFields = document.querySelectorAll('.location-field');
        locationFields.forEach(input => {
            // Capitalize on input
            input.addEventListener('input', function(e) {
                this.value = this.value.toUpperCase();
            });

            // Capitalize on blur (in case it was pasted)
            input.addEventListener('blur', function(e) {
                this.value = this.value.toUpperCase();
            });
        });

        // Handle hobby/ambition fields (title case)
        const titleCaseFields = document.querySelectorAll('.capitalize-words');
        titleCaseFields.forEach(input => {
            // Capitalize first letter of each word on input
            input.addEventListener('input', function(e) {
                this.value = this.value.replace(/\w\S*/g, function(txt) {
                    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                });
            });

            // Capitalize first letter of each word on blur (in case it was pasted)
            input.addEventListener('blur', function(e) {
                this.value = this.value.replace(/\w\S*/g, function(txt) {
                    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                });
            });
        });

        // Handle terms and conditions dialog
        if (termsCheckbox && termsDialog) {
            // Always show dialog on first checkbox click
            termsCheckbox.addEventListener('click', function(e) {
                // If this is the first click and the checkbox is being checked
                if (this.checked && !this.dataset.clicked) {
                    // Prevent the default checkbox behavior
                    e.preventDefault();

                    // Show the terms dialog with animation
                    termsDialog.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // Prevent scrolling behind dialog

                    // Mark the checkbox as having been clicked
                    this.dataset.clicked = 'true';
                }
            });

            // Close dialog and check the checkbox when user accepts
            const acceptTermsBtn = document.getElementById('accept-terms');
            if (acceptTermsBtn) {
                acceptTermsBtn.addEventListener('click', function() {
                    termsDialog.style.display = 'none';
                    document.body.style.overflow = ''; // Restore scrolling
                    termsCheckbox.checked = true;
                });
            }

            // Close dialog and uncheck the checkbox when user declines
            const declineTermsBtn = document.getElementById('decline-terms');
            if (declineTermsBtn) {
                declineTermsBtn.addEventListener('click', function() {
                    termsDialog.style.display = 'none';
                    document.body.style.overflow = ''; // Restore scrolling
                    termsCheckbox.checked = false;
                });
            }

            // Close dialog when clicking outside the content
            termsDialog.addEventListener('click', function(e) {
                if (e.target === termsDialog) {
                    termsDialog.style.display = 'none';
                    document.body.style.overflow = ''; // Restore scrolling
                    termsCheckbox.checked = false;
                }
            });

            // Close dialog with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && termsDialog.style.display === 'block') {
                    termsDialog.style.display = 'none';
                    document.body.style.overflow = ''; // Restore scrolling
                    termsCheckbox.checked = false;
                }
            });
        }

        // Form validation and submission handling
        const form = document.querySelector('.registration-form');
        if (form) {
            // Store original button text
            const submitBtn = form.querySelector('button[type="submit"]');
            let originalButtonText = '';
            if (submitBtn) {
                originalButtonText = submitBtn.innerHTML;
            }

            // Add event listener for form submission
            form.addEventListener('submit', function(event) {
                // Prevent default form submission
                event.preventDefault();

                // Validate form
                if (!form.checkValidity()) {
                    form.classList.add('was-validated');
                    return;
                }

                form.classList.add('was-validated');

                // Ensure CSRF token is fresh before submission
                const token = document.head.querySelector('meta[name="csrf-token"]');
                const csrfInput = form.querySelector('input[name="_token"]');
                if (token && csrfInput) {
                    csrfInput.value = token.content;
                }

                // Disable the submit button to prevent double submission
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' +
                        (document.documentElement.lang === 'id' ? 'Mengirim...' : 'Submitting...');
                }

                // Create FormData object from the form
                const formData = new FormData(form);

                // Send AJAX request
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Handle successful response
                    if (data.success) {
                        // Redirect to success page
                        window.location.href = data.redirect;
                    } else {
                        // Show error message
                        const statusDiv = document.getElementById('form-submission-status');
                        if (statusDiv) {
                            statusDiv.style.display = 'block';
                            statusDiv.innerHTML = '<div class="alert alert-danger">' +
                                (data.message || (document.documentElement.lang === 'id' ?
                                'Terjadi kesalahan saat memproses pendaftaran. Silakan coba lagi.' :
                                'An error occurred while processing your registration. Please try again.')) +
                                '</div>';
                        }

                        // Reset button
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = originalButtonText;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message
                    const statusDiv = document.getElementById('form-submission-status');
                    if (statusDiv) {
                        statusDiv.style.display = 'block';
                        statusDiv.innerHTML = '<div class="alert alert-danger">' +
                            (document.documentElement.lang === 'id' ?
                            'Terjadi kesalahan saat mengirim pendaftaran. Silakan coba lagi atau hubungi kami jika masalah berlanjut.' :
                            'An error occurred while submitting your registration. Please try again or contact us if the problem persists.') +
                            '</div>';
                    }

                    // Reset button
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalButtonText;
                    }
                });
            });

            // Add event listener for form errors
            window.addEventListener('load', function() {
                // Check if there are any error messages from a previous submission
                const errorMessage = document.querySelector('.alert-danger');
                if (errorMessage && submitBtn) {
                    // Reset button state
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalButtonText;
                }
            });
        }
    }

    /**
     * Initialize international telephone input
     */
    function initializePhoneInput() {
        const phoneInput = document.getElementById('phone');
        const phoneFullInput = document.getElementById('phone_full');

        if (phoneInput) {
            const iti = window.intlTelInput(phoneInput, {
                initialCountry: "id",
                preferredCountries: ["id", "my", "sg", "au"],
                separateDialCode: true,
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/utils.js",
            });

            // Format the phone number on blur
            phoneInput.addEventListener('blur', function() {
                if (phoneInput.value) {
                    // Remove leading zeros as they're replaced by country code
                    if (phoneInput.value.startsWith('0')) {
                        phoneInput.value = phoneInput.value.substring(1);
                    }

                    // Set the full number with country code
                    if (phoneFullInput) {
                        phoneFullInput.value = iti.getNumber();
                    }
                }
            });

            // Handle form submission
            const form = document.querySelector('.registration-form');
            if (form) {
                form.addEventListener('submit', function(event) {
                    // Validate phone number
                    if (phoneInput.value) {
                        if (!iti.isValidNumber()) {
                            event.preventDefault();
                            event.stopPropagation();

                            const errorMsg = document.querySelector('.phone-error');
                            if (errorMsg) {
                                const lang = document.documentElement.lang || 'en';
                                errorMsg.textContent = lang === 'id'
                                    ? "Nomor telepon tidak valid. Pastikan format sudah benar."
                                    : "Invalid phone number. Please check the format.";
                            }

                            phoneInput.classList.add('is-invalid');
                        } else {
                            // Set the full number with country code
                            if (phoneFullInput) {
                                phoneFullInput.value = iti.getNumber();
                            }
                        }
                    }
                });
            }
        }
    }
});
