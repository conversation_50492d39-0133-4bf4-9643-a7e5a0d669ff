import './bootstrap';
import { createApp } from 'vue';
import MenuItem from './components/MenuItem.vue';
import MenuManager from './components/MenuManager.vue';

document.addEventListener('DOMContentLoaded', () => {
    const menuManagerApp = document.getElementById('menu-manager-app');

    if (menuManagerApp) {
        console.log('Found menu-manager-app element, mounting Vue app...');

        // Create a root component that includes MenuManager
        const app = createApp({
            components: {
                'menu-manager': MenuManager
            },
            template: '<menu-manager></menu-manager>'
        });

        // Register the MenuItem component globally
        app.component('menu-item', MenuItem);

        // Mount the app
        app.mount('#menu-manager-app');

        console.log('Vue app mounted to #menu-manager-app');
    } else {
        console.error('Element with ID "menu-manager-app" not found');
    }
});
