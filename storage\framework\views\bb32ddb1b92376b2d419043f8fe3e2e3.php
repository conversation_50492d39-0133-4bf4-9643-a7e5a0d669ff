<?php $__env->startSection('title', app()->getLocale() == 'id' ? 'Profil' : 'Profile'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .motto-content {
        font-weight: 500;
        font-size: 1.3rem;
        color: #198754;
        display: inline;
    }

    .motto-text i {
        color: #198754;
        font-size: 1.3rem;
        vertical-align: middle;
        flex-shrink: 0;
    }

    .text-success.d-flex {
        flex-wrap: nowrap;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="fw-bold mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Profil Pondok Pesantren' : 'Islamic Boarding School Profile'); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center mb-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white"><?php echo e(app()->getLocale() == 'id' ? 'Beranda' : 'Home'); ?></a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(app()->getLocale() == 'id' ? 'Profil' : 'Profile'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </section>

    <!-- Motto Section -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title text-center" data-aos="fade-up">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Motto' : 'Motto'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Semangat dan nilai-nilai Pondok Pesantren Nurul Hayah 4' : 'Spirit and values of Nurul Hayah 4 Islamic Boarding School'); ?></p>
            </div>

            <?php if($motto): ?>
            <div class="row justify-content-center">
                <div class="col-lg-8 mb-5" data-aos="fade-up">
                    <div class="card border-0 shadow-sm text-center motto-card">
                        <div class="card-body p-5">

                            <div class="card-text motto-text mt-2">
                                <div class="text-success">
                                    <i class="fas fa-quote-left me-2"></i>
                                    <span class="motto-content"><?php echo app()->getLocale() == 'id' ? $motto->content : $motto->content_en; ?></span>
                                    <i class="fas fa-quote-right ms-2"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Vision & Mission Section -->
    <section class="section-padding bg-light">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Visi & Misi' : 'Vision & Mission'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Tujuan dan arah pengembangan Pondok Pesantren Nurul Hayah 4' : 'Goals and development direction of Nurul Hayah 4 Islamic Boarding School'); ?></p>
            </div>

            <div class="row">
                <?php if($vision): ?>
                <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="card-title text-success mb-3"><?php echo e(app()->getLocale() == 'id' ? $vision->title : $vision->title_en); ?></h3>
                            <div class="card-text">
                                <?php echo app()->getLocale() == 'id' ? $vision->content : $vision->content_en; ?>

                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($mission): ?>
                <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="card-title text-success mb-3"><?php echo e(app()->getLocale() == 'id' ? $mission->title : $mission->title_en); ?></h3>
                            <div class="card-text">
                                <?php echo app()->getLocale() == 'id' ? $mission->content : $mission->content_en; ?>

                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="text-center mt-4">
                <a href="<?php echo e(route('profile.vision-mission')); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Selengkapnya' : 'Read More'); ?></a>
            </div>
        </div>
    </section>

    <!-- History Section -->
    <section class="section-padding">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Sejarah Singkat' : 'Brief History'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Perjalanan Pondok Pesantren Nurul Hayah 4 sejak didirikan' : 'The journey of Nurul Hayah 4 Islamic Boarding School since its establishment'); ?></p>
            </div>

            <div class="row align-items-center">
                <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right">
                    <?php if($history && $history->image): ?>
                        <img src="<?php echo e(asset('storage/' . $history->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $history->title : $history->title_en); ?>" class="img-fluid rounded shadow-sm">
                    <?php else: ?>
                        <img src="<?php echo e(asset('images/history.jpg')); ?>" alt="History" class="img-fluid rounded shadow-sm">
                    <?php endif; ?>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <?php if($history): ?>
                    <h3 class="text-success mb-3"><?php echo e(app()->getLocale() == 'id' ? $history->title : $history->title_en); ?></h3>
                    <div class="mb-4">
                        <?php echo app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit(strip_tags($history->content), 500) : \Illuminate\Support\Str::limit(strip_tags($history->content_en), 500); ?>

                    </div>
                    <a href="<?php echo e(route('profile.history')); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More'); ?></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /home/<USER>/laravel/resources/views/public/profile/index.blade.php ENDPATH**/ ?>