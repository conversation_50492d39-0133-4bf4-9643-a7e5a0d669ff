@extends('admin.layouts.app')

@section('title', 'Edit News')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit News</h1>
        <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ route('admin.news.update', $news) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="title" class="form-label required">Title (Indonesian)</label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $news->title) }}" data-translate-from="title_en" required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="title_en" class="form-label required">Title (English)</label>
                        <input type="text" class="form-control @error('title_en') is-invalid @enderror" id="title_en" name="title_en" value="{{ old('title_en', $news->title_en) }}" required>
                        @error('title_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Will be automatically translated from Indonesian, but can be edited manually.</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="slug" class="form-label">Slug (Indonesian)</label>
                        <input type="text" class="form-control @error('slug') is-invalid @enderror" id="slug" name="slug" value="{{ old('slug', $news->slug) }}" required>
                        <small class="text-muted">The slug is used in the URL and should be unique.</small>
                        @error('slug')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="slug_en" class="form-label">Slug (English)</label>
                        <input type="text" class="form-control @error('slug_en') is-invalid @enderror" id="slug_en" name="slug_en" value="{{ old('slug_en', $news->slug_en) }}" required>
                        <small class="text-muted">Will be automatically generated from the English title. The slug is used in the URL and should be unique.</small>
                        @error('slug_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="thumbnail" class="form-label">Thumbnail</label>
                        @if($news->thumbnail)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $news->thumbnail) }}" alt="{{ $news->title }}" class="img-thumbnail" style="max-height: 150px;">
                            </div>
                        @endif
                        <input type="file" class="form-control @error('thumbnail') is-invalid @enderror" id="thumbnail" name="thumbnail" accept="image/*">
                        <small class="text-muted">Leave empty to keep the current thumbnail. Recommended size: 400x300 pixels. Max size: 2MB.</small>
                        @error('thumbnail')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="image" class="form-label">Main Image</label>
                        @if($news->image)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $news->image) }}" alt="{{ $news->title }}" class="img-thumbnail" style="max-height: 150px;">
                            </div>
                        @endif
                        <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                        <small class="text-muted">Leave empty to keep the current image. Recommended size: 800x600 pixels. Max size: 2MB.</small>
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="category_id" class="form-label required">Category</label>
                        <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                            <option value="">-- Select Category --</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id', $news->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('category_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="published_at" class="form-label required">Published Date</label>
                        <input type="date" class="form-control @error('published_at') is-invalid @enderror" id="published_at" name="published_at" value="{{ old('published_at', $news->published_at->format('Y-m-d')) }}" required>
                        @error('published_at')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1" {{ $news->is_published ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_published">
                                Publish immediately
                            </label>
                            <small class="form-text text-muted d-block">Uncheck this if you want to save as draft</small>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="content" class="form-label required">Content (Indonesian)</label>
                        <textarea class="form-control summernote @error('content') is-invalid @enderror" id="content" name="content" rows="10" data-translate-from="content_en" required>{{ old('content', $news->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label for="content_en" class="form-label required">Content (English)</label>
                        <textarea class="form-control summernote @error('content_en') is-invalid @enderror" id="content_en" name="content_en" rows="10" required>{{ old('content_en', $news->content_en) }}</textarea>
                        @error('content_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">Will be automatically translated from Indonesian, but can be edited manually.</small>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Auto-generate slug from title
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
        document.getElementById('slug').value = slug;
    });

    // Auto-generate slug from English title
    document.getElementById('title_en').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
        document.getElementById('slug_en').value = slug;
    });

    document.addEventListener('DOMContentLoaded', function() {

        // Add event listener for title translation to update slug
        document.addEventListener('input', function(e) {
            // If this is the Indonesian title field
            if (e.target && e.target.id === 'title') {
                // Get the title value
                const title = e.target.value;

                // Only proceed if we have at least 2 characters
                if (title && title.length > 2) {
                    // Get CSRF token
                    const token = document.querySelector('meta[name="csrf-token"]').content;

                    // Call the translation API
                    fetch('{{ route('admin.translate') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': token,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            text: title,
                            source_lang: 'id',
                            target_lang: 'en'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.translated_text) {
                            // Update the English title field
                            const titleEnField = document.getElementById('title_en');
                            if (titleEnField) {
                                titleEnField.value = data.translated_text;

                                // Also update the English slug
                                const slugEnField = document.getElementById('slug_en');
                                if (slugEnField) {
                                    const slug = data.translated_text.toLowerCase()
                                        .replace(/[^\w\s-]/g, '')
                                        .replace(/[\s_-]+/g, '-')
                                        .replace(/^-+|-+$/g, '');
                                    slugEnField.value = slug;
                                }

                                // Trigger change event
                                titleEnField.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Translation error:', error);
                    });
                }
            }
        });
    });
</script>
@endpush
