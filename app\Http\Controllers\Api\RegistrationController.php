<?php

namespace App\Http\Controllers\Api;

use App\Models\Registration;
use App\Services\WebhookService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RegistrationsExport;

class RegistrationController extends BaseApiController
{
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\WebhookService  $webhookService
     * @return void
     */
    public function __construct(WebhookService $webhookService)
    {
        parent::__construct($webhookService);
        $this->model = new Registration();
        $this->modelName = 'registration';
        
        $this->storeRules = [
            'nik' => 'required|string|max:20',
            'full_name' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'place_of_birth' => 'required|string|max:255',
            'date_of_birth' => 'required|date',
            'parent_name' => 'required|string|max:255',
            'parent_occupation' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'father_occupation' => 'required|string|max:255',
            'mother_name' => 'required|string|max:255',
            'mother_occupation' => 'required|string|max:255',
            'address' => 'required|string',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'nisn' => 'nullable|string|max:20',
            'last_education' => 'required|string|max:255',
            'previous_school' => 'required|string|max:255',
            'graduation_year' => 'required|numeric|digits:4',
            'education_unit_id' => 'required|exists:education_units,id',
            'reason' => 'required|string',
            'hobby' => 'required|string|max:255',
            'ambition' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ];
        
        $this->updateRules = [
            'nik' => 'string|max:20',
            'full_name' => 'string|max:255',
            'gender' => 'in:male,female',
            'place_of_birth' => 'string|max:255',
            'date_of_birth' => 'date',
            'parent_name' => 'string|max:255',
            'parent_occupation' => 'string|max:255',
            'father_name' => 'string|max:255',
            'father_occupation' => 'string|max:255',
            'mother_name' => 'string|max:255',
            'mother_occupation' => 'string|max:255',
            'address' => 'string',
            'phone' => 'string|max:20',
            'email' => 'nullable|email|max:255',
            'nisn' => 'nullable|string|max:20',
            'last_education' => 'string|max:255',
            'previous_school' => 'string|max:255',
            'graduation_year' => 'numeric|digits:4',
            'education_unit_id' => 'exists:education_units,id',
            'reason' => 'string',
            'hobby' => 'string|max:255',
            'ambition' => 'string|max:255',
            'notes' => 'nullable|string',
            'status' => 'in:pending,approved,rejected',
        ];
    }

    /**
     * Display a listing of the registrations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = $this->model->query();

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('education_unit_id')) {
            $query->where('education_unit_id', $request->education_unit_id);
        }

        // Apply search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        // Apply pagination
        $perPage = $request->input('per_page', 15);
        $registrations = $query->with('educationUnit')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $registrations,
        ]);
    }

    /**
     * Approve a registration.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve($id)
    {
        $registration = $this->model->findOrFail($id);
        
        if ($registration->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Registration is not in pending status',
            ], 422);
        }
        
        $registration->update([
            'status' => 'approved',
            'approved_at' => now(),
        ]);
        
        // Dispatch webhook event
        $this->webhookService->dispatchEvent('registration.approved', $registration->toArray());
        
        return response()->json([
            'success' => true,
            'message' => 'Registration approved successfully',
            'data' => $registration,
        ]);
    }
    
    /**
     * Reject a registration.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reject(Request $request, $id)
    {
        $registration = $this->model->findOrFail($id);
        
        if ($registration->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Registration is not in pending status',
            ], 422);
        }
        
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $registration->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'rejection_reason' => $request->rejection_reason,
        ]);
        
        // Dispatch webhook event
        $this->webhookService->dispatchEvent('registration.rejected', $registration->toArray());
        
        return response()->json([
            'success' => true,
            'message' => 'Registration rejected successfully',
            'data' => $registration,
        ]);
    }
    
    /**
     * Export registrations to Excel.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $fileName = 'registrations_' . date('Y-m-d_His') . '.xlsx';
        
        return Excel::download(new RegistrationsExport($request), $fileName);
    }
}
