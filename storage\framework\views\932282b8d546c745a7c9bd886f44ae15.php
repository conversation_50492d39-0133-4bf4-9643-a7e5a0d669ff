<?php if (isset($component)) { $__componentOriginalaa758e6a82983efcbf593f765e026bd9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa758e6a82983efcbf593f765e026bd9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::message'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
# <?php echo e(__('emails.islamic_greeting')); ?>


<?php echo e(__('emails.registration_thank_you')); ?>


<div style="background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3 style="color: #0056b3; margin-top: 0;"><?php echo e(__('emails.registration_details')); ?></h3>
    <table style="width: 100%; border-collapse: collapse; border: none;">
        <tr>
            <td style="padding: 8px; width: 40%; border-bottom: 1px solid #dee2e6;"><strong><?php echo e(__('emails.registration_name')); ?>:</strong></td>
            <td style="padding: 8px; border-bottom: 1px solid #dee2e6;"><?php echo e($name); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px; width: 40%; border-bottom: 1px solid #dee2e6;"><strong><?php echo e(__('emails.registration_email')); ?>:</strong></td>
            <td style="padding: 8px; border-bottom: 1px solid #dee2e6;"><?php echo e($email); ?></td>
        </tr>
        <tr>
            <td style="padding: 8px; width: 40%;"><strong><?php echo e(__('emails.registration_number')); ?>:</strong></td>
            <td style="padding: 8px; font-weight: bold; color: #0056b3;"><?php echo e($registration_number); ?></td>
        </tr>
    </table>
</div>

<div style="background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; border-radius: 4px;">
    <strong><?php echo e(__('emails.registration_save')); ?></strong>
</div>

<div style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6c757d; margin: 15px 0;">
    <strong><?php echo e(__('emails.auto_email_notice')); ?></strong>
</div>

<p><?php echo e(__('emails.islamic_closing')); ?></p>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaa758e6a82983efcbf593f765e026bd9)): ?>
<?php $attributes = $__attributesOriginalaa758e6a82983efcbf593f765e026bd9; ?>
<?php unset($__attributesOriginalaa758e6a82983efcbf593f765e026bd9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaa758e6a82983efcbf593f765e026bd9)): ?>
<?php $component = $__componentOriginalaa758e6a82983efcbf593f765e026bd9; ?>
<?php unset($__componentOriginalaa758e6a82983efcbf593f765e026bd9); ?>
<?php endif; ?>
<?php /**PATH /home/<USER>/laravel/resources/views/emails/registration.blade.php ENDPATH**/ ?>