<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Video extends Model
{
    protected $fillable = [
        'title',
        'title_en',
        'description',
        'description_en',
        'youtube_url',
        'thumbnail',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active videos.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->orderBy('order', 'asc');
    }

    /**
     * Get the YouTube video ID from the URL.
     *
     * @return string|null
     */
    public function getYoutubeIdAttribute()
    {
        if (preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->youtube_url, $match)) {
            return $match[1];
        }
        
        return null;
    }

    /**
     * Get the YouTube embed URL.
     *
     * @return string|null
     */
    public function getYoutubeEmbedUrlAttribute()
    {
        if ($this->youtube_id) {
            return 'https://www.youtube.com/embed/' . $this->youtube_id;
        }
        
        return null;
    }
}
