<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            // Check if the columns don't exist before adding them
            if (!Schema::hasColumn('programs', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('image');
            }
            
            if (!Schema::hasColumn('programs', 'order')) {
                $table->integer('order')->default(0)->after('is_featured');
            }
            
            if (!Schema::hasColumn('programs', 'icon')) {
                $table->string('icon')->nullable()->after('image');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('programs', function (Blueprint $table) {
            // Drop the columns if they exist
            if (Schema::hasColumn('programs', 'is_featured')) {
                $table->dropColumn('is_featured');
            }
            
            if (Schema::hasColumn('programs', 'order')) {
                $table->dropColumn('order');
            }
            
            if (Schema::hasColumn('programs', 'icon')) {
                $table->dropColumn('icon');
            }
        });
    }
};
