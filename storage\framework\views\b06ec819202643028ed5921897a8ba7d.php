<!-- Latest News -->
<div class="card border-0 shadow-sm mb-4" data-aos="fade-up">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Berita Terbaru' : 'Latest News'); ?></h5>
    </div>
    <div class="card-body">
        <?php $__empty_1 = true; $__currentLoopData = $latestNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="related-item mb-3 pb-3 <?php echo e(!$loop->last ? 'border-bottom' : ''); ?>">
                <div class="row g-0">
                    <div class="col-4">
                        <?php if($news->thumbnail): ?>
                            <img src="<?php echo e(asset('storage/' . $news->thumbnail)); ?>" class="img-fluid rounded" alt="<?php echo e(app()->getLocale() == 'id' ? $news->title : $news->title_en); ?>">
                        <?php else: ?>
                            <img src="<?php echo e(asset('images/news-placeholder.jpg')); ?>" class="img-fluid rounded" alt="<?php echo e(app()->getLocale() == 'id' ? $news->title : $news->title_en); ?>">
                        <?php endif; ?>
                    </div>
                    <div class="col-8 ps-3">
                        <div class="small text-muted mb-1"><?php echo e($news->published_at->format('d M Y')); ?></div>
                        <h6 class="mb-0"><a href="<?php echo e(route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en)); ?>" class="text-decoration-none"><?php echo e(app()->getLocale() == 'id' ? \Illuminate\Support\Str::limit($news->title, 50) : \Illuminate\Support\Str::limit($news->title_en, 50)); ?></a></h6>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p class="mb-0"><?php echo e(app()->getLocale() == 'id' ? 'Tidak ada berita terbaru.' : 'No latest news.'); ?></p>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH /home/<USER>/laravel/resources/views/public/partials/latest-news.blade.php ENDPATH**/ ?>