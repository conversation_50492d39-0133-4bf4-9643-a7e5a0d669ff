<?php $__env->startSection('title', $websiteName ?? (app()->getLocale() == 'id' ? 'Beranda' : 'Home')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Hero Section with Swiper -->
    <section class="hero-section">
        <div class="swiper hero-slider">
            <div class="swiper-wrapper">
                <?php if($swiperImages->isNotEmpty()): ?>
                    <?php $__currentLoopData = $swiperImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $swiper): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="swiper-slide">
                            <img src="<?php echo e(asset('storage/' . $swiper->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $swiper->title : $swiper->title_en); ?>">
                            <div class="container">
                                <div class="hero-content fade-in">
                                    <h1><?php echo e(app()->getLocale() == 'id' ? $swiper->title : $swiper->title_en); ?></h1>
                                    <?php if($swiper->description): ?>
                                        <div class="swiper-description">
                                            <?php echo app()->getLocale() == 'id' ? $swiper->description : $swiper->description_en; ?>

                                        </div>
                                    <?php endif; ?>
                                    <?php if($swiper->button_url): ?>
                                        <div class="mt-4">
                                            <a href="<?php echo e($swiper->button_url); ?>" class="btn btn-success">
                                                <?php echo e(app()->getLocale() == 'id' ? ($swiper->button_text ?? 'Selengkapnya') : ($swiper->button_text_en ?? 'Learn More')); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <!-- Fallback if no swiper images -->
                    <div class="swiper-slide">
                        <img src="<?php echo e(asset('images/hero-bg.jpg')); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? \App\Helpers\SettingHelper::getInstitutionName() : \App\Helpers\SettingHelper::getInstitutionNameEn()); ?>">
                        <div class="container">
                            <div class="hero-content fade-in">
                                <h1><?php echo e(app()->getLocale() == 'id' ? \App\Helpers\SettingHelper::getInstitutionName() : \App\Helpers\SettingHelper::getInstitutionNameEn()); ?></h1>
                                <p><?php echo e(app()->getLocale() == 'id' ? 'Membentuk Generasi Muslim yang Berakhlak Mulia, Berwawasan Luas, dan Berdaya Saing Global' : 'Shaping a Generation of Muslims with Noble Character, Broad Insight, and Global Competitiveness'); ?></p>
                                <div class="mt-4">
                                    <a href="<?php echo e(route('registration')); ?>" class="btn btn-success me-2"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></a>
                                    <a href="<?php echo e(route('profile')); ?>" class="btn btn-outline-light"><?php echo e(app()->getLocale() == 'id' ? 'Pelajari Lebih Lanjut' : 'Learn More'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <div class="swiper-pagination"></div>
            <div class="swiper-button-next"></div>
            <div class="swiper-button-prev"></div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="section-padding bg-light scroll-section">
        <div class="container">
            <div class="section-title fade-in">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Mengapa Memilih Kami?' : 'Why Choose Us?'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Pondok Pesantren Nurul Hayah 4 menawarkan pendidikan berkualitas dengan fasilitas modern dan lingkungan yang kondusif.' : 'Nurul Hayah 4 Islamic Boarding School offers quality education with modern facilities and a conducive environment.'); ?></p>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="feature-box bg-white shadow-sm fade-in-left">
                        <div class="feature-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h3 class="feature-title"><?php echo e(app()->getLocale() == 'id' ? 'Kurikulum Terintegrasi' : 'Integrated Curriculum'); ?></h3>
                        <p><?php echo e(app()->getLocale() == 'id' ? 'Memadukan kurikulum nasional dengan pendidikan Islam yang komprehensif.' : 'Combining the national curriculum with comprehensive Islamic education.'); ?></p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="feature-box bg-white shadow-sm fade-in">
                        <div class="feature-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h3 class="feature-title"><?php echo e(app()->getLocale() == 'id' ? 'Pengajar Berkualitas' : 'Qualified Teachers'); ?></h3>
                        <p><?php echo e(app()->getLocale() == 'id' ? 'Didukung oleh tenaga pengajar yang berpengalaman dan berkompeten di bidangnya.' : 'Supported by experienced and competent teaching staff in their fields.'); ?></p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="feature-box bg-white shadow-sm fade-in-right">
                        <div class="feature-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h3 class="feature-title"><?php echo e(app()->getLocale() == 'id' ? 'Fasilitas Modern' : 'Modern Facilities'); ?></h3>
                        <p><?php echo e(app()->getLocale() == 'id' ? 'Dilengkapi dengan fasilitas modern untuk mendukung kegiatan belajar mengajar.' : 'Equipped with modern facilities to support teaching and learning activities.'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Director's Insight Section -->
    <div class="directors-insight-section scroll-section">
        <div class="gold-bg-left"></div>
        <div class="container position-relative">
            <div class="director-image-container">
                <?php if($director && $director->image): ?>
                    <img src="<?php echo e(asset('storage/' . $director->image)); ?>" alt="<?php echo e($director->name); ?>" class="img-fluid">
                    <div class="director-name-overlay">
                        <?php echo e(app()->getLocale() == 'id' ? $director->name : $director->name_en); ?>

                    </div>
                <?php else: ?>
                    <img src="<?php echo e(asset('images/director-placeholder.jpg')); ?>" alt="Director" class="img-fluid">
                    <div class="director-name-overlay">
                        Abi M. Fakri Islami Arif, C.Ht., M.Pd.
                    </div>
                <?php endif; ?>
            </div>

            <div class="directors-insight-content">
                <div class="directors-insight-header">
                    <h2>DIRECTOR's INSIGHT</h2>
                </div>

                <?php if($featuredInsight): ?>
                    <div class="insight-card">
                        <div class="insight-card-content">
                            <div class="insight-thumbnail">
                                <?php if($featuredInsight->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $featuredInsight->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $featuredInsight->title : $featuredInsight->title_en); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('images/insight-placeholder.jpg')); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $featuredInsight->title : $featuredInsight->title_en); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="insight-text">
                                <h4><?php echo e(app()->getLocale() == 'id' ? $featuredInsight->title : $featuredInsight->title_en); ?></h4>
                                <div class="insight-date"><?php echo e($featuredInsight->published_at ? $featuredInsight->published_at->format('d M Y') : ''); ?></div>
                                <div class="insight-content">
                                    <?php
                                        $content = app()->getLocale() == 'id' ? $featuredInsight->content : $featuredInsight->content_en;
                                        $plainText = strip_tags($content);
                                        // Hapus karakter HTML entities seperti &nbsp;
                                        $plainText = html_entity_decode($plainText);
                                        // Hapus whitespace berlebih
                                        $plainText = preg_replace('/\s+/', ' ', $plainText);
                                        $plainText = trim($plainText);
                                        $excerpt = \Illuminate\Support\Str::limit($plainText, 200);
                                    ?>
                                    <?php echo e($excerpt); ?>

                                </div>
                                <div class="insight-read-more">
                                    <a href="<?php echo e(route('insights.show', $featuredInsight->id)); ?>"><?php echo e(app()->getLocale() == 'id' ? 'Selengkapnya >>' : 'Read more >>'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="insight-card">
                        <div class="insight-card-content">
                            <div class="text-center p-4">
                                <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada insight yang tersedia.' : 'No insights available yet.'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Programs Section -->
    <section class="section-padding scroll-section program-carousel-section">
        <div class="section-title fade-in">
            <h2><?php echo e(app()->getLocale() == 'id' ? 'Program Unggulan' : 'Featured Programs'); ?></h2>
            <p><?php echo e(app()->getLocale() == 'id' ? 'Program-program unggulan yang kami tawarkan untuk mengembangkan potensi santri.' : 'Featured programs we offer to develop students\' potential.'); ?></p>
        </div>

        <?php if($featuredPrograms->isNotEmpty()): ?>
            <div class="program-carousel fade-in">
                <div class="owl-carousel owl-theme">
                    <?php $__currentLoopData = $featuredPrograms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="item">
                            <div class="program-card card h-100 zoom-in">
                                <?php if($program->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $program->image)); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $program->name : $program->name_en); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('images/program-placeholder.jpg')); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $program->name : $program->name_en); ?>">
                                <?php endif; ?>
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $program->name : $program->name_en); ?></h5>
                                    <div class="card-text">
                                        <?php
                                            $description = app()->getLocale() == 'id' ? $program->description : $program->description_en;
                                            $plainText = strip_tags($description);
                                            // Hapus karakter HTML entities seperti &nbsp;
                                            $plainText = html_entity_decode($plainText);
                                            // Hapus whitespace berlebih
                                            $plainText = preg_replace('/\s+/', ' ', $plainText);
                                            $plainText = trim($plainText);
                                            $excerpt = \Illuminate\Support\Str::limit($plainText, 100);
                                        ?>
                                        <?php echo e($excerpt); ?>

                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php else: ?>
            <div class="container">
                <div class="text-center py-5 fade-in">
                    <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada program yang tersedia.' : 'No programs available yet.'); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <div class="container">
            <div class="text-center mt-4 fade-in">
                <a href="<?php echo e(route('programs')); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Lihat Semua Program' : 'View All Programs'); ?></a>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-success text-white scroll-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 fade-in-left">
                    <h2 class="mb-3"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang untuk Tahun Ajaran Baru' : 'Register Now for the New Academic Year'); ?></h2>
                    <p class="mb-4"><?php echo e(app()->getLocale() == 'id' ? 'Jadilah bagian dari keluarga besar Pondok Pesantren Nurul Hayah 4 dan raih masa depan yang cerah.' : 'Be a part of the Nurul Hayah 4 Islamic Boarding School family and achieve a bright future.'); ?></p>
                </div>
                <div class="col-lg-4 text-lg-end fade-in-right">
                    <a href="<?php echo e(route('registration')); ?>" class="btn btn-light btn-lg"><?php echo e(app()->getLocale() == 'id' ? 'Daftar Sekarang' : 'Register Now'); ?></a>
                </div>
            </div>
        </div>
    </section>

    <!-- Information Center Section -->
    <section class="section-padding bg-light scroll-section">
        <div class="container">
            <div class="section-title info-center-title fade-in">
                <img src="<?php echo e(asset('storage/' . \App\Models\Setting::getValue('logo', 'images/logo.png'))); ?>" alt="<?php echo e(\App\Helpers\SettingHelper::getInstitutionName()); ?>" class="info-center-logo" onerror="this.src='<?php echo e(asset('images/logo.png')); ?>'">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Pusat Informasi' : 'Information Center'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Informasi terbaru tentang Berita, Pengumuman, dan Agenda NUHA 4.' : 'Latest information about News, Announcements, and Agenda of NUHA 4.'); ?></p>
            </div>

            <div class="row">
                <div class="col-12 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="elementor-widget-wrap elementor-element-populated newsroom-label fade-in-left">
                            <h3>Newsroom</h3>
                        </div>
                        <a href="<?php echo e(route('news')); ?>" class="btn btn-gold fade-in-right">
                            <?php echo e(app()->getLocale() == 'id' ? 'Lainnya' : 'Other'); ?>

                        </a>
                    </div>
                </div>
                <?php $__empty_1 = true; $__currentLoopData = $latestNews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $news): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-md-4">
                        <div class="news-card card h-100 fade-in" style="transition-delay: <?php echo e($loop->iteration * 0.1); ?>s">
                            <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($news->thumbnail, 'news')); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $news->title : $news->title_en); ?>">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="news-date">
                                        <i class="far fa-calendar-alt me-1"></i> <?php echo e($news->published_at ? $news->published_at->format('d M Y') : date('d M Y')); ?>

                                    </div>
                                    <?php if($news->category): ?>
                                        <div class="news-category">
                                            <span class="badge bg-success">
                                                <?php echo e(app()->getLocale() == 'id' ? $news->category->name : $news->category->name_en); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <h5 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $news->title : $news->title_en); ?></h5>
                                <div class="card-text">
                                    <?php
                                        $content = app()->getLocale() == 'id' ? $news->content : $news->content_en;
                                        $plainText = strip_tags($content, '<br>');
                                        // Hapus karakter HTML entities seperti &nbsp;
                                        $plainText = html_entity_decode($plainText);
                                        // Hapus whitespace berlebih
                                        $plainText = preg_replace('/\s+/', ' ', $plainText);
                                        $plainText = trim($plainText);
                                        $excerpt = \Illuminate\Support\Str::limit($plainText, 100);
                                    ?>
                                    <?php echo e($excerpt); ?>

                                </div>
                                <a href="<?php echo e(route('news.show', app()->getLocale() == 'id' ? $news->slug : $news->slug_en)); ?>" class="btn btn-sm btn-outline-success"><?php echo e(app()->getLocale() == 'id' ? 'Baca Selengkapnya' : 'Read More'); ?></a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada berita yang tersedia.' : 'No news available yet.'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Announcements and Agenda Row -->
            <div class="row mt-5">
                <!-- Announcements Column -->
                <div class="col-md-6 mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="elementor-widget-wrap elementor-element-populated announcements-label fade-in-left">
                            <h3><?php echo e(app()->getLocale() == 'id' ? 'Pengumuman' : 'Announcements'); ?></h3>
                        </div>
                        <a href="<?php echo e(route('announcements')); ?>" class="btn btn-gold fade-in-right">
                            <?php echo e(app()->getLocale() == 'id' ? 'Lainnya' : 'Others'); ?>

                        </a>
                    </div>

                    <div class="card shadow-sm fade-in">
                        <div class="card-body p-3">
                            <?php $__empty_1 = true; $__currentLoopData = $latestAnnouncements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $announcement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="announcement-item">
                                    <div class="w-100">
                                        <div class="announcement-date">
                                            <i class="far fa-calendar-alt me-1"></i>
                                            <?php echo e($announcement->start_date->format('d M Y')); ?> - <?php echo e($announcement->end_date->format('d M Y')); ?>

                                        </div>
                                        <a href="<?php echo e(route('announcements.show', $announcement->id)); ?>" class="announcement-title">
                                            <?php echo e(app()->getLocale() == 'id' ? $announcement->title : $announcement->title_en); ?>

                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="text-center py-3">
                                    <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada pengumuman yang tersedia.' : 'No announcements available yet.'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Agenda Column -->
                <div class="col-md-6 mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="elementor-widget-wrap elementor-element-populated agenda-label fade-in-left">
                            <h3><?php echo e(app()->getLocale() == 'id' ? 'Agenda' : 'Agenda'); ?></h3>
                        </div>
                        <a href="<?php echo e(route('agenda')); ?>" class="btn btn-gold fade-in-right">
                            <?php echo e(app()->getLocale() == 'id' ? 'Lainnya' : 'Others'); ?>

                        </a>
                    </div>

                    <div class="card shadow-sm fade-in">
                        <div class="card-body p-3">
                            <?php $__empty_1 = true; $__currentLoopData = $upcomingAgenda; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agenda): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="agenda-item">
                                    <div class="agenda-date">
                                        <div class="day"><?php echo e($agenda->date->format('d')); ?></div>
                                        <div class="month"><?php echo e($agenda->date->format('M')); ?></div>
                                    </div>
                                    <div class="agenda-content">
                                        <h5><?php echo e(app()->getLocale() == 'id' ? $agenda->title : $agenda->title_en); ?></h5>
                                        <div class="agenda-info">
                                            <p><i class="far fa-clock"></i> <?php echo e($agenda->time->format('H:i')); ?></p>
                                            <p><i class="fas fa-map-marker-alt"></i> <?php echo e(app()->getLocale() == 'id' ? $agenda->location : $agenda->location_en); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <div class="text-center py-3">
                                    <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada agenda yang tersedia.' : 'No agenda available yet.'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="section-padding scroll-section">
        <div class="container">
            <div class="section-title fade-in">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Galeri Kegiatan' : 'Activity Gallery'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Dokumentasi kegiatan dan aktivitas di Pondok Pesantren Nurul Hayah 4.' : 'Documentation of activities at Nurul Hayah 4 Islamic Boarding School.'); ?></p>
            </div>

            <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $galleryImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-md-4 col-sm-6">
                        <div class="gallery-item zoom-in" style="transition-delay: <?php echo e($loop->iteration * 0.1); ?>s">
                            <div class="gallery-image-container">
                                <?php if($gallery->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $gallery->image)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $gallery->title : $gallery->title_en); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('images/gallery-placeholder.jpg')); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $gallery->title : $gallery->title_en); ?>">
                                <?php endif; ?>
                                <div class="gallery-overlay">
                                    <a href="<?php echo e(asset('storage/' . $gallery->image)); ?>" class="gallery-link">
                                        <i class="fas fa-search-plus"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada galeri yang tersedia.' : 'No gallery available yet.'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center mt-4 fade-in">
                <a href="<?php echo e(route('gallery')); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Lihat Semua Galeri' : 'View All Gallery'); ?></a>
            </div>
        </div>
    </section>

    <!-- Facilities Section -->
    <section class="section-padding bg-light scroll-section">
        <div class="container">
            <div class="section-title fade-in">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Fasilitas Kami' : 'Our Facilities'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'Fasilitas modern yang mendukung kegiatan belajar mengajar di Pondok Pesantren Nurul Hayah 4.' : 'Modern facilities that support teaching and learning activities at Nurul Hayah 4 Islamic Boarding School.'); ?></p>
            </div>

            <div class="row justify-content-center">
                <?php $__empty_1 = true; $__currentLoopData = $facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-md-4 col-sm-6 mb-4">
                        <div class="card h-100 fade-in" style="transition-delay: <?php echo e($loop->iteration * 0.1); ?>s">
                            <?php if($facility->image): ?>
                                <img src="<?php echo e(asset('storage/' . $facility->image)); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $facility->name : $facility->name_en); ?>">
                            <?php else: ?>
                                <img src="<?php echo e(asset('images/facility-placeholder.jpg')); ?>" class="card-img-top" alt="<?php echo e(app()->getLocale() == 'id' ? $facility->name : $facility->name_en); ?>">
                            <?php endif; ?>
                            <div class="card-body">
                                <h5 class="card-title"><?php echo e(app()->getLocale() == 'id' ? $facility->name : $facility->name_en); ?></h5>
                                <div class="card-text">
                                    <?php
                                        $description = app()->getLocale() == 'id' ? $facility->description : $facility->description_en;
                                        $plainText = strip_tags($description);
                                        // Hapus karakter HTML entities seperti &nbsp;
                                        $plainText = html_entity_decode($plainText);
                                        // Hapus whitespace berlebih
                                        $plainText = preg_replace('/\s+/', ' ', $plainText);
                                        $plainText = trim($plainText);
                                        $excerpt = \Illuminate\Support\Str::limit($plainText, 100);
                                    ?>
                                    <?php echo e($excerpt); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12 text-center">
                        <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada fasilitas yang tersedia.' : 'No facilities available yet.'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="text-center mt-4 fade-in">
                <a href="<?php echo e(route('facilities')); ?>" class="btn btn-success"><?php echo e(app()->getLocale() == 'id' ? 'Lihat Semua Fasilitas' : 'View All Facilities'); ?></a>
            </div>
        </div>
    </section>

    <!-- Partnership Section -->
    <section class="section-padding bg-success text-white scroll-section">
        <div class="container">
            <div class="section-title fade-in text-center">
                <h2><?php echo e(app()->getLocale() == 'id' ? 'Kemitraan Kami' : 'Our Partnership'); ?></h2>
                <p><?php echo e(app()->getLocale() == 'id' ? 'NURUL HAYAH 4 telah menjalin kerja sama dengan berbagai lembaga dan institusi pendidikan, baik dari dalam negeri maupun luar negeri. Berikut adalah daftar beberapa lembaga dan institusi yang telah bekerja sama dengan NURUL HAYAH 4.' : 'NURUL HAYAH 4 has established partnerships with various educational institutions, both nationally and internationally. The following is a list of institutions and organizations that collaborate with NURUL HAYAH 4.'); ?></p>
            </div>

            <?php if($partnerships->isNotEmpty()): ?>
                <div class="partner-carousel fade-in">
                    <div class="owl-carousel owl-theme">
                        <?php $__currentLoopData = $partnerships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partnership): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="item text-center">
                                <div class="partner-logo mx-2">
                                    <?php if($partnership->logo): ?>
                                        <img src="<?php echo e(asset('storage/' . $partnership->logo)); ?>" alt="<?php echo e(app()->getLocale() == 'id' ? $partnership->name : $partnership->name_en); ?>" class="img-fluid">
                                    <?php else: ?>
                                        <div class="p-4">
                                            <h5 class="text-white"><?php echo e(app()->getLocale() == 'id' ? $partnership->name : $partnership->name_en); ?></h5>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-5 fade-in">
                    <p><?php echo e(app()->getLocale() == 'id' ? 'Belum ada data kemitraan yang tersedia.' : 'No partnership data available yet.'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    $(document).ready(function(){
        // Gallery lightbox for home page
        const galleryLinks = document.querySelectorAll('.gallery-item .gallery-link');
        if (galleryLinks.length > 0) {
            galleryLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('href');
                    const lightbox = document.createElement('div');
                    lightbox.classList.add('lightbox');

                    lightbox.innerHTML = `
                        <div class="lightbox-content">
                            <img src="${imgSrc}" alt="Gallery Image">
                            <span class="lightbox-close">&times;</span>
                        </div>
                    `;

                    document.body.appendChild(lightbox);

                    // Prevent scrolling when lightbox is open
                    document.body.style.overflow = 'hidden';

                    // Close lightbox when clicking on the close button or outside the image
                    lightbox.addEventListener('click', function(e) {
                        if (e.target === this || e.target.classList.contains('lightbox-close')) {
                            document.body.removeChild(lightbox);
                            document.body.style.overflow = 'auto';
                        }
                    });
                });
            });
        }

        // Initialize the hero slider
        if (document.querySelector('.hero-slider')) {
            const heroSwiper = new Swiper('.hero-slider', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                speed: 800, // Slower transition for smoother fade
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                // Ensure slides are properly rendered before transition
                preloadImages: true,
                lazy: false,
                watchSlidesProgress: true,
                watchSlidesVisibility: true,
                on: {
                    // Make sure content is visible on slide change
                    slideChangeTransitionStart: function() {
                        $('.swiper-slide-active .hero-content').hide();
                        $('.swiper-slide-active .hero-content').removeClass('active');
                    },
                    slideChangeTransitionEnd: function() {
                        $('.swiper-slide-active .hero-content').show();
                        setTimeout(function() {
                            $('.swiper-slide-active .hero-content').addClass('active');
                        }, 50);
                    }
                }
            });

            // Force update on page load to ensure first slide is visible
            setTimeout(function() {
                heroSwiper.update();
            }, 500);
        }

        // Initialize the program carousel
        $('.program-carousel .owl-carousel').owlCarousel({
            loop: true,
            margin: 20,
            nav: false,
            dots: true,
            autoplay: true,
            autoplayTimeout: 4000,
            autoplayHoverPause: true,
            stagePadding: 0,
            responsive: {
                0: {
                    items: 1,
                    margin: 15,
                    stagePadding: 0
                },
                576: {
                    items: 2,
                    margin: 15
                },
                768: {
                    items: 3,
                    margin: 20
                },
                992: {
                    items: 5,
                    margin: 20
                }
            }
        });

        // Initialize the partnership carousel
        $('.partner-carousel .owl-carousel').owlCarousel({
            loop: true,
            margin: 20,
            nav: false,
            dots: false,
            autoplay: true,
            autoplayTimeout: 3000,
            autoplayHoverPause: true,
            responsive: {
                0: {
                    items: 1
                },
                576: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 4
                }
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('public.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\IT\www\nurul-hayah-4\resources\views/public/home.blade.php ENDPATH**/ ?>