<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test 2</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Google Maps Test 2 (Using JavaScript API)</h1>
    
    <div id="map"></div>
    
    <script>
        function initMap() {
            // The location of Nurul Hayah 4
            const nurulHayah = { lat: -7.030394468879357, lng: 108.62930117587732 };
            // The map, centered at Nurul Hayah 4
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 15,
                center: nurulHayah,
            });
            // The marker, positioned at Nurul Hayah 4
            const marker = new google.maps.Marker({
                position: nurulHayah,
                map: map,
                title: "NURUL HAYAH 4"
            });
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?callback=initMap&v=weekly" defer></script>
</body>
</html>
