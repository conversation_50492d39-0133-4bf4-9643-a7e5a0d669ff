@extends('admin.layouts.app')

@section('title', 'View Page')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">View Page</h1>
        <div>
            <a href="{{ route('admin.pages.edit', $page) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="{{ route('admin.pages.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Pages
            </a>
            <a href="{{ route('pages.show', $page->slug) }}" class="btn btn-success" target="_blank">
                <i class="fas fa-external-link-alt"></i> View on Site
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="languageTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="indonesian-tab" data-bs-toggle="tab" data-bs-target="#indonesian" type="button" role="tab" aria-controls="indonesian" aria-selected="true">Indonesian</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="english-tab" data-bs-toggle="tab" data-bs-target="#english" type="button" role="tab" aria-controls="english" aria-selected="false">English</button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="languageTabsContent">
                <!-- Indonesian Tab -->
                <div class="tab-pane fade show active" id="indonesian" role="tabpanel" aria-labelledby="indonesian-tab">
                    <h2 class="mb-3">{{ $page->title }}</h2>
                    
                    @if($page->image)
                        <div class="mb-4 text-center">
                            <img src="{{ asset('storage/' . $page->image) }}" alt="{{ $page->title }}" class="img-fluid rounded" style="max-height: 400px;">
                        </div>
                    @endif
                    
                    <div class="content-display">
                        {!! $page->content !!}
                    </div>
                </div>

                <!-- English Tab -->
                <div class="tab-pane fade" id="english" role="tabpanel" aria-labelledby="english-tab">
                    <h2 class="mb-3">{{ $page->title_en ?: $page->title }}</h2>
                    
                    @if($page->image)
                        <div class="mb-4 text-center">
                            <img src="{{ asset('storage/' . $page->image) }}" alt="{{ $page->title_en ?: $page->title }}" class="img-fluid rounded" style="max-height: 400px;">
                        </div>
                    @endif
                    
                    <div class="content-display">
                        {!! $page->content_en ?: '<div class="alert alert-info">No English content available.</div>' !!}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i> Page Details
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 150px;">Slug</th>
                            <td>{{ $page->slug }}</td>
                        </tr>
                        <tr>
                            <th>Order</th>
                            <td>{{ $page->order }}</td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                @if($page->is_active)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-danger">Inactive</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>Created At</th>
                            <td>{{ $page->created_at->format('d M Y, H:i') }}</td>
                        </tr>
                        <tr>
                            <th>Last Updated</th>
                            <td>{{ $page->updated_at->format('d M Y, H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-search me-1"></i> SEO Information
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th style="width: 150px;">Meta Title</th>
                            <td>{{ $page->meta_title ?: 'Not set' }}</td>
                        </tr>
                        <tr>
                            <th>Meta Description</th>
                            <td>{{ $page->meta_description ?: 'Not set' }}</td>
                        </tr>
                        <tr>
                            <th>URL</th>
                            <td>
                                <a href="{{ route('pages.show', $page->slug) }}" target="_blank">
                                    {{ route('pages.show', $page->slug) }}
                                    <i class="fas fa-external-link-alt ms-1 small"></i>
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .content-display {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        min-height: 200px;
    }
    
    .content-display img {
        max-width: 100%;
        height: auto;
    }
    
    .content-display table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }
    
    .content-display table, .content-display th, .content-display td {
        border: 1px solid #dee2e6;
    }
    
    .content-display th, .content-display td {
        padding: 0.75rem;
    }
</style>
@endpush
