<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MenuItem extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'title_en',
        'url',
        'route_name',
        'target',
        'parent_id',
        'order',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * This method was removed as we no longer have a menu relationship
     */

    /**
     * Get the parent menu item.
     */
    public function parent()
    {
        return $this->belongsTo(MenuItem::class, 'parent_id');
    }

    /**
     * Get the children menu items.
     */
    public function children()
    {
        return $this->hasMany(MenuItem::class, 'parent_id')
            ->orderBy('order', 'asc');
    }

    /**
     * Get the active children menu items.
     */
    public function activeChildren()
    {
        try {
            return $this->hasMany(MenuItem::class, 'parent_id')
                ->where('is_active', true)
                ->orderBy('order', 'asc');
        } catch (\Exception $e) {
            // For debugging
            echo "<!-- Error in activeChildren: {$e->getMessage()} -->";
            return $this->hasMany(MenuItem::class, 'parent_id');
        }
    }

    /**
     * Scope a query to only include active menu items.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        try {
            return $query->where('is_active', true);
        } catch (\Exception $e) {
            // For debugging
            echo "<!-- Error in scopeActive: {$e->getMessage()} -->";
            return $query;
        }
    }

    /**
     * Get the URL for the menu item.
     *
     * @return string
     */
    public function getUrlAttribute($value)
    {
        try {
            if ($this->route_name) {
                try {
                    return route($this->route_name);
                } catch (\Exception $e) {
                    \Log::warning("Route not found for route_name: {$this->route_name}. Using fallback URL.");
                    return $value ?: '#';
                }
            }

            return $value ?: '#';
        } catch (\Exception $e) {
            // For debugging
            \Log::error("Error in getUrlAttribute for item ID {$this->id}: {$e->getMessage()}");
            return '#';
        }
    }

    /**
     * Determine if the menu item has children.
     *
     * @return bool
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * Determine if the menu item has active children.
     *
     * @return bool
     */
    public function hasActiveChildren()
    {
        try {
            return $this->activeChildren()->count() > 0;
        } catch (\Exception $e) {
            // For debugging
            echo "<!-- Error in hasActiveChildren for item ID {$this->id}: {$e->getMessage()} -->";
            return false;
        }
    }

    /**
     * Determine if the menu item has active grandchildren (sub-submenu).
     *
     * @return bool
     */
    public function hasActiveGrandchildren()
    {
        try {
            foreach ($this->activeChildren as $child) {
                if ($child->hasActiveChildren()) {
                    return true;
                }
            }
            return false;
        } catch (\Exception $e) {
            // For debugging
            echo "<!-- Error in hasActiveGrandchildren for item ID {$this->id}: {$e->getMessage()} -->";
            return false;
        }
    }
}
